<?php namespace App\Http\Controllers;

use App\Location;
use App\Services\Validation\ValidationException;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Facades\View;

class LocationsController extends Controller {
    // TODO: move to Hodor, It is an admin only controller

	/**
	 * Display a listing of the resource.
	 *
	 *
	 */
	public function index()
	{
		// fetch all locations
		$this->view_data['locations'] = Location::all();

		$this->view_data['content_group_selected'] = 'current';
        $this->view_data['locations_selected'] = 'current';

	    return View::make('admin.locations.index', $this->view_data);
	}

	/**
	 * Show the form for creating a new resource.
	 *
	 *
	 */
	public function create()
	{
        $this->view_data['content_group_selected'] = 'current';
        $this->view_data['add_location_selected'] = 'current';

		return View::make('admin.locations.create', $this->view_data);
	}

	/**
	 * Store a newly created resource in storage.
	 *
	 *
	 */
	public function store(Request $request)
	{
		try
		{
			$location = Location::create($request->all());

			foreach (Config::get('translationLocales') as $locale){
				$location->translateOrNew($locale)->name = $request->get('name_' . $locale);
				$location->translateOrNew($locale)->description = $request->get('description_' . $locale);
				$location->translateOrNew($locale)->pickup_description = $request->get('pickup_description_' . $locale);
				$location->translateOrNew($locale)->dropoff_description = $request->get('dropoff_description_' . $locale);
			}
			$location->save();

			// return to the locations list
			return Redirect::route('admin.locations.index', $location->id)->with('success', 'Location ' . $location->id . ' created');
		}
		catch(ValidationException $e)
		{
			return Redirect::back()->withInput()->withErrors($e->getErrors());
		}
	}


	/**
	 * Show the form for editing the specified resource.
	 *
	 * @param  int  $id
	 *
	 */
	public function edit($id)
	{
		// check the existence of the given id
		$location = Location::findOrFail($id);
		$this->view_data['locations_selected'] = 'current';

		return View::make('admin.locations.edit', $this->view_data)->with('location', $location);
	}


	/**
	 * Update the specified resource in storage.
	 *
	 * @param  int  $id
	 *
	 */
	public function update(Request $request, $id)
	{
		// check the existence of the given id
		$location = Location::findOrFail($id);

		try
		{
			foreach (Config::get('translationLocales') as $locale){
				$location->translateOrNew($locale)->name = $request->get('name_' . $locale);
				$location->translateOrNew($locale)->description = $request->get('description_' . $locale);
                $location->translateOrNew($locale)->pickup_description = $request->get('pickup_description_' . $locale);
                $location->translateOrNew($locale)->dropoff_description = $request->get('dropoff_description_' . $locale);
			}
			$location->validateAndUpdate($request->all());

			// return to the edit location screen
			return Redirect::route('admin.locations.edit', $location->id)->with('success', 'Location ' . $location->id . ' saved');
		}
		catch(ValidationException $e)
		{
			return Redirect::back()->withInput()->withErrors($e->getErrors());
		}
	}

	/**
	 * Remove the specified resource from storage.
	 *
	 * @param  int  $id
	 *
	 */
	public function destroy($id)
	{
		// check the existence of the given id
		$location = Location::findOrFail($id);

		// proceed with deleting the location row
		$location->delete();

		// return to the locations list screen
		return Redirect::route('admin.locations.index')->with('success', 'Location '. $id . ' deleted');
	}
}
