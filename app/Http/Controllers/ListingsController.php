<?php namespace App\Http\Controllers;

use App\Accessory;
use App\Coupon;
use App\Group;
use App\Listing;
use App\Country;
use App\Repositories\Contracts\ListingRepositoryInterface;
use App\Services\Offer\StaticDiscount;
use App\Services\Offer\CouponDiscount;
use App\Services\Offer\DateRange;
use App\Supergroup;
use Illuminate\Support\Facades\Lang;
use Illuminate\Support\Facades\Request;
use Illuminate\Support\Facades\Response;
use Illuminate\Support\Facades\View;
use Illuminate\Support\Arr;
use Spatie\SchemaOrg\Schema;

class ListingsController extends Controller {

	private $listingRepo;

	public function __construct(ListingRepositoryInterface $listingRepo) {
		$this->listingRepo = $listingRepo;
		parent::__construct();

        // canonical url initialization
		$this->view_data['canonical_url'] = Request::url();
	}

	/**
	 * Display a listing of the resource.
	 *
	 */
	public function index(\Illuminate\Http\Request $request)
	{
        // if null, set pickup location session var to default location id
        if(!Arr::get($_COOKIE, 'pickup_location')){
            //set as session value the first key of the locations_dropdown_pickup array
            reset($this->view_data['locations_dropdown_pickup']);
            setcookie('pickup_location', key($this->view_data['locations_dropdown_pickup']), 0, '/');
        }

        // grab querystrings
        // groups
        $groups     = $request->get('group');
        $supergroup = $request->get('group');
        // view variable used for dropdown preselection
        $this->view_data['group_filter'] = $supergroup;

        $supergroup = Supergroup::where('slug',$supergroup)->first();

        // transmission filter
        $transmission_filter = $request->get('transmission', false);
        $this->view_data['transmission_filter'] = $transmission_filter;
        // fuel filter
        $fuel_filter = $request->get('fuel', false);
        $this->view_data['fuel_filter'] = $fuel_filter;
        // seats filter
        $seats_filter = $request->get('seats', false);
        $this->view_data['seats_filter'] = $seats_filter;

        $this->view_data['group'] = $groups;
        // string to insert in the heading
        $group_description = '';

        // initialize temporary listing collections
        $listings_by_all            = '';
        $listings_by_groups         = collect();
        $listings_by_transmission   = collect();
        $listings_by_fuel           = collect();
        $listings_by_seats          = collect();

        // default search text
        $this->view_data['sidebar_text'] = Lang::get('listings.sidebar_text_default');
        // flag to govern whether we are seing the "all groups" page or a specific-group page
        $this->view_data['with_group']          = false;

        // page meta title, meta description and heading
        $this->view_data['page_title']          = trans('listings.index_vanilla_page_title');
        $this->view_data['page_heading']        = trans('listings.index_vanilla_page_heading');
        $this->view_data['page_description']    = trans('listings.index_vanilla_page_description');

        if (!empty($supergroup))
        {
            // strtoupper the string for correct db query matching
//            $groups_array = explode('_', strtoupper($groups));

            // get groups from supergroups
            $groups_array = $supergroup->groups->pluck('id')->toArray();

//            dd($groups_array);

            $this->view_data['listings'] = Listing::whereIn('group_id', $groups_array)
                ->publishedEurodollar()
                ->orderBy(Group::select('group_order')->whereColumn('groups.id', 'listings.group_id'), 'asc')
                ->get();

            // temporary collection to intersect for final results
            $listings_by_groups = $this->view_data['listings'];
            $listings_by_all = $listings_by_groups;

            // string to insert in the heading
            $group = Group::find($groups_array[0]);

            // set url_variables
            $this->view_data['url_variables'] = '?' . http_build_query(['group' => $request->get('group')]);

            $group_description = '';
            if ( ! empty($group) )
            {
                $group_description = $group->description;

                // customize sidebar text based on given car group
                $this->view_data['sidebar_text'] = $group->seo_text;

                // flag to govern whether we are seing the "all groups" page or a specific-group page
                $this->view_data['with_group']          = true;
                $this->view_data['group_description']   = $group->fullNameSEO;

                // page meta title, meta description and heading
                $this->view_data['page_title']          = sprintf(trans('listings.index_group_page_title'), $group_description);
                $this->view_data['page_heading']        = sprintf(trans('listings.index_group_page_heading'), $group_description);
                $this->view_data['page_description']    = sprintf(trans('listings.index_group_page_description'), $group_description);
            }
        }
        else
        {
            // fetch all listings
            $this->view_data['listings'] = $this->listingRepo->findListings('published_'.config('appportal.name'),'1',array('group_id' => 'ASC', 'order'=>'DESC'));
            $listings_by_groups = $this->view_data['listings'];
            $listings_by_all = $listings_by_groups;
        }

        // check for other query params (search filters) that might affect the returned results
        // transmission
        if(!empty($transmission_filter))
        {
            $this->view_data['listings'] = $this->listingRepo->findByTransmission($transmission_filter, config('appportal.name'));
            $listings_by_transmission = $this->view_data['listings'];
            // this part combines the results from previous queries, effectively providing combination of search filters
            $listings_by_all = $listings_by_all->intersect($listings_by_transmission);
        }

        // fuel
        if(!empty($fuel_filter))
        {
            $this->view_data['listings'] = $this->listingRepo->findByFuel($fuel_filter, config('appportal.name'));
            $listings_by_fuel = $this->view_data['listings'];
            // this part combines the results from previous queries, effectively providing combination of search filters
            $listings_by_all = $listings_by_all->intersect($listings_by_fuel);
        }

        // seats
        if(!empty($seats_filter))
        {
            $this->view_data['listings'] = $this->listingRepo->findBySeats($seats_filter, config('appportal.name'));
            $listings_by_seats = $this->view_data['listings'];
            // this part combines the results from previous queries, effectively providing combination of search filters
            $listings_by_all = $listings_by_all->intersect($listings_by_seats);
        }

        // populate the final version of listings list
        $this->view_data['listings'] = $listings_by_all;

        $this->view_data['car_list_selected'] = 'current';

        // groups list
        $this->view_data['group_list'] = array('all' => Lang::get('forms.all_categories')) + $this->group_data->pluck('fullName', 'id')->all();
        // transmission types list
        $this->view_data['transmission_list'] = array(
            'all'       => trans('common.all'),
            'automatic' => trans('common.automatic_transmission'),
            'manual' => trans('common.manual_transmission'),
        );
        // fuel types list
        $this->view_data['fuel_list'] = array(
            'all'       => trans('common.all'),
            'petrol'    => trans('common.petrol'),
            'diesel'    => trans('common.diesel'),
        );

        // get date range
        $pickup_date_time = Arr::get($_COOKIE, 'pickup_date');
        if(!empty($pickup_date_time)){
            $pickup_date_time .= ' ' . $this->view_data['pickup_time'];
        }
        $dropoff_date_time = Arr::get($_COOKIE, 'dropoff_date');
        if(!empty($dropoff_date_time)){
            $dropoff_date_time .= ' ' . $this->view_data['dropoff_time'];
        }
        $this->view_data['date_range'] = new DateRange($pickup_date_time, $dropoff_date_time);


        // fetch all valid coupons (enabled, promoted and belonging to eurodollar site)
        $coupons = Coupon::eurodollarPromoted()->get();

        $this->view_data['coupons'] = $coupons;

//            $this->view_data['discount'] = new StaticDiscount($this->view_data['date_range']->getTotalDays(), $this->view_data['date_range']->getDaysIndex(), );

        // handle the dropoff location for offer price calculation which takes place in the view (case cookie value is empty or not set)
        $this->view_data['dropoff_location_for_offer_calculation'] = !empty(Arr::get($_COOKIE, 'dropoff_location')) ?
            Arr::get($_COOKIE, 'dropoff_location') :
            Arr::get($_COOKIE, 'pickup_location', 1);

//            dd($this->view_data['group_list']);

        // json-ld microdata script (echoed in view)
        $this->view_data['breadcrumbs_schema'] = Schema::breadcrumbList()
            ->itemListElement(
                array(
                    Schema::listItem()
                        ->position(1)
                        ->item(array(
                            '@id' => route('home'),
                            'name' => trans('breadcrumbs.home'),
                            )
                        ),
                    Schema::listItem()
                        ->position(2)
                        ->item(array(
                                '@id' => route('listings.index'),
                                'name' => trans('breadcrumbs.cars'),
                            )
                        ),
                )
            );

        // initialize groups var
        $this->view_data['groups'] = array();

        // populate the groups array
        foreach($this->view_data['listings'] as $listing)
        {
            $this->view_data['groups'][$listing->group->id][] = $listing;
        }

        // open graph
        $this->overwriteOpengraph(
            $this->view_data['page_title'],
            $this->view_data['page_description']
        );

        return View::make('frontend.listings.index', $this->view_data);
	}


	/**
	 * Display the specified resource.
	 *
	 * @param  int  $id
	 */
	public function show($slug)
	{
		// check the existence of the given slug
		$listing = Listing::where('slug', $slug)->first();

        // no listing was found
        if(!$listing)
        {
            // check if listing is soft deleted
            // query for slug with trashed results
            $listing = Listing::where('slug', $slug)->withTrashed()->first();

            // fix the calling of trashed in null
            if(!$listing)
            {
                // listing was not found nor trashed
                abort(404);
            }

            if($listing->trashed())
            {
                return $this->handleUnavailable($listing);
            }

            // listing was not found nor trashed
            // keeping it here redundantly to throw erro in case the ifs are bypassed
            abort(404);
        }

        // check if listing is unpublished
        if(!$listing->published_eurodollar)
        {
            return $this->handleUnavailable($listing);
        }

		$this->view_data['listing'] = $listing;

        // get date range
        $pickup_date_time = Arr::get($_COOKIE, 'pickup_date');
        if(!empty($pickup_date_time)){
        	$pickup_date_time .= ' ' . Arr::get($_COOKIE, 'pickup_time', '12:00');
        }
        $dropoff_date_time = Arr::get($_COOKIE, 'dropoff_date');
        if(!empty($dropoff_date_time)){
        	$dropoff_date_time .= ' ' . Arr::get($_COOKIE, 'dropoff_time', '12:00');
        }
        $date_range = new DateRange($pickup_date_time, $dropoff_date_time);

        // final "featured" containing both related and suggested
        $this->view_data['featuredListings']    = $listing->similar();

        // related posts
        $this->view_data['related_posts']       = $listing->presentablePosts;

		// discount
        $discount = new StaticDiscount($date_range->getTotalDays(), $date_range->getDaysIndex(), $listing);

		// get offer
        // handle the dropoff location (case cookie value is empty or not set)
        $dropoff = !empty(Arr::get($_COOKIE, 'dropoff_location')) ?
            Arr::get($_COOKIE, 'dropoff_location') :
            Arr::get($_COOKIE, 'pickup_location', 1);
		$offer = $listing->getOffer(
            $date_range,
            $discount,
            Arr::get($_COOKIE, 'pickup_location', 1),
            $dropoff
        );

        $this->view_data['offer'] = $offer;
        $this->view_data['date_range'] = $date_range;

		// get all accessories
		$this->view_data['accessories'] = Accessory::all();

		// get all countries
		$this->view_data['countries'] = Country::all()->pluck('name', 'id')->toArray();
		asort($this->view_data['countries']);
		$this->view_data['countries'] = array(Lang::get('forms.select_country')) + $this->view_data['countries'];

		$this->view_data['references'] = [
		    null            => 'Choose one...',
		    'search'        => 'Search engines',
            'banner'        => 'Marketing banners',
            'newsletter'    => 'Newsletter',
            'mouth'         => 'Word of mouth',
            'brochure'      => 'Printed brochure',
            'returning'     => 'I am a returning client',
            'other'         => 'Other',
        ];

		$this->view_data['people_counts'] = [
		    null    => 'Choose one...',
		    1       => '1',
            2       => '2',
            3       => '3',
            4       => '4',
            5       => '5',
            6       => '6',
            7       => '7',
            8       => '8',
            9       => '9',
        ];

        // set the timepicker preselected values
        $this->view_data['pickup_time']  = Arr::get($_COOKIE, 'pickup_time',  $this->default_timepicker_value);
        $this->view_data['dropoff_time'] = Arr::get($_COOKIE, 'dropoff_time', $this->default_timepicker_value);

        // listing group
        $this->view_data['listing_group'] = $listing->group;

        // next and previous listings
        $previous_listing = Listing::publishedEurodollar()
            ->where('group_id', '=', $listing->group_id)
            ->where('id', '<', $listing->id)
            ->orderBy('id', 'desc')
            ->first();
        // we have the first listing of the group as current
        // so the previous one is the last
        if($previous_listing === null)
        {
            $previous_listing = Listing::publishedEurodollar()
                ->where('group_id', '=', $listing->group_id)
                ->orderby('id', 'desc')
                ->first();
        }
        $next_listing = Listing::publishedEurodollar()
            ->where('group_id', '=', $listing->group_id)
            ->where('id', '>', $listing->id)
            ->orderBy('id', 'asc')
            ->first();
        // we have the last listing of the group as current
        // sp the next one is the first
        if($next_listing === null)
        {
            $next_listing = Listing::publishedEurodollar()
                ->where('group_id', '=', $listing->group_id)
                ->orderby('id', 'asc')
                ->first();
        }

        $this->view_data['previous_listing_url'] = route('listings.show', $previous_listing->slug);
        $this->view_data['next_listing_url'] = route('listings.show', $next_listing->slug);

//        dd($this->view_data);

        // fuel plan
        $this->view_data['fuel_plan_options'] = [0 => Lang::get('listings.fuel_plan_option_0'), 1 => Lang::get('listings.fuel_plan_option_1') . " (+&euro;{$listing->group->fuel_plan})"];

        // image - photo
        $this->view_data['main_photo']              = $listing->getFirstMedia('photos');
        $this->view_data['main_photo_attributes']   = empty($this->view_data['main_photo']) ? array() : $this->view_data['main_photo']->getSeoAttributes();

        // seo stuff
        // page title
        // in case we haz custom meta title fer this language, we use this one
        if ( !empty($listing->meta_title) )
        {
            $this->view_data['page_title']      = $listing->meta_title;
        }
        else
        {
		    $this->view_data['page_title']      = sprintf(Lang::get('listings.show_meta_title'), $listing->SEOTitle, $listing->group->base_price);
        }
        // meta description
        if ( !empty($listing->meta_description) )
        {
            $this->view_data['meta_description'] = $listing->meta_description;
        }
        else
        {
            $this->view_data['meta_description'] = sprintf(Lang::get('listings.show_meta_description'), $listing->SEOTitle);
        }

        // canonical url is set on constructor level

        // json-ld microdata script (echoed in view)
        $this->view_data['breadcrumbs_schema'] = Schema::breadcrumbList()
            ->itemListElement(
                array(
                    Schema::listItem()
                        ->position(1)
                        ->item(array(
                                '@id' => route('home'),
                                'name' => trans('breadcrumbs.home'),
                            )
                        ),
                    Schema::listItem()
                        ->position(2)
                        ->item(array(
                                '@id' => route('listings.index'),
                                'name' => trans('breadcrumbs.cars'),
                            )
                        ),
                    Schema::listItem()
                        ->position(3)
                        ->item(array(
                                '@id' => $this->view_data['canonical_url'],
                                'name' => $listing->SEOTitle,
                            )
                        ),
                )
            );

        // record a view fer teh listing
        views($listing)->record();

        // open graph
        $this->overwriteOpengraph(
            $this->view_data['page_title'],
            $this->view_data['meta_description'],
            $this->view_data['main_photo'] ? $this->view_data['main_photo']->getFullUrl() : null
        );

		return View::make('frontend.listings.show', $this->view_data);
	}


	/**
	 * Calculate booking price
	 *
	 */
	public function getPrice(\Illuminate\Http\Request $request)
	{
		$listing = $this->listingRepo->findOrFail($request->get('listing_id'));

		// get date range
		$pickup_date_time = $request->get('pickup_date');
		if(!empty($pickup_date_time)){
			$pickup_date_time .= ' ' . (empty($request->get('pickup_time')) ? '12:00' : $request->get('pickup_time'));
		}
		$dropoff_date_time = $request->get('dropoff_date');
		if(!empty($dropoff_date_time)){
			$dropoff_date_time .= ' ' . (empty($request->get('dropoff_time')) ? '12:00' : $request->get('dropoff_time'));
		}
        $date_range = new DateRange($pickup_date_time, $dropoff_date_time);

        $discount = new StaticDiscount($date_range->getTotalDays(), $date_range->getDaysIndex(), $listing);
        // check whether we have coupon discount
        if ( $request->has('discount_coupon') )
        {
            $discount = new CouponDiscount($discount, $request->get('discount_coupon'));
        }

        // fix for empty dropoff location
        // the previous code was never taking into account the "Same as pickup" selection in the dropdown
        if ($request->get('dropoff_location') == '')
        {
            $dropoff_location = $request->get('pickup_location', 1);
        }
        else
        {
            $dropoff_location = $request->get('dropoff_location', 1);
        }

		// get accessories if present
		$accessories = $request->get('accessory', []);
		$offer = $listing->getOffer(
            $date_range,
            $discount,
            $request->get('pickup_location', 1),
            $dropoff_location,
            $accessories,
            ($request->get('fuel_plan_value') === 1 || $request->get('fuel_plan_value') === '1')
        );

		$data['total_price_initial'] = $offer->getBasePrice();
		$data['extra_miles_charge'] = $offer->getExtraMilesCharge();
		$data['remote_location_charge'] = $offer->getRemoteLocationCharge();
		$data['after_hours_charge'] = $offer->getAfterHoursCharge();
		$data['fuel_plan_charge'] = $offer->getFuelPlanCharge();

		$data['final_price'] = $offer->getTotalDiscountPrice();
		$data['total_days'] = $date_range->getTotalDays();

        // check availability of listing
        $data['available'] = $offer->hasAvailability() ? 1 : 0;

        // check previous availability VS new availability
        // so as to decide whether to refresh the page or not
        $data['refresh'] = false;
        if ( ! empty($request->get('current_availability')) && $request->get('current_availability') != $data['available'] )
        {
            $data['refresh'] = true;
        }

		foreach($accessories as $accessory_id => $counter)
		{
			$data['accessories'][$accessory_id]['price'] = $offer->getAccessoryPrice($accessory_id);
			$data['accessories'][$accessory_id]['count'] = $counter;
		}

		return Response::json($data);
	}

    private function handleUnavailable(Listing $listing)
    {
        $this->view_data['listing'] = $listing;
        $this->view_data['featuredListings'] = $listing->similar();

        return view('frontend.listings.unavailable_show', $this->view_data);
    }

}
