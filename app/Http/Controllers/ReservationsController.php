<?php namespace App\Http\Controllers;

use App\Listing;
use App\Reservation;
use App\Site;
use App\ReservationToAccessory;
use App\Country;
use App\Services\Validation\ValidationException;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Facades\Response;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\View;
use App\Http\Requests\ReservationRequest;

class ReservationsController extends Controller {

	/**
	 * Display a listing of the resource.
	 *
	 *
	 */
	public function index(Request $request)
	{
		// Check search filters
		$searchAttributes = array('id', 'show', 'status', 'after_hours_charge', 'fuel_plan_charge', 'extra_day_charge', 'extra_miles_charge', 'pickup_location', 'dropoff_location', 'listing_model', 'listing_group');
		$where = [];
		foreach($searchAttributes as $searchAttribute){
        	$this->view_data['searchAttributes_' . $searchAttribute] = $request->get($searchAttribute);
        	if(!empty($this->view_data['searchAttributes_' . $searchAttribute]) || $this->view_data['searchAttributes_' . $searchAttribute] === '0'){
        		$where['reservations.' . $searchAttribute] = $this->view_data['searchAttributes_' . $searchAttribute];
        	}
		}

        // site
        $this->view_data['reservations_site_dropdown_options'] = Site::all()->pluck('name','id')->toArray();
        // site selected - set eurodollar if site is not set, or validate if forms invoked
        $this->view_data['siteSelected'] = $request->get('site');
        if(!in_array($this->view_data['siteSelected'],array_keys($this->view_data['reservations_site_dropdown_options'])))
        {
            $this->view_data['siteSelected'] = key($this->view_data['reservations_site_dropdown_options']);
        }
        //add site to where array (so we'll always have a 'where' statement and maybe a whereRaw stament)
        $where['reservations.site'] = $this->view_data['siteSelected'];

		// Country
		$this->view_data['searchAttributes_country'] = $request->get('country');
        if(!empty($this->view_data['searchAttributes_country'])){
        	$where['countries.id'] = $this->view_data['searchAttributes_country'];
        }

        // Discount coupon
		$whereRaw = [];
		$this->view_data['searchAttributes_discount_coupon'] = $request->get('discount_coupon');
		if($this->view_data['searchAttributes_discount_coupon'] === '1'){
			$whereRaw[] = '(discount_coupon IS NOT NULL && discount_coupon != "")';
		}
		elseif($this->view_data['searchAttributes_discount_coupon'] === '0'){
			$whereRaw[] = '(discount_coupon IS NULL || discount_coupon = "")';
		}

        // Check range filters
        $rangeAttributes = array('price' => 'final_price', 'pickup_date' => 'pickup_date', 'dropoff_date' => 'dropoff_date', 'created_at' => 'created_at');
		foreach($rangeAttributes as $rangeAttribute => $rangeColumn){
			$rangeAttributeLow = $rangeAttribute . '_low';
			$rangeAttributeHigh = $rangeAttribute . '_high';
        	$this->view_data['searchAttributes_' . $rangeAttributeLow] = $request->get($rangeAttributeLow);
        	$this->view_data['searchAttributes_' . $rangeAttributeHigh] = $request->get($rangeAttributeHigh);
        	if(!empty($this->view_data['searchAttributes_' . $rangeAttributeLow])){
        		$whereRaw[] = "reservations.{$rangeColumn} >= '{$this->view_data['searchAttributes_' . $rangeAttributeLow]}'";
        	}
        	if(!empty($this->view_data['searchAttributes_' . $rangeAttributeHigh])){
        		$whereRaw[] = "reservations.{$rangeColumn} <= '{$this->view_data['searchAttributes_' . $rangeAttributeHigh]}'";
        	}
		}
		$whereRaw = implode(' && ', $whereRaw);

		// Check google it
		$this->view_data['searchAttributes_google_it'] = $request->get('google_it');

		// Fetch Reservation specific data - models, groups and countries
        $listing_model = array_column(collect(DB::table('reservations')->select('listing_model')->groupBy('listing_model')->orderBy('listing_model')->get())->map(function($x){ return (array) $x; })->toArray(), 'listing_model');
        $listing_group = array_column(collect(DB::table('reservations')->select('listing_group')->groupBy('listing_group')->orderBy('listing_group')->get())->map(function($x){ return (array) $x; })->toArray(), 'listing_group');
        $this->view_data['reservations_listing_model'] = array_combine($listing_model, $listing_model);
        $this->view_data['reservations_listing_group'] = array_combine($listing_group, $listing_group);

        $country_ids = array_column(collect(DB::table('reservations')->select('customers.country_id')->join('customers', 'reservations.customer_id', '=', 'customers.id')->groupBy('customers.country_id')->get())->map(function($x){ return (array) $x; })->toArray(), 'country_id');
        $this->view_data['reservations_countries'] = Country::whereIn('id', $country_ids)->get()->pluck('name', 'id')->toArray();


		// Google it form submit
        if($request->get('g_form'))
        {
	        if(!empty($where)) // $where param should be filled only with site option
	        {
	            $this->view_data['reservations'] =
	            	Reservation::select('reservations.*')
	            		->join('customers', 'reservations.customer_id', '=', 'customers.id')
                        ->where(function($query){
                            $query->orWhere('customers.name', 'like', "%" . $this->view_data['searchAttributes_google_it'] . "%");
                            $query->orWhere('customers.email', 'like', "%" . $this->view_data['searchAttributes_google_it'] . "%");
                            $query->orWhere('customers.telephone', 'like', "%" . $this->view_data['searchAttributes_google_it'] . "%");
                        })
                        ->where($where)
	            		->orderBy('reservations.id', 'DESC')
	            		->paginate(20);
	        }
        }
		// Search form submit
        elseif($request->get('s_form'))
        {
	        if(!empty($whereRaw))
	        {
	            $this->view_data['reservations'] =
		            Reservation::select('reservations.*')
			            ->join('customers', 'reservations.customer_id', '=', 'customers.id')
			            ->join('countries', 'customers.country_id', '=', 'countries.id')
			            ->where($where)
			            ->whereRaw($whereRaw)
			            ->orderBy('reservations.id', 'DESC')
			            ->paginate(20);
	        }
	        else
	        {
	            $this->view_data['reservations'] =
		            Reservation::select('reservations.*')
			            ->join('customers', 'reservations.customer_id', '=', 'customers.id')
			            ->join('countries', 'customers.country_id', '=', 'countries.id')
			            ->where($where)
			            ->orderBy('reservations.id', 'DESC')
			            ->paginate(20);
	        }
        }
        if(!isset($this->view_data['reservations'])){
            // no forms invoked
        	$this->view_data['reservations'] =
        		Reservation::select('reservations.*')
                    ->where('site', $this->view_data['siteSelected']) //site id
        			->orderBy('id', 'DESC')
        			->paginate(20);

        }

        $this->view_data['reservations_group_selected'] = 'current';
        $this->view_data['reservations_selected'] = 'current';

		return View::make('admin.reservations.index', $this->view_data);
	}


	/**
	 * Show the form for creating a new resource.
	 *
	 *
	 */
	public function create()
	{
		//
	}


	/**
	 * Store a newly created resource in storage.
	 *
	 *
	 */
	public function store(ReservationRequest $request)
	{
        // log the submission of reservation form (after validation)
        Log::info('Reservation form validated. Payload ' . json_encode($request->all()));

		try
		{
			$reservation = Reservation::create($request->all());

            // log the creation of reservation
            Log::info('Reservation ' . $reservation->id . ' created');

			// send email messages
			if (!empty($reservation))
			{
                $listing = Listing::find($reservation->listing_id);
                $customer = $reservation->getCustomer();
				// ...to client
				Mail::send('emails.reservation_client_revamp', ['reservation' => $reservation, 'listing' => $listing, 'customer' => $customer], function($message) use ($reservation, $customer)
				{
					$message->to($customer->email, $customer->name)
						->subject('Eurodollar: Reservation Confirmation #' . $reservation->id);
				});
				// Change local to en
				$currentLocale = App::getLocale();
				App::setLocale('en');

				// ...and to admin
                // all rest admins with default mail setup
                Mail::send('emails.reservation_admin', ['reservation' => $reservation, 'listing' => $listing, 'customer' => $customer], function($message) use ($reservation)
                {
                    $message->to(config('mail_addresses.argiry'), 'Argiry Fragkaki')
                        ->cc([config('mail_addresses.kiritsis'), '<EMAIL>'])
                        ->subject('New reservation #' . $reservation->id);
                });

				Mail::send('emails.reservation_admin', ['reservation' => $reservation, 'listing' => $listing, 'customer' => $customer], function($message) use ($reservation)
				{
					$message->to(config('mail_addresses.info_ed'), 'Rentcars-crete Admins')
						->subject('New reservation #' . $reservation->id);
				});

				 //Reset local
				App::setLocale($currentLocale);
			}

			// flush the session variables
			setcookie('pickup_location' , '' , time() - 3600, '/');
			setcookie('pickup_date' , '' , time() - 3600, '/');
			setcookie('pickup_time' , '' , time() - 3600, '/');
			setcookie('dropoff_location' , '' , time() - 3600, '/');
			setcookie('dropoff_date' , '' , time() - 3600, '/');
			setcookie('dropoff_time' , '' , time() - 3600, '/');
			Session::put('reservation_confirmation', $reservation->id);

			// return Response::json(['status' => 'success', 'msg' => 'Reservation saved', 'reservationId' => $reservation->id]);

			return redirect()->route('reservation.confirm', $reservation->id)->with('success', 'Reservation saved');
		}
		catch(ValidationException $e)
		{
			// return Response::json(['status' => 'error', 'msg' => $e->getErrors()]);
			return redirect()->back()->withFragment('#customer-fields')
			->withErrors($e->getErrors())  // Add validation errors to the session
			->withInput();  // Keep the old input
		}
	}


	/**
	 * Display the specified resource.
	 *
	 * @param  int  $id
	 *
	 */
	public function show($id)
	{
		$reservation = Reservation::findOrFail($id);

		$this->view_data['reservation']             = $reservation;
		$this->view_data['customer']                = $reservation->getCustomer();
		$this->view_data['reservations_group_selected'] = 'current';
        $this->view_data['reservations_selected']   = 'current';
        $this->view_data['reservations_site']       = ucfirst(Site::find($reservation->site)->name);

		return Response::view('admin.reservations.show', $this->view_data);
	}


	/**
	 * Show the form for editing the specified resource.
	 *
	 * @param  int  $id
	 *
	 */
	public function edit($id)
	{
		//
	}


	/**
	 * Update the specified resource in storage.
	 *
	 * @param  int  $id
	 *
	 */
	public function update(Request $request, $id)
	{
		$reservation = Reservation::findOrFail($id);

		$updateAttribute = 'status';
		if($request->get('show')){
			$updateAttribute = 'show';
		}
        $reservation->$updateAttribute = $request->get($updateAttribute);
        $reservation->save();

        return Response::json(['status' => 'success', 'msg' => $updateAttribute . ' updated']);
	}


	/**
	 * Remove the specified resource from storage.
	 *
	 * @param  int  $id
	 *
	 */
	public function destroy($id)
	{
		// check the existence of the given id
		$reservation = Reservation::findOrFail($id);

		// proceed with deleting the reservation row
		$reservation->delete();

		// proceed with deleting all reservation to accessory rows
		$affectedRows = ReservationToAccessory::where('reservation_id', '=', $id)->delete();

        Log::info('Reservation with id: ' . $id . ' was deleted');

		// return to the reservation list screen
        return redirect()->back()->with('success', 'Reservation '. $id . ' deleted');
	}

	/**
	 * Confirmation reservation page
	 */
	public function confirm($reservation_id)
	{
		$sess_value = Session::get('reservation_confirmation');

		if($sess_value == $reservation_id)
		{
			$reservation = Reservation::findOrFail($reservation_id);
			$this->view_data['reservation'] = $reservation;
			$this->view_data['customer'] = $reservation->getCustomer();

			Session::forget('reservation_confirmation');

			return View::make('frontend.reservation', $this->view_data);

		}
		else
		{
			return Redirect::to('/');
		}
	}

	/**
	 * Verify reservation page
	 */
	public function verify($reservation_uuid, $resolution)
	{
        // fetch reservation
        $reservation = Reservation::where('uuid', $reservation_uuid)
            ->firstOrFail();

        $this->view_data['reservation'] = $reservation;
        $this->view_data['customer'] = $reservation->getCustomer();

        // initialize result
        $this->view_data['result'] = '';

        // if the reservation is already verified do nothing apart from inform the user
        if($reservation->show_verified)
        {
            $this->view_data['result'] = 'already_verified';

            return View::make('frontend.reservation_verify', $this->view_data);
        }

        // if we have reached thus far, we have found the reservation and it is unverified
        if($resolution == 'no_show')
        {
            $reservation->show          = 'no show';
            $reservation->show_verified = true;

            $reservation->save();

            Log::info('Reservation with id: ' . $reservation->id . ' was made no_show and verified through email link');

            $this->view_data['result'] = 'made_no_show';
        }
        elseif($resolution == 'show')
        {
            $reservation->show          = 'show';
            $reservation->show_verified = true;

            $reservation->save();

            Log::info('Reservation with id: ' . $reservation->id . ' was made show and verified through email link');

            $this->view_data['result'] = 'made_show';
        }

        return View::make('frontend.reservation_verify', $this->view_data);
	}


}
