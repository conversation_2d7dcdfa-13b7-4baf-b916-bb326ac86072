<?php

namespace App\Traits\AccessorsMutators;

use App\Customer;
use App\Listing;
use App\Location;
use DateTime;

trait RepeatingClientAccessorsMutators
{

    /**
     * Get the title of the listing for this repeating client
     *
     * @return mixed
     */
    public function getListingTitle()
    {
        return empty($this->listing_id) ? '' : Listing::find($this->listing_id)->title;
    }

    /**
     * Get the name of the pickup location
     *
     * @return mixed
     */
    public function getPickupLocation()
    {
        return empty($this->pickup_location) ? '' : Location::find($this->pickup_location)->name;
    }

    /**
     * Get the name of the dropoff location
     *
     * @return mixed
     */
    public function getDropoffLocation()
    {
        return empty($this->dropoff_location) ? '' : Location::find($this->dropoff_location)->name;
    }

    /**
     * Includes the custom model attributes to the list of the attributes that are automatically mutated as dates
     *
     * @return mixed
     */
    public function getDates()
    {
        // get the dates as defined by the parent method
        $dates = parent::getDates();
        // inject the custom reservation attributes that need to be handled as dates
        array_push($dates, 'pickup_date', 'dropoff_date');

        return $dates;
    }

    /**
     * Stores the attribute in the correct format in the DB (Y-m-d)
     * @param $value
     */
    public function setPickupDateAttribute($value)
    {
        if(empty($value)){
            unset($this->attributes['pickup_date']);
            return;
        }
        $value = str_replace('/','-',$value);
        $dt = new DateTime($value);
        $this->attributes['pickup_date'] = $dt->format('Y-m-d');
    }

    /**
     * Stores the attribute in the correct format in the DB (Y-m-d)
     * @param $value
     */
    public function setDropoffDateAttribute($value)
    {
        if(empty($value)){
            unset($this->attributes['dropoff_date']);
            return;
        }
        $value = str_replace('/','-',$value);
        $dt = new DateTime($value);
        $this->attributes['dropoff_date'] = $dt->format('Y-m-d');
    }

    /**
     * Stores the correct value to the dropoff_location if same as pickup
     * @param $value
     */
    public function setDropoffLocationAttribute($value)
    {
        if(empty($value)){
            $this->attributes['dropoff_location'] = $this->attributes['pickup_location'];
        }
    }

    /**
     * Get the details of the reservation
     *
     * @return string
     */
    public function hasCustomer()
    {
        if(empty($this->email)){
            return null;
        }

        return !empty(Customer::where('email', $this->email)->take(1)->get());
    }

    /**
     * Get the details of the reservation
     *
     * @return string
     */
    public function getCustomer()
    {
        if(empty($this->email)){
            return null;
        }

        return Customer::where('email', $this->email)->where('site', $this->site)->orderBy('created_at', 'asc')->get();
    }
}