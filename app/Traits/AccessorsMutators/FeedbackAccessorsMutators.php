<?php

namespace App\Traits\AccessorsMutators;

trait FeedbackAccessorsMutators
{

    /**
     * Returns the average of the feedback
     *
     * @return string
     */
    public function getAverage()
    {
        $sum = $this->value_for_money_rating
            + $this->cleanliness_rating
            + $this->desk_service_rating
            + $this->pickup_rating
            + $this->dropoff_rating;

        $count = 5;

        return number_format($sum / $count, 1, ',', '');
    }
}