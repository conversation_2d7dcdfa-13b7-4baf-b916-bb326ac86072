<?php

namespace App\Traits\Relationships;

trait CategoryRelationships
{

    /*
     * Returns the listings that are related to the current category
     * AND are published at the current site (as defined by global config('appportal.name') param)
     */
    public function publishedListings()
    {
        $x = 'wherePublished'.ucfirst(config('appportal.name'));
        return $this->belongsToMany('App\Listing')->{$x}('1');
    }

}