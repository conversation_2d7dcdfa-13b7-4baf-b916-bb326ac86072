<?php

namespace App\Traits\Relationships;

use App\Accessory;
use App\Customer;
use App\Listing;
use App\Location;
use App\Reservation;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;

trait OfferRelationships
{

    /**
     *
     * @param $query
     */
    public function getCustomer(): BelongsTo
    {
        return $this->belongsTo('App\Customer', 'customer_id', 'id')->getResults();
    }

    /**
     *
     * Get the customer that owns the offer
     *
     * @param $query
     */
    public function customer(): BelongsTo
    {
        return $this->belongsTo(Customer::class);
    }

    /**
     *
     * Get the listing that owns the offer
     *
     * @param $query
     */
    public function listing(): BelongsTo
    {
        return $this->belongsTo(Listing::class);
    }

    /**
     *
     * @param $query
     */
    public function pickup_loc(): BelongsTo
    {
        return $this->belongsTo(Location::class, 'pickup_location');
    }

    /**
     *
     * @param $query
     */
    public function dropoff_loc(): BelongsTo
    {
        return $this->belongsTo(Location::class, 'dropoff_location');
    }


    /**
     * The accessories that belong to the offer.
     */
    public function accessories(): BelongsToMany
    {
        return $this->belongsToMany(Accessory::class, 'offer_to_accessory');
    }


    /**
     * Get the reservations for the offer.
     */
    public function reservations(): HasMany
    {
        return $this->hasMany(Reservation::class);
    }


}
