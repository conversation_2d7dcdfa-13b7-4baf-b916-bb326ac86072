<?php

namespace App\Traits\Relationships;

use App\Motif;
use App\MotifPost;
use App\Post;
use App\Tag;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

trait PostRelationships
{

    /**
     * The tags that belong to the post.
     */
    public function tags(): BelongsToMany
    {
        return $this->belongsToMany(Tag::class)
            ->withTimestamps();
    }


    /**
     * The motifs that belong to the post.
     */
    public function motifs(): BelongsToMany
    {
        return $this->belongsToMany(Motif::class)
            ->withTimestamps();
    }

    public function getMotifedPosts()
    {
        // get all motif_ids for this post
        $motif_ids = $this->motifs->pluck('id')->toArray();

        // get related posts ids
        // getRelatedPosts returns an array
        $related_post_ids = collect($this->getRelatedPosts())->pluck('id');

        // get all posts that have any of these motifs
        return Post::whereHas('motifs', function ($query) use ($motif_ids) {
            $query->whereIn('motif_id', $motif_ids);
        })
            ->where('id', '<>', $this->id)
            ->whereNotIn('id', $related_post_ids)
            ->translatedIn(app()->getLocale())
            ->published()
            ->orderByViews('asc')
            ->take(3)
            ->get();
    }


    /**
     * Returns (at most 3) related articles
     * For now the logic is just the previous/next ones (based on id)
     *
     * @return array
     */
    public function getRelatedPosts()
    {
        // initialize return array
        $related = [];

        // get next article
        $next = Post::where('id', '>', $this->id)
            ->translatedIn(app()->getLocale())
            ->published()
            ->orderBy('id', 'asc')
            ->first();

        if (!is_null($next)) {
            $related[] = $next;

            // and get one more from up next
            $more_next = Post::where('id', '>', $next->id)
                ->translatedIn(app()->getLocale())
                ->published()
                ->orderBy('id', 'asc')
                ->first();

            if (!is_null($more_next)) {
                $related[] = $more_next;
            }
        }

        // get previous article
        $previous = Post::where('id', '<', $this->id)
            ->translatedIn(app()->getLocale())
            ->published()
            ->orderBy('id', 'desc')
            ->first();

        if (!is_null($previous)) {
            $related[] = $previous;

            // and get one more from the rear
            $more_previous = Post::where('id', '<', $previous->id)
                ->translatedIn(app()->getLocale())
                ->published()
                ->orderBy('id', 'desc')
                ->first();

            if (!is_null($more_previous)) {
                $related[] = $more_previous;

                // and get one more more from the rear
                $more_more_previous = Post::where('id', '<', $more_previous->id)
                    ->translatedIn(app()->getLocale())
                    ->published()
                    ->orderBy('id', 'desc')
                    ->first();

                if (!is_null($more_more_previous)) {
                    $related[] = $more_more_previous;
                }
            }
        }

        // putting this here so we will not have only next items if 2 or more exist in either direction
        if (!is_null($next)) {
            if (!is_null($more_next)) {
                // and get one more more from up next
                $more_more_next = Post::where('id', '>', $more_next->id)
                    ->translatedIn(app()->getLocale())
                    ->published()
                    ->orderBy('id', 'asc')
                    ->first();

                if (!is_null($more_more_next)) {
                    $related[] = $more_more_next;
                }
            }
        }

        // we only want to send 3 related so we slice the reminders array
        $related = array_slice($related, 0, 3, true);

        return $related;
    }

}
