<?php namespace App\Repositories\Contracts;

interface ImageRepositoryInterface extends RepositoryInterface{
    
    /**
     * @param $form_data
     * @return mixed
     */
    public function upload( $form_data );
    
    /**
     * @param $filename
     * @param $extension
     * @return mixed
     */
    public function createUniqueFilename( $filename, $extension );
    
    /**
     * @param $photo
     * @param $filename
     * @return mixed
     */
    public function original( $photo, $filename );
    
    /**
     * @param $photo
     * @param $filename
     * @return mixed
     */
    public function icon( $photo, $filename );
    
    /**
     * @param $filename
     * @return mixed
     */
    public function delete( $filename );
    
    /**
     * @param $string
     * @param $force_lowercase
     * @param $anal
     * @return mixed
     */
    public function sanitize($string, $force_lowercase = true, $anal = false);
}