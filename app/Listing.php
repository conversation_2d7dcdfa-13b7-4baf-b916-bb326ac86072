<?php namespace App;

use App\Services\PhotoHandling\PhotoableTrait;
use App\Traits\AccessorsMutators\ListingAccessorsMutators;
use App\Traits\Scopes\ListingScopes;
use Astrotomic\Translatable\Contracts\Translatable as TranslatableContract;
use Astrotomic\Translatable\Translatable;
use App\Traits\Relationships\ListingRelationships;
use Cviebrock\EloquentSluggable\Sluggable;
use CyrildeWit\EloquentViewable\Contracts\Viewable;
use CyrildeWit\EloquentViewable\InteractsWithViews;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\App;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;
use Spatie\Sitemap\Contracts\Sitemapable;
use Spatie\Sitemap\Tags\Url;
use \Staudenmeir\EloquentHasManyDeep\HasRelationships;

class Listing extends Model implements TranslatableContract, HasMedia, Sitemapable, Viewable
{
	use PhotoableTrait,
        Sluggable,
        Translatable,
        ListingRelationships,
        ListingAccessorsMutators,
        ListingScopes,
        SoftDeletes,
        InteractsWithMedia,
        HasRelationships,
        InteractsWithViews;

    public $translatedAttributes = [
        'description',
        'description_cr',
        'meta_title',
        'meta_description',
        'meta_title_cr',
        'meta_description_cr',
    ];

    protected $fillable = [
        'manufacturer',
        'model',
        'description',
        'description_cr',
        'group',
        'group_id',
        'group_flagship',
        'engine',
        'fuel',
        'consumption',
        'seats',
        'capacity',
        'doors',
        'driver_age',
//         'discount_price',
//         'low_price',
//         'medium_price',
//         'high_price',
        'order',
        'transmission',
        'airbags',
        'four_wheeled',
        'abs',
        'radio_cd',
        'clima',
        'sunroof',
        'popular',
        'published_eurodollar',
        'published_cretanrentals',
    ];


    public function registerMediaConversions(\Spatie\MediaLibrary\MediaCollections\Models\Media $media = null): void
    {
        $this->addMediaConversion('thumb')
            ->width(368)
            ->height(232)
            ->sharpen(10);
    }

    /**
     * Return the sluggable configuration array for this model.
     *
     * @return array
     */
    public function sluggable(): array
    {
        return [
            'slug' => [
                'source' => 'short_title'
            ]
        ];
    }

    public function toSitemapTag(): Url | string | array
    {
        return Url::create(\LaravelLocalization::localizeUrl(route('listings.show', $this->slug), App::getLocale()))
            ->setLastModificationDate(Carbon::create($this->updated_at))
            ->setChangeFrequency(Url::CHANGE_FREQUENCY_MONTHLY)
            ->setPriority(0.9);
    }

}
