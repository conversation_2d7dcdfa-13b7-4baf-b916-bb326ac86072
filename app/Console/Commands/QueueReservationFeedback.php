<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Reservation;
use App\Jobs\SendReservationFeedback;
use Illuminate\Foundation\Bus\DispatchesJobs;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;

class QueueReservationFeedback extends Command
{
	use DispatchesJobs;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'reservation:sendFeedback';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Add reservations that should receive feedback request to queue';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
    	// Find the reservations that should receive feedback requests and add them to the queue
    	// Feedback requests should be sent to reservations that:
    	// ended more than 5 days ago
    	// ended less than 60 days ago
    	// have not received a feedback request yet
    	// have been marked as SHOW
    	$feedbackPeriod = 5;
    	$reservations = DB::table('reservations')
    	->select('id')
    	->where('dropoff_date', '<', Carbon::now()->subDays($feedbackPeriod))
    	->where('dropoff_date', '>', Carbon::now()->subDays(220))
    	->where('show', '=', 'show')
    	->where('feedback_requested_at', '=', '0000-00-00 00:00:00')
        ->whereNull('deleted_at')
        ->limit(10)
    	->get();

//    	dd($reservations);

    	foreach($reservations as $reservationId){
    		$reservation = Reservation::findOrFail($reservationId->id);
    		$this->dispatch(new SendReservationFeedback($reservation));
    	}

        // log execution of command
        Log::info('QueueReservationFeedback command executed');

    	return true;
    }
}
