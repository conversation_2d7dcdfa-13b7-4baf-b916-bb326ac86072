<?php

namespace App;

use App\Traits\AccessorsMutators\PhotoAccessorsMutators;
use App\Traits\Relationships\PhotoRelationships;
use Astrotomic\Translatable\Contracts\Translatable as TranslatableContract;
use Astrotomic\Translatable\Translatable;
use Spatie\MediaLibrary\MediaCollections\Models\Media as BaseMedia;

class Photo extends BaseMedia implements TranslatableContract {

    protected $table = 'photos';

	use PhotoRelationships,
        PhotoAccessorsMutators,
        Translatable;

    public $translatedAttributes = [
        'caption',
        'alt_text',
        'description',
    ];
	
    protected $fillable = [
        'original_name',
        'filename',
        'main',
    ];

}
