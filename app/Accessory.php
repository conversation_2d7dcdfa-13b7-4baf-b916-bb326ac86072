<?php namespace App;

use App\Services\ImageHandling\ImageableInterface;
use App\Services\ImageHandling\ImageableTrait;
use Astrotomic\Translatable\Contracts\Translatable as TranslatableContract;
use Astrotomic\Translatable\Translatable;

class Accessory extends BaseModel implements ImageableInterface, TranslatableContract
{
	use ImageableTrait;
	use Translatable;

	protected $table = 'accessories';
    public $translatedAttributes = ['name', 'description'];
	
	protected $fillable = [
		'name',
		'description',
		'price'];

	/**
	 * Overrides the parent method to add image handling functionality
	 *
	 * @param array $attributes
	 *
	 * @return void
	 */
	protected function performCustomCreationTasks(array $attributes = [])
	{
		// handle image data
		if (! empty($attributes['image']))
		{
			$this->storeImage('img/accessories/');
		}
		return $this;
	}

	/**
	 * Overrides the parent method to add image handling functionality
	 *
	 * @param array $attributes
	 *
	 * @return void
	 */
	protected function performCustomUpdateTasks(array $attributes = [])
	{
		// handle image data
		if (! empty($attributes['image']))
		{
			$this->deleteImage()
				->storeImage('img/accessories/');
		}
		return $this;
	}

	/**
	 * Deletes image data and then calls the delete method of (grand)parent to delete the model
	 * @return mixed
	 */
	public function delete()
	{
		// delete image data
		$this->deleteImage();

		// proceed with actual model deletion
		return parent::delete();
	}
}