<?php
/**
 * Created by PhpStorm.
 * User: bmenekl
 * Date: 12/26/14
 * Time: 10:54
 */

namespace App\Services\Validation;


class QuoteValidator extends Validator {

    /**
     * Default rules
     *
     * @var array
     */
    protected $rules = [
        'listing_id' => ['required_without:group', 'integer'],
        'group' => ['required_without:listing_id', 'integer'],
        'pickup_time' => 'required',
        'dropoff_time' => 'required',
//        'listing_manufacturer' => 'required',
//        'listing_model' => 'required',
//        'listing_group' => array('required', 'in:A,B,C,D,E,L,H,F,K'),
//        'listing_transmission' => array('required', 'in:automatic,manual'),
        'customer_name' => 'required',
        'customer_email' => ['required', 'email'],
//        'customer_address' => 'required',
//        'customer_country' => 'required',
//        'customer_telephone' => 'required',
//        'total_days' => 'required',
//        'total_price' => 'required',
//        'total_discount_price' => 'required',
    ];


    /**
     * Rules for updating a reservation
     *
     * @var array
     */
    protected $updateRules = [
//        'pickup_date' => 'required',
//        'pickup_time' => 'required',
//        'pickup_location' => 'required',
//        'dropoff_date' => 'required',
//        'dropoff_time' => 'required',
//        'dropoff_location' => 'required',
        'listing_id' => ['required', 'integer'],
//        'listing_manufacturer' => 'required',
//        'listing_model' => 'required',
//        'listing_group' => array('required', 'in:A,B,C,D,E,L,H,F,K'),
//        'listing_transmission' => array('required', 'in:automatic,manual'),
//        'customer_name' => 'required',
        'customer_email' => ['required', 'email'],
//        'customer_address' => 'required',
//        'customer_country' => 'required',
//        'customer_telephone' => 'required',
//        'total_days' => 'required',
//        'total_price' => 'required',
//        'total_discount_price' => 'required',
    ];
}