<?php
/**
 * Created by PhpStorm.
 * User: bmenekl
 * Date: 5/24/15
 * Time: 09:02
 */

namespace App\Services\Offer;

use App\Group;
use App\Listing;
use App\Location;
use App\Accessory;
use App\Period;
use App\Pricing;
use DateTime;


class RentalOffer {

    /**
     * Holds the listing object
     * @var
     */
    protected $listing;

    /**
     * Holds the DateRange object
     * @var
     */
    protected $date_range;

    /**
     * Holds the discountBehaviour object
     * @var
     */
    protected $discount_behaviour;

    /**
     * Holds true/false whether the offer has extra miles charge or not
     * @var bool
     */
    protected $extra_miles_charge = false;

    /**
     * Holds true/false whether the offer has after hours charge or not
     * @var bool
     */
    protected $after_hours_charge = false;

    /**
     * Holds the actual cost of the after hours charge
     */
    protected $after_hours_charge_cost = 0;

    /**
     * Holds true/false whether the offer has after hours charge or not
     * @var bool
     */
    protected $fuel_plan_charge = false;

    /**
     * Holds true/false whether the offer has availability or not
     * @var bool
     */
    protected $is_available = true;

    /**
     * Holds an aray of the accessories that the offer has.
     *
     * In the following format
     *
     * accessory_id => accessory_occurences
     *
     * @var array
     */
    protected $accessories = [];

    public function __construct(
        Listing $listing = null,
        DateRange $date_range,
        DiscountBehaviour $discount,
        $pickup_location = '',
        $dropoff_location = '',
        array $accessories = array(),
    	$fuel_plan_charge = false
    )
    {
        $this->listing = $listing;
        $this->date_range = $date_range;
        $this->discount_behaviour = $discount;
        $this->pickup_location = $pickup_location;
        $this->dropoff_location = $dropoff_location;
        $this->accessories = $accessories;
        $this->fuel_plan_charge = $fuel_plan_charge;

        $this->setAvailability();
    }

    /**
     * Returns the percentage of the discount
     *
     * @return mixed
     */
    public function getDiscountPercentage()
    {
        return $this->discount_behaviour->getDiscount();
    }

    public function getDiscountAmount()
    {
        return $this->discount_behaviour->getDiscountAmount();
    }

    /**
     * The total price minor the discount
     * The actual paying price
     *
     * @return float|mixed
     */
    public function getTotalDiscountPrice()
    {
        $base_price = $this->getRangePrice() - $this->getDiscountAmount() < 0 ?
            0 : $this->getRangePrice() - $this->getDiscountAmount();
        // apply range discount and any extra charges
        return $base_price
            + $this->getExtraMilesCharge()
            + $this->getRemoteLocationCharge()
            + $this->getAfterHoursCharge()
            + $this->getFuelPlanCharge()
            + $this->getAccessoriesPrice();
    }

    /**
     * Calculate the range price (based on the date range)
     *
     * @return mixed
     */
    public function getRangePrice()
    {
        // if no specific range is given return the marketing price
        if ($this->date_range->getTotalDays() == 0)
        {
        	$groupField = 'base_price' . (config('appportal.name') == 'cretanrentals' ? '_cretan' : '' );
            $group = Group::where('id', $this->listing->group_id)->first();
            return $group->$groupField;
        }

        $periodCounters = $this->date_range->getPeriodCounters();

        // calculate base price based on date range

        $priceField = 'price' . (config('appportal.name') == 'cretanrentals' ? '_cretan' : '' );

        $price = 0;
        foreach ($periodCounters as $periodAlias => $numberOfDays)
        {
            if ($numberOfDays)
            {
                $period = Period::where('alias', $periodAlias)->first();
                if ($period && isset($period->id) && isset($this->listing->group_id))
                {
                    $price += $numberOfDays * Pricing::where('period_id', $period->id)->where('group_id', $this->listing->group_id)->first()->$priceField;
                }
            }
        }
        return $price;
    }

    /**
     * The range price minus the discount amount
     * In essence the starting price to be paid
     *
     * @return mixed
     */
    public function getBasePrice()
    {
        $base_price = $this->getRangePrice() - $this->getDiscountAmount();
        return $base_price < 0 ? 0 : $base_price;
    }

    /**
     * Returns the total price of all accessories in the offer (counts occurences)
     * @return int
     */
    public function getAccessoriesPrice()
    {
        $price = 0;

        foreach ($this->accessories as $accessory_id => $accessory_count)
        {
            // get accessoru
            $accessory = Accessory::find($accessory_id);

            $price = $price + $accessory->price * $accessory_count * $this->date_range->getTotalDays();
        }

        return $price;
    }

    /**
     * Returns the price for a given accessory in the order (counts occurences)
     *
     * @param $accessory_id
     * @return int
     */
    public function getAccessoryPrice($accessory_id)
    {
        if (empty($this->accessories[$accessory_id]))
        {
            return 0;
        }

        // get accessory
        $accessory = Accessory::findOrFail($accessory_id);

        return $accessory->price * $this->accessories[$accessory_id];
    }

    /**
     * Returns true/false whether the offer needs to charge an extra day or not
     *
     * @return bool
     */
    public function hasExtraDayCharge()
    {
        return $this->date_range->getExtraDayCharge();
    }

    /**
     * Returns whether the offer has extra miles charge or not, due to distantiated pickup and dropoff locations
     * @return bool
     */
    public function hasExtraMilesCharge()
    {
        $pickup_loc = Location::find($this->pickup_location);
        $dropoff_loc = Location::find($this->dropoff_location);

        if (!empty($pickup_loc) && !empty($dropoff_loc))
        {
            if ($pickup_loc->area != $dropoff_loc->area)
            {
                return true;
            }
        }
        return false;
    }

    public function getExtraMilesCharge()
    {
        return $this->hasExtraMilesCharge() ? config('rental_offer.extra_miles') : 0;
    }

    /**
     * Returns whether the offer has remote location charge or not, due to remote pickup location
     * @return bool
     */
    public function hasRemoteLocationPickupCharge()
    {
        $pickup_loc = Location::find($this->pickup_location);

        if ( ! empty($pickup_loc) )
        {
            if ($pickup_loc->remote_location || $pickup_loc->remote_location_short)
            {
                return true;
            }
        }
        return false;
    }

    /**
     * Returns whether the offer has remote location charge or not, due to remote dropoff location
     * @return bool
     */
    public function hasRemoteLocationDropoffCharge()
    {
        $dropoff_loc = Location::find($this->dropoff_location);

        if ( ! empty($dropoff_loc) )
        {
            if ($dropoff_loc->remote_location || $dropoff_loc->remote_location_short)
            {
                return true;
            }
        }
        return false;
    }

    public function getRemoteLocationCharge()
    {
        $charge = 0;

        // check fer remote pickup charge
        if ($this->hasRemoteLocationPickupCharge())
        {
            // get the pickup location in order to see whether to charge normal (long) or short remote location charge
            $pickup_loc = Location::find($this->pickup_location);

            if ( ! empty($pickup_loc) ) {
                if ($pickup_loc->remote_location_short) {
                    $charge = $charge + config('rental_offer.remote_location_short');
                } else {
                    $charge = $charge + config('rental_offer.remote_location');
                }
            }
        }

        // check fer remote dropoff charge
        if ($this->hasRemoteLocationDropoffCharge())
        {
            // get the dropoff location in order to see whether to charge normal (long) or short remote location charge
            $dropoff_loc = Location::find($this->dropoff_location);

            if ( ! empty($dropoff_loc) ) {
                if ($dropoff_loc->remote_location_short) {
                    $charge = $charge + config('rental_offer.remote_location_short');
                } else {
                    $charge = $charge + config('rental_offer.remote_location');
                }
            }
        }

        return $charge;
    }

    /**
     * Returns true/false based on whether the offer has after hours charge
     * @return bool
     */
    public function hasAfterHoursCharge()
    {
    	$pickupDateTime = new DateTime($this->date_range->getPickupDateTime());
    	$pickupTime = strtotime(date("H:i:s",$pickupDateTime->getTimestamp()));

    	// time points to define the time ranges
        // !IMPORTANT: we must have time_point_a < time_point_b < time_point_c
    	$time_point_a   = strtotime(config( 'rental_offer.after_hours.timepoints.a'));
    	$time_point_b   = strtotime(config( 'rental_offer.after_hours.timepoints.b'));
    	$time_point_c   = strtotime(config( 'rental_offer.after_hours.timepoints.c'));

    	// range 00:00 - 05:30
    	if ($pickupTime < $time_point_a)
        {
            $this->after_hours_charge_cost = config('rental_offer.after_hours.costs.high');
            return true;
        }
    	// range 05:30 - 08:00
    	else if ($pickupTime < $time_point_b)
        {
            $this->after_hours_charge_cost = config('rental_offer.after_hours.costs.low');
            return true;
        }
        // range 08:00 - 21:00 (no cost)
        else if ($pickupTime < $time_point_c)
        {
            return false;
        }
        // range 21:00 - 24:00
        else
        {
            $this->after_hours_charge_cost = config('rental_offer.after_hours.costs.low');
            return true;
        }
    }

    public function getAfterHoursCharge()
    {
        return $this->hasAfterHoursCharge() ? $this->after_hours_charge_cost : 0;
    }

    /**
     * Returns whether the offer has fuel plan charge
     * @return bool
     */
    public function hasFuelPlanCharge()
    {
    	return $this->fuel_plan_charge;
    }

    public function getFuelPlanCharge()
    {
        return $this->hasFuelPlanCharge() ? $this->listing->group->fuel_plan : 0;
    }

    /**
     * Returns whether the specific offer contains dates that the listing is unavailable or not
     */
    public function hasAvailability()
    {
        return $this->is_available;
    }

    /**
     * Sets the is_available attribute accordingly
     */
    protected function setAvailability()
    {
        // failsafe checks
        // no listing
        if (empty($this->listing))
            $this->is_available = false;

        // no date range
        if ($this->date_range->getTotalDays() < 1)
            $this->is_available = true;

        // get the unavailable periods fer teh specific group
        // depending on the portal
        $availability = 'available' . (config('appportal.name') == 'cretanrentals' ? '_cretan' : '' );
        $unavailability = Pricing
            ::where('group_id', $this->listing->group->id)
            ->where($availability, 0)
            ->get();

        $days_index = $this->date_range->getDaysIndex();

        // check the actual availability against the given date range
        foreach($unavailability as $unavailable)
        {
            // get the un-availability period alias
            $unavailability_alias = Period::where('id', $unavailable->period_id)->first()->alias;
            if ( array_search($unavailability_alias, $days_index) )
            {
                $this->is_available = false;
                return true;
            }
        }

        // if we came thus far, everything is ok
        $this->is_available = true;
    }

    /**
     *  Get the offer's total days
     *
     */
    public function getTotalDays()
    {
        return $this->date_range->getTotalDays();
    }


    /**
     * Get the name of the pickup location
     *
     * @return mixed
     */
    public function getPickupLocation()
    {
        return Location::find($this->pickup_location)->name;
    }

    /**
     * Get the name of the dropoff location
     *
     * @return mixed
     */
    public function getDropoffLocation()
    {
        return Location::find($this->dropoff_location)->name;
    }
}
