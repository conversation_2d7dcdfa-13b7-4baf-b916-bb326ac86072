<?php

namespace App;

use App\Traits\Relationships\TagRelationships;
use Illuminate\Database\Eloquent\Model;
use C<PERSON>brock\EloquentSluggable\Sluggable;

class Tag extends Model {
	
	use TagRelationships,
        Sluggable;
	
    protected $fillable = [
        'title',
    ];

    /**
     * Return the sluggable configuration array for this model.
     *
     * @return array
     */
    public function sluggable(): array
    {
        return [
            'slug' => [
                'source' => 'title'
            ]
        ];
    }

}
