<?php

use App\Customer;
use App\Reservation;
use Illuminate\Support\Arr;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;

function removeGreekPunctuation($text)
{
    $trans = array(
        "ή" => "η",
        "ά" => "α",
        "ύ" => "υ",
        "έ" => "ε",
        "ί" => "ι",
        "ό" => "ο",
        'ώ' => 'ω',
        'Έ' => 'Ε'

    );
    return strtr($text, $trans);
}

function getFilteredListingsListUrl($route_name, array $request_query, array $add_query, array $remove_query)
{
    if(empty($request_query))
    {
        return route($route_name, $add_query);
    }
    else
    {
        $result_query = array_diff(array_merge($request_query, $add_query), $remove_query);
        return route($route_name, $result_query);
    }
}

function getAnalyticsData(string $analytics_year = null)
{
//    $customersQuery         = Customer::whereIn('customers.id', function($query) use ($analytics_year){
//        $query->selectRaw('distinct customer_id')
//            ->from(with(new Reservation)->getTable())
//            ->whereBetween('pickup_date', [$analytics_year . '-01-01', $analytics_year . '-12-31']);
//    })

    // Init base queries
    $customersQuery         = Customer::
        selectRaw('lower(countries.code) as code, count(*) as count')
        ->leftJoin('countries', 'customers.country_id', '=', 'countries.id')
        ->whereNotNull('country_id')
        ->groupBy('country_id');
    $reservations_count     = Reservation::onlyShow();
    $reservations_location  = Reservation::onlyShow()->whereRaw('pickup_location != dropoff_location');
    // Add year constraint
    if(!empty($analytics_year))
    {
        $customersQuery->whereBetween(DB::raw('date(created_at)'), [$analytics_year . '-01-01', $analytics_year . '-12-31']);
        $reservations_count->whereBetween('pickup_date', [$analytics_year . '-01-01', $analytics_year . '-12-31']);
        $reservations_location->whereBetween('pickup_date', [$analytics_year . '-01-01', $analytics_year . '-12-31']);
    }
    // Get customers
    $customers = $customersQuery
        ->get()
        ->pluck('count', 'code')
        ->toArray();
    // Change greece from el to gr for vectorMap plugin
    if(isset($customers['el']))
    {
        $customers['gr'] = $customers['el'];
        unset($customers['el']);
    }
    arsort($customers);
    // Count data
    $data['counts']['total_customers']      = array_sum($customers);
    $data['counts']['total_reservations']   = $reservations_count->count();
    $data['counts']['location_diff']        = $reservations_location->count();
    // Table data
    $data['tables']['pickup_location']       = Reservation::getReservationShowDataAggregatedBy('pickup_location', $analytics_year)->toArray();
    $data['tables']['dropoff_location']      = Reservation::getReservationShowDataAggregatedBy('dropoff_location', $analytics_year)->toArray();
    $data['tables']['listing_model']         = Reservation::getReservationShowDataAggregatedBy('listing_model', $analytics_year)->toArray();
    $data['tables']['listing_group']         = Reservation::getReservationShowDataAggregatedBy('listing_group', $analytics_year)->toArray();
    // Graph data
    $data['graphs']['created_at']            = Reservation::getReservationShowDataMonthlyAggregatedBy('created_at', $analytics_year)->toArray();
    $data['graphs']['pickup_date']           = Reservation::getReservationShowDataMonthlyAggregatedBy('pickup_date', $analytics_year)->toArray();
    $data['graphs']['customers']             = $customers;

    return $data;
}

function getBudgetAnalyticsData(string $analytics_year = null, string $analytics_month = null)
{

    // Init base queries
    $reservationsPickup     = Reservation::onlyShow()->selectRaw('count(*) as reservations, sum(final_price) as revenue');
    $reservationsCreated    = Reservation::onlyShow()->selectRaw('count(*) as reservations, sum(final_price) as revenue');
    // Add year constraint
    if(!empty($analytics_year))
    {
        $start_month        = 1;
        $end_month          = 12;
        if(!empty($analytics_year) && !empty($analytics_month))
        {
            $start_month    = $analytics_month;
            $end_month      = $analytics_month;
        }
        $reservationsPickup->whereBetween('pickup_date', [
            Carbon::createFromFormat('Y-m', $analytics_year . '-' . $start_month)->startOfMonth()->format('Y-m-d'),
            Carbon::createFromFormat('Y-m', $analytics_year . '-' . $end_month)->addMonth()->startOfMonth()->format('Y-m-d')
        ]);
        $reservationsCreated->whereBetween(DB::raw('date(created_at)'), [
            Carbon::createFromFormat('Y-m', $analytics_year . '-' . $start_month)->startOfMonth()->format('Y-m-d'),
            Carbon::createFromFormat('Y-m', $analytics_year . '-' . $end_month)->addMonth()->startOfMonth()->format('Y-m-d')
        ]);
    }
    // Count data
    $reservationsPickupData = $reservationsPickup->first();
    $reservationsCreatedData = $reservationsCreated->first();
    $data['counts']['total_reservations_pickup']   = $reservationsPickupData->reservations;
    $data['counts']['total_revenue_pickup']        = $reservationsPickupData->revenue;
    $data['counts']['total_reservations_created']  = $reservationsCreatedData->reservations;
    $data['counts']['total_revenue_created']       = $reservationsCreatedData->revenue;
    // Table data
    $data['tables']['listing_model_sum']     = Reservation::getReservationShowDataAggregatedBy('listing_model', $analytics_year, $analytics_month, ['sum' => 'final_price'])->toArray();
    $data['tables']['listing_group_sum']     = Reservation::getReservationShowDataAggregatedBy('listing_group', $analytics_year, $analytics_month, ['sum' => 'final_price'])->toArray();
    // Graph data
    $data['graphs']['sum_pickup_date']       = Reservation::getReservationShowDataMonthlyAggregatedBy('pickup_date', $analytics_year, $analytics_month, ['sum' => 'final_price'])->toArray();
    $data['graphs']['pickup_date']           = Reservation::getReservationShowDataMonthlyAggregatedBy('pickup_date', $analytics_year, $analytics_month)->toArray();
    $data['graphs']['sum_created_date']      = Reservation::getReservationShowDataMonthlyAggregatedBy('created_at', $analytics_year, $analytics_month, ['sum' => 'final_price'])->toArray();
    $data['graphs']['created_date']          = Reservation::getReservationShowDataMonthlyAggregatedBy('created_at', $analytics_year, $analytics_month)->toArray();

    return $data;
}

function getGoogleBusinessClient()
{
    $client = new Google_Client();
    // Client app name
    $client->setApplicationName("Eurodollar reviews");
    // Client credentials
    $client->setAuthConfig(config('google_client.credentials'));
    $client->setAccessToken(config('google_client.token'));
    // Client scope
    $client->addScope("https://www.googleapis.com/auth/business.manage");

    // Access token has expired
    if ($client->isAccessTokenExpired()) {
        // Refresh token
        if(!$client->getRefreshToken())
        {
            throw new \Exception('Cannot refresh Google API token');
        }
    }
    // Authorize client
    return $client->authorize();
}
