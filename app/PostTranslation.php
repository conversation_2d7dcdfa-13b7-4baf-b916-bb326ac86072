<?php

namespace App;

use App\Traits\Relationships\PostTranslationRelationships;
use Cviebrock\EloquentSluggable\Sluggable;
use Illuminate\Database\Eloquent\Model;
use App\Traits\Scopes\LocalisableScopes;
use Spatie\Searchable\Searchable;
use Spatie\Searchable\SearchResult;

class PostTranslation extends Model implements Searchable {

	use Sluggable,
        LocalisableScopes,
        PostTranslationRelationships;

    protected $fillable = [
        'title',
        'meta_title',
        'meta_description',
        'content',
    ];

    /**
     * Return the sluggable configuration array for this model.
     *
     * @return array
     */
    public function sluggable(): array
    {
        return [
            'slug' => [
                'source' => 'title',
                'maxLength' => 188,
            ]
        ];
    }


    public function getSearchResult(): SearchResult
    {
        $url = route('posts.show', $this->slug);

        return new \Spatie\Searchable\SearchResult(
            $this,
            $this->title,
            $url
        );
    }

}
