.table{
    display:table;
    width:100%;box-sizing:border-box;
}
.table-cell{
    display:table-cell;
    vertical-align:middle;
}

.search_offers{
	padding:25px 30px 0 0!important;

}
@media screen and   (max-width: 980px) {
    .search_offers{
	    padding:10px 30px!important;

	}
}

.search_offers h4 {
	   font-size:15px;
}
.home_search .table{
	width:100%;
}
.home_search .table-cell{
	padding:20px 25px;
	 vertical-align:top;

}
.home_search_button{
	margin:15px 30px 15px 30px
}


@media screen and (min-width: 768px) and (max-width: 980px) {
    .home_search .table{
	    display:block;
	}
	.home_search .table-cell{
	     display:inline-block;
	      width:40%;
	}

}
@media screen and (max-width: 767px) {
    .home_search .table{
        display:block;
    }
    .home_search .table-cell{
         display:block;


    }
}

.coupon.generic {
    background-image:url('../images/rent_car_grass.jpeg');
}
.coupon.mountain {
    background-image:url('../images/rent_car_landscape.jpg');
}
.coupon.autumn {
    background-image:url('../images/rent_car_autumn_2.jpg');
}
.coupon.summer {
    background-image:url('../images/rent_car_beach_1.jpg');
}
.coupon.spring {
    background-image:url('../images/rent_car_flower.jpeg');
}
.coupon.winter {
    background-image:url('../images/rent_car_dawn.jpg');
}

.coupon{
	background:url('../images/rent_car_autumn.jpg');
	background-position:0px -570px;
	background-size:cover;
	color:#fff;
	width:100%;
	border-radius: 3px;
	box-sizing:border-box;
}

.coupon p{
    opacity:0.9
}
.coupon .button{
    vertical-align: middle;
    background-color:#FEC308;
    color:#000
}

.coupon h2{
	font-size:26px;
}
.coupon_overlay{
	background-color:rgba(0,0,0,0.3);
	padding:40px 40px;
	border-radius: 3px;
}
.coupon_responsive{
	margin:20px 0;
}
.coupon_responsive .table, .coupon_responsive .table-cell{
    display:block;
}
.coupon_responsive.coupon{
    background-position:0px 0px ;
}
.coupon_responsive .coupon_code{
	margin-top:20px;
}
@media screen and (min-width: 768px) and (min-width: 980px) and (max-width: 1300px) {
    .coupon{
        background-position:0px -420px;
    }

}

@media screen and (min-width: 768px) and (max-width: 980px) {
    .coupon_responsive.coupon{
        background-position:0px 0px;
    }
    .coupon{
        background-position:0px -220px;
    }


}
@media screen and (max-width: 767px) {
    .coupon_responsive.coupon{
        background-position:0px -70px;
    }
      .coupon{
        background-position:center;
    }
     .coupon .table-cell{
        display:block!important;
        text-align:left;
    }
}

@media screen and (max-width: 479px) {
    .coupon_responsive.coupon, .coupon{
        background-position:center;
    }
     .coupon .table-cell{
        display:block!important;
        text-align:left;
    }
}



.image-small {
	 background-position:center; background-size:cover; width:130px; height:90px;
}
.image-small:hover {
     opacity:0.8
}

.socials_header .fa-twitter:hover, .socials_header .fa-facebook:hover{opacity:0.8}
.timepicker:hover, .hasDatepicker:hover{
	cursor:pointer;
}

#newsletter_form input[type="text"]  {
	background-color:#555;
	border:0;
	font-size:12px;
	padding:7px 5px;
	color:#ccc
}

#newsletter_form input[type="text"]:focus  {
    background-color:#777;
}

#newsletter_form input[type="submit"]  {
    background-color:#555;
    border:0;
    font-size:12px;
    padding:7px 8px;
    color:#ccc
}
#newsletter_form input[type="submit"]:hover  {
    background-color:#888;
    cursor:pointer;
    color:#fff
}


.bck.onhover h2 a:hover{
   color:#3498DB!important;
}
.bck.onhover img:hover{
  opacity:0.9;
}

.flags {opacity:0.85; border:1px solid #32302F; margin-right:3px;
}
.flags:hover {opacity:1; border:1px solid #3498DB}

.flags_grey {opacity:0.85;  margin-right:3px;
filter: url("data:image/svg+xml;utf8,<svg xmlns=\'http://www.w3.org/2000/svg\'><filter id=\'grayscale\'><feColorMatrix type=\'matrix\' values=\'0.3333 0.3333 0.3333 0 0 0.3333 0.3333 0.3333 0 0 0.3333 0.3333 0.3333 0 0 0 0 0 1 0\'/></filter></svg>#grayscale"); /* Firefox 10+, Firefox on Android */
    filter: gray; /* IE6-9 */
    -webkit-filter: grayscale(100%); /* Chrome 19+, Safari 6+, Safari 6+ iOS */
}
.flags_grey:hover {opacity:1;
  filter: url("data:image/svg+xml;utf8,<svg xmlns=\'http://www.w3.org/2000/svg\'><filter id=\'grayscale\'><feColorMatrix type=\'matrix\' values=\'1 0 0 0 0, 0 1 0 0 0, 0 0 1 0 0, 0 0 0 1 0\'/></filter></svg>#grayscale");
    -webkit-filter: grayscale(0%);
}


.bck_badge {
	background-image: url(../images/badge.png);
	background-repeat:no-repeat;
	background-position: top right;
	}

.bck_slider {
    background-image: url('../images/eurodollar.jpg');
    background-repeat:no-repeat;
    background-position: center center;
    background-size:cover;
    }

.bck_slider.chania {
    background-image: url('../images/crete/chania_hero.jpg');
    }

.bck_slider.chania_airport {
	background-image: url('../images/crete/chania_airport_hero.jpg');
}

.bck_slider.heraklion {
    background-image: url('../images/crete/heraklion_airport_hero2.jpg');
    }

.bck_slider.heraklion_airport {
    background-image: url('../images/crete/heraklion_airport_hero.jpg');
    }

.bck_slider.rethymno {
    background-image: url('../images/crete/rethymno_hero.jpg');
    }

.bck_slider.agios_nikolaos {
    background-image: url('../images/crete/agios_nikolaos_hero.jpg');
    }


.relative { position:relative;}


.text-shadow {
	text-shadow: 0px 1px 1px rgba(0, 0, 0, 0.5);
}


.testimonials h3 {font-size:26px; line-height:32px}
@media screen and (max-width: 767px) {

.testimonials h3 {font-size:20px; line-height:28px}
}

.header_gradient{

	background: rgb(255,191,2); /* Old browsers */
	background: -moz-linear-gradient(top,  rgba(255,191,2,0.5) 0%, rgba(253,198,14,1) 100%); /* FF3.6+ */
	background: -webkit-gradient(linear, left top, left bottom, color-stop(0%,rgba(255,191,2,0.5)), color-stop(100%,rgba(253,198,14,1))); /* Chrome,Safari4+ */
	background: -webkit-linear-gradient(top,  rgba(255,191,2,0.5) 0%,rgba(253,198,14,1) 100%); /* Chrome10+,Safari5.1+ */
	background: -o-linear-gradient(top,  rgba(255,191,2,0.5) 0%,rgba(253,198,14,1) 100%); /* Opera 11.10+ */
	background: -ms-linear-gradient(top,  rgba(255,191,2,0.5) 0%,rgba(253,198,14,1) 100%); /* IE10+ */
	background: linear-gradient(to bottom,  rgba(255,191,2,0.5) 0%,rgba(253,198,14,1) 100%); /* W3C */
	filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#ffbf02', endColorstr='#fdc60e',GradientType=0 ); /* IE6-9 */
}

.padding-header {
padding:1.7em 0
}

table tr td {  padding:5px;}

a.disabled {
  opacity: 0.5
  pointer-events: none
  cursor: default
}


.testimonials {background-image: url(../images/crete.jpg); background-size:cover; background-position:center;  }
.stripes {background-image: url(../images/stripes.jpg); }

.shadow {
	z-index:1;
	-webkit-box-shadow: 0px 0px 5px 1px rgba(0,0,0,3.25);
	-moz-box-shadow: 0px 0px 5px 1px rgba(0,0,0,0.35);
	box-shadow: 0px 0px 5px 1px rgba(0,0,0,0.35);
}

.fa-angle-right { margin-right:5px;}
.fa.fa-map-marker { font-size:30px!important; opacity:0.6}
.fa.fa-map-marker:hover { color:#3498DB; cursor:pointer;opacity:0,9;
text-shadow:0px 0px #999}


.owl-dots {

}
.owl-dot {

	border:2px solid #FEC206;
	width:10px;
	height:10px;
	display:inline-block;
	margin:5px;
	margin-top:30px;
	  /* Safari 3-4, iOS 1-3.2, Android 1.6- */
  -webkit-border-radius: 50%;

  /* Firefox 1-3.6 */
  -moz-border-radius: 50%;

  /* Opera 10.5, IE 9, Safari 5, Chrome, Firefox 4, iOS 4, Android 2.1+ */
  border-radius: 50%;
}

.owl-dot.active{ background-color:#FEC206;}

/* ====== media ======

<div class="media attribution">

  <a href="http://twitter.com/stubbornella" class="img">
    <img src="http://stubbornella.com/profile_image.jpg" alt="me" />
  </a>

  <div class="bd">
    @Stubbornella 14 minutes ago
  </div>

</div>

 */
.media { }

.media,
.bd {
	overflow: hidden;
	_overflow: visible;
	zoom: 1;
}

.media .img {
	float: left;
	margin-right: 0px;
}

.media .img img { display: block; }

.media .imgExt {
	float: right;
	margin-left: 10px;
}


/* ====== media ====== */
/* Tablet */
@media screen and (min-width: 768px) and (max-width: 980px) {
    #phone_menu {    float:left;}
	}
@media screen and (max-width: 767px) {
	   #logo { width:300px;}
	    #phone_menu {font-size:12px; font-weight:bold; float:left;}
	   #phone_menu a, #phone_menu .noAnchor-menu-item { padding:5px 4px}
	   .mobile-left {text-align:left!important}
 }

 /* Breadcrumps */
 .breadcrumps {
	list-style:none;
	margin-top:-20px;
 }
 .breadcrumps li {
	list-style:none;
	display:inline-block;
 }
 .breadcrumps li span{
	color:rgb(126, 124, 117);
 }

/* Listing item */
.listing-item-title > a > span{
	vertical-align:middle;
	display:inline-block;
	margin-right: 7px;
}
.badges {
	display:inline-block;
	list-style:none;
}
.badges li {
	display:inline-block;
	list-style:none;

}
.badges li span{
	font-family: 'Roboto', "HelveticaNeue", "Helvetica Neue", Helvetica, Arial, sans-serif;
	display:inline-block;
	padding: 1px 10px;
	color: #fff;
	background: #434b56;
	font-size: 12px;
	-moz-border-radius: 3px;
	-webkit-border-radius: 3px;
	border-radius: 3px;
	margin-right: 7px;
	font-weight:normal;
	vertical-align:middle;
}
.badge.top-seller {
	color: #333;
	background-color: rgba(244,187,1,0.8);
}
.badge.best-price {
	background:#C83937;
}

.corner-ribbon{
  width: 200px;
  background: #e43;
  position: absolute;
  text-align: center;
  line-height: 25px;
  letter-spacing: 1px;
  color: #f0f0f0;
  transform: rotate(-45deg);
  -webkit-transform: rotate(-45deg);
  top: 20px;
  right: -70px;
  left: auto;
  font-size: 13px;
  transform: rotate(45deg);
  -webkit-transform: rotate(45deg);
}

/* Colors */
.corner-ribbon.white{background: #f0f0f0; color: #555;}
.corner-ribbon.black{background: #333;}
.corner-ribbon.grey{background: #999;}
.corner-ribbon.blue{background: #39d;}
.corner-ribbon.green{background: #2c7;}
.corner-ribbon.turquoise{background: #1b9;}
.corner-ribbon.purple{background: #95b;}
.corner-ribbon.red{background: #C83937;}
.corner-ribbon.orange{background: #e82;}
.corner-ribbon.yellow{background: #ec0;}

.extra-info-cont.stripes{
	padding:8px 10px;
	font-size: 14px;
	font-weight: 400;
	line-height: 20px;
	letter-spacing: -0.02em;
}
.extra-info-cont .col {

}

.actions-cont {
	text-align:right;
}
.price-col {
	text-align:right;
}
.price-cont {
	display:inline-block;
	min-width:100px;
	text-align:center;
	padding-bottom:10px;
}

.info-cont {


}

ul.info-list {
	padding:5px 0 5px 0;
	-webkit-column-count: 2; /* Chrome, Safari, Opera */
    -moz-column-count: 2; /* Firefox */
    column-count: 2;
	-webkit-column-gap: 10px; /* Chrome, Safari, Opera */
    -moz-column-gap: 10px; /* Firefox */
    column-gap: 10px;
}

ul.info-list li{
	padding:0 0 0 30px;
	display:block;
	position:relative;
	margin:0 0 3px 0;
	min-height:16px;
}
ul.info-list li .info-icon {
	position:absolute;
	top:0;
	left:0;
	font-size: 14px;
	width:20px;
	text-align:center;
}
ul.info-list li .info-icon i{
	font-size: 14px;
}

.listing-item-info {

}

/* Price Includes */
.price-includes-cont {
	border-top: 4px solid orange;
}
.price-includes-cont h3 {
	text-align:left;
	font-size:28px;
	margin-bottom:20px;
	padding-top:10px;

}
.price-includes-cont h4 {
	text-align:left;
	font-size:16px;
	margin-bottom:0px;
	padding-top:20px;
}
.price-includes-cont h4 a,
.price-includes-cont h4 a i{
	color:#3498DB;
}
.price-includes-cont h4 a:hover {
	color:#3498DB;
}
.price-includes-cont i {
	color:orange;

}
.price-includes-cont .column_1_3{
	position:relative;
	padding-left:15px;
	line-height:1.4;
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	box-sizing: border-box;
}
.price-includes-cont .column_1_3::before{
	display:inline-block;
	content:'\f105';
	font-family:'FontAwesome';
	color:orange;
	position:absolute;
	top:7px;
	left:0;
	font-weight:bold;
}

.mfp-iframe-holder header {
	display:none!important;
}


@media only screen and (max-width: 980px) {
	ul.info-list {
		-webkit-column-count: 1; /* Chrome, Safari, Opera */
		-moz-column-count: 1; /* Firefox */
		column-count: 1;
	}

	.listing-item .media .img {display:block; clear:both; margin:0px!important; }
	.listing-item .media .img img {width:100%;}
	.listing-item .media .bd {display:block; clear:both;}

	.extra-info-cont.stripes {
		font-size:14px;
	}

	.corner-ribbon{
		width: 200px;
		line-height: 30px;
		top: 15px;
		right: -65px;
		left: auto;
		font-size: 12px;
	}

}

@media only screen and (max-width: 479px) {

	.corner-ribbon{
		font-size: 11px;
	}
	.extra-info-cont.stripes {
		font-size:14px;
	}
	.extra-info-cont .row-flex {
		display:block;

	}
	.extra-info-cont .row-flex > div{
		display:block;
		float:none;
		text-align:left;
	}
	.price-cont {
		min-width:auto;
	}
}


/*Accordion Component*/
.accordion__el {
	margin-bottom: .5em;
	border-bottom:1px solid #2e2f30;
}
.accordion__inner {
	padding: 5px 0 20px 0;
	overflow: hidden;
	display: none;
	line-height:1.5;
	/*background-color: rgba(255, 255, 255, 1);*/
	/*-webkit-box-shadow: 0px 0px 5px 1px rgba(0, 0, 0, 3.25);*/
	/*-moz-box-shadow: 0px 0px 5px 1px rgba(0, 0, 0, 0.35);*/
	/*box-shadow: 0px 0px 5px 1px rgba(0, 0, 0, 0.06);*/
}
a.accordion__toggle {
	max-width: 100%;
	display: block;
	/*background: #2e2f30;*/

	color: #2e2f30;
	padding: 10px 30px 15px 0px;
	/*border-radius: 0.15em;*/
	/*transition: background .3s ease;*/
	transition:all 300ms ease;
	position:relative;
	font-weight:500;
}
a.accordion__toggle:focus {
	outline:none!important;
}
a.accordion__toggle::before {
	font-family:FontAwesome;
	content:'\f107';
	position:absolute;
	top:13px;
	right:10px;
	font-size:28px;
	color:#FEC206;
}
a.accordion__toggle.active {
	border-bottom:0;
}
a.accordion__toggle.active::before {
	content:'\f106';
	top:13px;
}
a.accordion__toggle:hover {
	 //padding-left:20px;
 }

