.text-white {
    color: #fff;
}

.text-grey {
    color: #80807f;
}
.inline-block{
    display: inline-block;
}

.parallax-mirror {
    z-index:1
}

.parallax-window {
    background: transparent;
    padding:47px;
}

.parallax-window.big-padding{
    padding: 70px;
}

.parallax-heading {
    font-size: 30px;
    font-weight: 600;
}

.parallax-p {
    font-weight: 500;
    line-height: 1.3;
}

/* overlay at start */
.mfp-fade.mfp-bg {
    opacity: 0;

    -webkit-transition: all 0.15s ease-out;
    -moz-transition: all 0.15s ease-out;
    transition: all 0.15s ease-out;
}
/* overlay animate in */
.mfp-fade.mfp-bg.mfp-ready {
    opacity: 0.8;
}
/* overlay animate out */
.mfp-fade.mfp-bg.mfp-removing {
    opacity: 0;
}

/* content at start */
.mfp-fade.mfp-wrap .mfp-content {
    opacity: 0;

    -webkit-transition: all 0.15s ease-out;
    -moz-transition: all 0.15s ease-out;
    transition: all 0.15s ease-out;
}
/* content animate it */
.mfp-fade.mfp-wrap.mfp-ready .mfp-content {
    opacity: 1;
}
/* content animate out */
.mfp-fade.mfp-wrap.mfp-removing .mfp-content {
    opacity: 0;
}

section.places_map {
    border-top: 4px solid #0000000f;
    /*border-bottom: 1px solid #0000001f;*/
}

section.places_map .gmap_canvas {
    background-color: #0000000f;
}

.places-meeting_points ul li {
    font-weight: 600;
    margin-bottom: 12px;
    cursor: default;
}

/* Widescreen */
@media screen and (min-width: 1300px) {
    .places-meeting_points ul li {
        font-size: 19px;
    }
}

@media screen and (min-width: 981px) and (max-width: 1299px){
    .places-meeting_points ul li {
        font-size: 18px;
    }
}

/* Tablet */
@media screen and (min-width: 768px) and (max-width: 980px) {
    .places-meeting_points ul li {
        font-size: 18px;
    }
}

/* Mobile - Portrait */
@media screen and (max-width: 767px) {
    .places-meeting_points ul li {
        font-size: 18px;
    }
}



.places-meeting_points h2 {
    margin-bottom: 10px;
}