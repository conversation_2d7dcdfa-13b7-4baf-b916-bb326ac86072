.theme-config {
    position: fixed;
    z-index: 999999;
    top: 80px;
    right: -200px;
    width: 200px;
    background: #272727;
}
.theme-config .theme-config-wrap {
	padding: 0 10px 0 10px;
}
.theme-config .theme-config-head {
    margin: 0;
    padding: 0 0 0 10px;
    font-size: 15px;
    line-height: 40px;
    color: #ffffff;
    background-color: #232323;
}
.theme-config .theme-config-head a {
    display: block;
    position: absolute;
    top: 0;
    left: -40px;
    height: 40px;
    width: 40px;
    font-size: 20px;
    line-height: 40px;
    text-align: center;
    background-color: #232323;
    color: #ffffff;
    overflow: hidden;
}
.theme-config .theme-config-head a i {
	margin-left: 3px;
    font-size: 22px;
    line-height: 40px;
    color: #ffffff;
}
.theme-config .theme-config-title {
	margin: 0;
    padding: 0;
    font-size: 13px;
    font-weight: normal;
    line-height: 40px;
    color: #e9e9e9;
}
.theme-config .options {
    list-style: none;
}
.theme-config .options li {}
.theme-config .options li a {
    cursor: pointer;
    font-size: 12px;
}
.theme-config .options.colors {
    padding-top: 1px;
    margin: -1px 0 -5px -5px;
    overflow: hidden;
}
.theme-config .options.colors li {
    float: left;
    width: 31px;
    height: 31px;
    margin: 0 0 5px 5px;
    text-align: center;
}
.theme-config .options.colors li a {
    display: block;
    margin: 0;
    padding: 10px;
    height: 30px;
    font-size: 10px;
    color: #ffffff;
    /*hide the text*/
    text-indent: 9999px;
    white-space: nowrap;
    overflow: hidden;
    word-wrap: normal;
    border: solid 3px transparent;
    border: solid 3px rgba(255, 255, 255, 0.3);
}
.theme-config .options.colors li a:hover {
    /*-webkit-box-shadow: 0 0 0 1px #ffffff;*/
    /*box-shadow: 0 0 0 1px #ffffff;*/
    border: solid 3px #ffffff;
}

.theme-config .options.layouts {
    margin: 0 0 0 -5px;
    overflow: hidden;
}
.theme-config .options.layouts li {
    float: left;
    margin: 0 0 10px 5px;
    width: 87px;
}
.theme-config .options.layouts li a {
    cursor: pointer;
    display: block;
    height: auto;
    font-size: 10px;
    line-height: 35px;
    width: 100%;
    font-weight: bold;
    text-align: center;
    text-transform: uppercase;
    border: solid 3px #232323;
    border: solid 3px rgba(255, 255, 255, 0.3);
    background-color: #232323 !important;
    color: #ffffff;
}
.theme-config .options.layouts li a:focus,
.theme-config .options.layouts li a:hover {
    border: solid 3px #ffffff;
    background-color: #232323 !important;
    color: #ffffff;
}

.theme-config .options.reset-settings {margin: 0 0 10px 0;}
.theme-config .options.reset-settings li {}
.theme-config .options.reset-settings li a {color: #7f7f7f;}
.theme-config .options.reset-settings li a:hover {color: #ffffff;}

.theme-config-divider {
    margin: 5px 0;
    border-top-color: #232323;
    border-top-width: 3px;
}