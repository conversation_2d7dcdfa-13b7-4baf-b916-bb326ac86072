{"version": 3, "sources": ["swiper.js"], "names": ["addLibraryPlugin", "lib", "fn", "swiper", "params", "firstInstance", "this", "each", "s", "Swiper", "window", "container", "isH", "direction", "autoplay", "autoplayTimeoutId", "setTimeout", "loop", "fixLoop", "_slideNext", "isEnd", "autoplayStopOnLast", "stopAutoplay", "_slideTo", "findElementInEvent", "e", "selector", "el", "$", "target", "is", "parents", "nodeType", "found", "index", "_el", "undefined", "length", "initObserver", "options", "ObserverFunc", "MutationObserver", "WebkitMutationObserver", "observer", "mutations", "for<PERSON>ach", "mutation", "onResize", "emit", "observe", "attributes", "childList", "characterData", "observers", "push", "handleKeyboard", "originalEvent", "kc", "keyCode", "charCode", "shift<PERSON>ey", "altKey", "ctrl<PERSON>ey", "metaKey", "document", "activeElement", "nodeName", "toLowerCase", "inView", "windowScroll", "left", "pageXOffset", "top", "pageYOffset", "windowWidth", "innerWidth", "windowHeight", "innerHeight", "swiperOffset", "offset", "swiperCoord", "width", "height", "i", "point", "preventDefault", "returnValue", "slideNext", "slidePrev", "handleMousewheel", "we", "_wheelEvent", "delta", "detail", "mousewheelForceToAxis", "Math", "abs", "wheelDeltaX", "wheelDeltaY", "wheelDelta", "deltaX", "deltaY", "freeMode", "position", "getWrapperTranslate", "maxTranslate", "setWrapperTransition", "setWrapperTranslate", "updateProgress", "updateActiveIndex", "Date", "getTime", "_lastWheelScrollTime", "setParallaxTransform", "progress", "p", "pX", "pY", "attr", "indexOf", "parseInt", "transform", "normalizeEventName", "eventName", "toUpperCase", "substring", "defaults", "touchEventsTarget", "initialSlide", "speed", "autoplayDisableOnInteraction", "freeModeMomentum", "freeModeMomentumRatio", "freeModeMomentumBounce", "freeModeMomentumBounceRatio", "setWrapperSize", "virtualTranslate", "effect", "coverflow", "rotate", "stretch", "depth", "modifier", "slideShadows", "cube", "shadow", "shadowOffset", "shadowScale", "fade", "crossFade", "parallax", "scrollbar", "scrollbarHide", "keyboardControl", "mousewheelControl", "<PERSON><PERSON><PERSON>", "spaceBetween", "<PERSON><PERSON><PERSON><PERSON>iew", "slidesPerColumn", "slidesPerColumnFill", "slidesPerGroup", "centeredSlides", "touchRatio", "touchAngle", "simulate<PERSON>ouch", "shortSwipes", "longSwipes", "longSwipesRatio", "longSwipesMs", "follow<PERSON><PERSON>", "onlyEx<PERSON><PERSON>", "threshold", "touchMoveStopPropagation", "pagination", "paginationClickable", "paginationHide", "paginationBulletRender", "resistance", "resistanceRatio", "nextButton", "prevButton", "watchSlidesProgress", "watchSlidesVisibility", "grabCursor", "preventClicks", "preventClicksPropagation", "slideToClickedSlide", "lazyLoading", "lazyLoadingInPrevNext", "lazyLoadingOnTransitionStart", "preloadImages", "updateOnImagesReady", "loopAdditionalSlides", "loopedSlides", "control", "controlInverse", "allowSwipeToPrev", "allowSwipeToNext", "swi<PERSON><PERSON><PERSON><PERSON>", "noSwiping", "noSwipingClass", "slideClass", "slideActiveClass", "slideVisibleClass", "slideDuplicateClass", "slideNextClass", "slidePrevClass", "wrapperClass", "bulletClass", "bulletActiveClass", "buttonDisabledClass", "paginationHiddenClass", "observeParents", "a11y", "prevSlideMessage", "nextSlideMessage", "firstSlideMessage", "lastSlideMessage", "runCallbacksOnInit", "initalVirtualTranslate", "def", "deepDef", "classNames", "Dom7", "Zepto", "j<PERSON><PERSON><PERSON>", "data", "support", "flexbox", "transforms3d", "touch", "wrapper", "children", "paginationContainer", "addClass", "rtl", "dir", "css", "wrongRTL", "device", "android", "join", "translate", "velocity", "lockSwipeToNext", "lockSwipeToPrev", "lockSwipes", "unlockSwipeToNext", "unlockSwipeToPrev", "unlockSwipes", "style", "cursor", "imagesToLoad", "imagesLoaded", "loadImage", "imgElement", "src", "checkForComplete", "callback", "onReady", "image", "complete", "Image", "onload", "onerror", "_onReady", "update", "find", "currentSrc", "getAttribute", "autoplaying", "autoplayPaused", "startAutoplay", "clearTimeout", "pauseAutoplay", "transitionEnd", "minTranslate", "snapGrid", "updateContainerSize", "clientWidth", "clientHeight", "size", "updateSlidesSize", "slides", "slidesGrid", "slidesSizesGrid", "slidePosition", "prevSlideSize", "parseFloat", "replace", "virtualSize", "marginLeft", "marginTop", "marginRight", "marginBottom", "slidesNumberEvenToRows", "floor", "ceil", "slideSize", "slide", "eq", "newSlideOrderIndex", "column", "row", "slidesPerRow", "-webkit-box-ordinal-group", "-moz-box-ordinal-group", "-ms-flex-order", "-webkit-order", "order", "margin-top", "outerWidth", "outerHeight", "swiperSlideSize", "max", "newSlidesGrid", "updateSlidesOffset", "swiperSlideOffset", "offsetLeft", "offsetTop", "updateSlidesProgress", "offsetCenter", "getBoundingClientRect", "removeClass", "slideCenterOffset", "slideProgress", "slideBefore", "slideAfter", "isVisible", "translatesDiff", "isBeginning", "newActiveIndex", "snapIndex", "activeIndex", "previousIndex", "updateClasses", "activeSlide", "next", "prev", "bullets", "bulletIndex", "disable", "enable", "updatePagination", "bulletsHTML", "numberOfBullets", "html", "updateTranslate", "forceSetTranslate", "newTranslate", "min", "set", "translated", "slideTo", "desktopEvents", "navigator", "pointer<PERSON><PERSON>bled", "msPointer<PERSON><PERSON><PERSON>", "touchEvents", "start", "move", "end", "initEvents", "detach", "actionDom", "action", "moveCapture", "nested", "browser", "ie", "onTouchStart", "onTouchMove", "onTouchEnd", "ios", "onClickNext", "onEnterKey", "onClickPrev", "onClickIndex", "attachEvents", "detachEvents", "allowClick", "stopPropagation", "stopImmediatePropagation", "updateClickedSlide", "clickedSlide", "clickedIndex", "realIndex", "slideToIndex", "duplicatedSlides", "isTouched", "isMoved", "touchStartTime", "isScrolling", "currentTranslate", "startTranslate", "allowThresholdMove", "clickTimeout", "allowMomentumBounce", "formElements", "lastClickTime", "now", "velocities", "animating", "touches", "startX", "startY", "currentX", "currentY", "diff", "isTouchEvent", "startMoving", "type", "which", "targetTouches", "pageX", "pageY", "swipeDirection", "blur", "preventedByNestedSwiper", "atan2", "PI", "ieTouch", "trigger", "disableParentSwiper", "pow", "time", "touchEndTime", "timeDiff", "hasClass", "toggleClass", "currentPos", "lastMoveEvent", "pop", "velocityEvent", "distance", "momentumDuration", "momentumDistance", "newPosition", "afterBouncePosition", "doBounce", "bounceAmount", "onTransitionStart", "onTransitionEnd", "stopIndex", "groupSize", "ratio", "slideIndex", "runCallbacks", "internal", "lazy", "setHash", "clientLeft", "_slidePrev", "slideReset", "duration", "byController", "transition", "effects", "setTransition", "controller", "x", "y", "z", "setTranslate", "getTranslate", "axis", "matrix", "curTransform", "curStyle", "transformMatrix", "getComputedStyle", "WebKitCSSMatrix", "webkitTransform", "MozTransform", "OTransform", "MsTransform", "msTransform", "getPropertyValue", "toString", "split", "m41", "m42", "initObservers", "containerParents", "disconnectObservers", "disconnect", "createLoop", "remove", "prependSlides", "appendSlides", "append", "cloneNode", "prepend", "destroyLoop", "removeAttr", "newIndex", "appendSlide", "prependSlide", "removeSlide", "slidesIndexes", "indexToRemove", "removeAllSlides", "fadeIndex", "tx", "ty", "slideOpacity", "opacity", "triggerEvents", "cubeShadow", "wrapperRotate", "slideAngle", "round", "tz", "shadowBefore", "shadowAfter", "-webkit-transform-origin", "-moz-transform-origin", "-ms-transform-origin", "transform-origin", "shadowAngle", "multiplier", "sin", "cos", "scale1", "scale2", "zFactor", "<PERSON><PERSON><PERSON><PERSON>", "isUiWebView", "center", "slideOffset", "offsetMultiplier", "rotateY", "rotateX", "translateZ", "translateY", "translateX", "slideTransform", "zIndex", "ws", "<PERSON><PERSON><PERSON><PERSON>", "initialImageLoaded", "loadImageInSlide", "img", "_img", "load", "nextSlide", "prevSlide", "sb", "track", "drag", "trackSize", "offsetWidth", "offsetHeight", "divider", "moveDivider", "dragSize", "display", "newPos", "newSize", "timeout", "controlledTranslate", "controlled", "isArray", "init", "initialized", "hash", "location", "slideHash", "disableKeyboardControl", "off", "enableKeyboardControl", "on", "onmousew<PERSON><PERSON>", "WheelEvent", "disableMousewheelControl", "enableMousewheelControl", "parallaxDuration", "_plugins", "plugin", "plugins", "callPlugins", "arguments", "emitterEventListeners", "handler", "splice", "once", "_handler", "makeFocusable", "$el", "tabIndex", "addRole", "role", "addLabel", "label", "event", "notify", "lastSlideMsg", "nextSlideMsg", "firstSlideMsg", "prevSlideMsg", "liveRegion", "message", "notification", "destroy", "cleanupStyles", "deleteInstance", "prototype", "ua", "userAgent", "test", "arr", "Object", "apply", "msMaxTouchPoints", "maxTouchPoints", "match", "ipad", "iphone", "Modernizr", "DocumentTouch", "csstransforms3d", "div", "createElement", "styles", "_this", "context", "els", "tempParent", "trim", "toCreate", "innerHTML", "childNodes", "querySelectorAll", "getElementById", "className", "classes", "j", "classList", "add", "contains", "toggle", "attrs", "value", "setAttribute", "attrName", "removeAttribute", "key", "dataKey", "dom7ElementDataStorage", "elStyle", "webkitTransitionDuration", "MsTransitionDuration", "msTransitionDuration", "MozTransitionDuration", "OTransitionDuration", "transitionDuration", "targetSelector", "listener", "capture", "handleLiveEvent", "call", "k", "events", "addEventListener", "dom7LiveListeners", "liveListener", "removeEventListener", "proxy", "dom", "eventData", "evt", "CustomEvent", "bubbles", "cancelable", "createEvent", "initEvent", "dispatchEvent", "fireCallBack", "<PERSON><PERSON><PERSON><PERSON>", "box", "body", "clientTop", "scrollTop", "scrollLeft", "props", "prop", "compareWith", "matches", "webkitMatchesSelector", "mozMatchesSelector", "msMatchesSelector", "child", "previousSibling", "returnIndex", "<PERSON><PERSON><PERSON><PERSON>", "tempDiv", "<PERSON><PERSON><PERSON><PERSON>", "append<PERSON><PERSON><PERSON>", "insertBefore", "before", "parentNode", "insertAfter", "after", "nextS<PERSON>ling", "nextElement<PERSON><PERSON>ling", "nextAll", "nextEls", "previousElementSibling", "prevAll", "prevEls", "parent", "unique", "foundElements", "<PERSON><PERSON><PERSON><PERSON>", "toAdd", "swiperDomPlugins", "domLib", "module", "exports", "define", "amd"], "mappings": "CAcA,WACI,YAq/GA,SAASA,GAAiBC,GACtBA,EAAIC,GAAGC,OAAS,SAAUC,GACtB,GAAIC,EAKJ,OAJAJ,GAAIK,MAAMC,KAAK,WACX,GAAIC,GAAI,GAAIC,QAAOH,KAAMF,EACpBC,KAAeA,EAAgBG,KAEjCH,GAx/GfK,OAAOD,OAAS,SAAUE,EAAWP,GAuRjC,QAASQ,KACL,MAA8B,eAAvBJ,EAAEJ,OAAOS,UAgHpB,QAASC,KACLN,EAAEO,kBAAoBC,WAAW,WACzBR,EAAEJ,OAAOa,MACTT,EAAEU,UACFV,EAAEW,cAGGX,EAAEY,MAIEhB,EAAOiB,mBAIRb,EAAEc,eAHFd,EAAEe,SAAS,GAJff,EAAEW,cAWXX,EAAEJ,OAAOU,UAujBhB,QAASU,GAAmBC,EAAGC,GAC3B,GAAIC,GAAKC,EAAEH,EAAEI,OACb,KAAKF,EAAGG,GAAGJ,GACP,GAAwB,gBAAbA,GACPC,EAAKA,EAAGI,QAAQL,OAEf,IAAIA,EAASM,SAAU,CACxB,GAAIC,EAIJ,OAHAN,GAAGI,UAAUxB,KAAK,SAAU2B,EAAOC,GAC3BA,IAAQT,IAAUO,EAAQP,KAE7BO,EACOP,EADOU,OAI3B,MAAkB,KAAdT,EAAGU,OACID,OAEJT,EAAG,GAmsBd,QAASW,GAAaT,EAAQU,GAC1BA,EAAUA,KAEV,IAAIC,GAAe9B,OAAO+B,kBAAoB/B,OAAOgC,uBACjDC,EAAW,GAAIH,GAAa,SAAUI,GACtCA,EAAUC,QAAQ,SAAUC,GACxBtC,EAAEuC,WACFvC,EAAEwC,KAAK,mBAAoBxC,EAAGsC,MAItCH,GAASM,QAAQpB,GACbqB,WAA0C,mBAAvBX,GAAQW,YAA6B,EAAOX,EAAQW,WACvEC,UAAwC,mBAAtBZ,GAAQY,WAA4B,EAAOZ,EAAQY,UACrEC,cAAgD,mBAA1Bb,GAAQa,eAAgC,EAAOb,EAAQa,gBAGjF5C,EAAE6C,UAAUC,KAAKX,GAkmBrB,QAASY,GAAe9B,GAChBA,EAAE+B,gBAAe/B,EAAIA,EAAE+B,cAC3B,IAAIC,GAAKhC,EAAEiC,SAAWjC,EAAEkC,QACxB,MAAIlC,EAAEmC,UAAYnC,EAAEoC,QAAUpC,EAAEqC,SAAWrC,EAAEsC,SAGzCC,SAASC,eAAiBD,SAASC,cAAcC,WAA+D,UAAlDF,SAASC,cAAcC,SAASC,eAA+E,aAAlDH,SAASC,cAAcC,SAASC,gBAA/J,CAGA,GAAW,KAAPV,GAAoB,KAAPA,GAAoB,KAAPA,GAAoB,KAAPA,EAAW,CAClD,GAAIW,IAAS,CAEb,IAAI5D,EAAEG,UAAUoB,QAAQ,iBAAiBM,OAAS,GAA4D,IAAvD7B,EAAEG,UAAUoB,QAAQ,wBAAwBM,OAC/F,MAgBJ,KAAK,GAdDgC,IACAC,KAAM5D,OAAO6D,YACbC,IAAK9D,OAAO+D,aAEZC,EAAchE,OAAOiE,WACrBC,EAAelE,OAAOmE,YACtBC,EAAetE,EAAEG,UAAUoE,SAE3BC,IACCF,EAAaR,KAAMQ,EAAaN,MAChCM,EAAaR,KAAO9D,EAAEyE,MAAOH,EAAaN,MAC1CM,EAAaR,KAAMQ,EAAaN,IAAMhE,EAAE0E,SACxCJ,EAAaR,KAAO9D,EAAEyE,MAAOH,EAAaN,IAAMhE,EAAE0E,SAE9CC,EAAI,EAAGA,EAAIH,EAAY3C,OAAQ8C,IAAK,CACzC,GAAIC,GAAQJ,EAAYG,EAEpBC,GAAM,IAAMf,EAAaC,MAAQc,EAAM,IAAMf,EAAaC,KAAOI,GACjEU,EAAM,IAAMf,EAAaG,KAAOY,EAAM,IAAMf,EAAaG,IAAMI,IAE/DR,GAAS,GAIjB,IAAKA,EAAQ,OAEbxD,MACW,KAAP6C,GAAoB,KAAPA,KACThC,EAAE4D,eAAgB5D,EAAE4D,iBACnB5D,EAAE6D,aAAc,GAEd,KAAP7B,GAAWjD,EAAE+E,YACN,KAAP9B,GAAWjD,EAAEgF,eAGN,KAAP/B,GAAoB,KAAPA,KACThC,EAAE4D,eAAgB5D,EAAE4D,iBACnB5D,EAAE6D,aAAc,GAEd,KAAP7B,GAAWjD,EAAE+E,YACN,KAAP9B,GAAWjD,EAAEgF,cA8BzB,QAASC,GAAiBhE,GAClBA,EAAE+B,gBAAe/B,EAAIA,EAAE+B,cAC3B,IAAIkC,GAAKlF,EAAEmF,YACPC,EAAQ,CAEZ,IAAInE,EAAEoE,OAAQD,GAASnE,EAAEoE,WAEpB,IAAW,eAAPH,EACL,GAAIlF,EAAEJ,OAAO0F,sBACT,GAAIlF,IAAO,CACP,KAAImF,KAAKC,IAAIvE,EAAEwE,aAAeF,KAAKC,IAAIvE,EAAEyE,cACpC,MADkDN,GAAQnE,EAAEwE,gBAGhE,CACD,KAAIF,KAAKC,IAAIvE,EAAEyE,aAAeH,KAAKC,IAAIvE,EAAEwE,cACpC,MADkDL,GAAQnE,EAAEyE,gBAKrEN,GAAQnE,EAAE0E,eAIb,IAAW,mBAAPT,EAAyBE,GAASnE,EAAEoE,WAExC,IAAW,UAAPH,EACL,GAAIlF,EAAEJ,OAAO0F,sBACT,GAAIlF,IAAO,CACP,KAAImF,KAAKC,IAAIvE,EAAE2E,QAAUL,KAAKC,IAAIvE,EAAE4E,SAC/B,MADwCT,IAASnE,EAAE2E,WAGvD,CACD,KAAIL,KAAKC,IAAIvE,EAAE4E,QAAUN,KAAKC,IAAIvE,EAAE2E,SAC/B,MADwCR,IAASnE,EAAE4E,WAK5DT,GAAQG,KAAKC,IAAIvE,EAAE2E,QAAUL,KAAKC,IAAIvE,EAAE4E,SAAY5E,EAAE2E,QAAW3E,EAAE4E,MAI3E,IAAK7F,EAAEJ,OAAOkG,SAQT,CAED,GAAIC,GAAW/F,EAAEgG,sBAAwBZ,CAWzC,IATIW,EAAW,IAAGA,EAAW,GACzBA,EAAW/F,EAAEiG,iBAAgBF,EAAW/F,EAAEiG,gBAE9CjG,EAAEkG,qBAAqB,GACvBlG,EAAEmG,oBAAoBJ,GACtB/F,EAAEoG,iBACFpG,EAAEqG,oBAGe,IAAbN,GAAkBA,IAAa/F,EAAEiG,eAAgB,YApBjD,GAAKK,OAAQC,UAAYvG,EAAEwG,qBAAuB,KACtC,EAARpB,EAAWpF,EAAE+E,YACZ/E,EAAEgF,aAEXhF,EAAEwG,sBAAuB,GAAKF,OAAQC,SAsB1C,OAJIvG,GAAEJ,OAAOU,UAAUN,EAAEc,eAErBG,EAAE4D,eAAgB5D,EAAE4D,iBACnB5D,EAAE6D,aAAc,GACd,EAiBX,QAAS2B,GAAqBtF,EAAIuF,GAC9BvF,EAAKC,EAAED,EACP,IAAIwF,GAAGC,EAAIC,CAEXF,GAAIxF,EAAG2F,KAAK,yBAA2B,IACvCF,EAAKzF,EAAG2F,KAAK,0BACbD,EAAK1F,EAAG2F,KAAK,0BACTF,GAAMC,GACND,EAAKA,GAAM,IACXC,EAAKA,GAAM,KAGPzG,KACAwG,EAAKD,EACLE,EAAK,MAGLA,EAAKF,EACLC,EAAK,KAITA,EADA,EAAKG,QAAQ,MAAQ,EAChBC,SAASJ,EAAI,IAAMF,EAAW,IAG9BE,EAAKF,EAAW,KAGrBG,EADA,EAAKE,QAAQ,MAAQ,EAChBC,SAASH,EAAI,IAAMH,EAAW,IAG9BG,EAAKH,EAAW,KAEzBvF,EAAG8F,UAAU,eAAiBL,EAAK,KAAOC,EAAK,SAgDnD,QAASK,GAAoBC,GASzB,MARgC,KAA5BA,EAAUJ,QAAQ,QAEdI,EADAA,EAAU,KAAOA,EAAU,GAAGC,cAClB,KAAOD,EAAU,GAAGC,cAAgBD,EAAUE,UAAU,GAGxD,KAAOF,GAGpBA,EA7hFX,KAAMrH,eAAgBG,SAAS,MAAO,IAAIA,QAAOE,EAAWP,EAE5D,IAAI0H,IACAjH,UAAW,aACXkH,kBAAmB,YACnBC,aAAc,EACdC,MAAO,IAEPnH,UAAU,EACVoH,8BAA8B,EAE9B5B,UAAU,EACV6B,kBAAkB,EAClBC,sBAAuB,EACvBC,wBAAwB,EACxBC,4BAA6B,EAE7BC,gBAAgB,EAEhBC,kBAAkB,EAElBC,OAAQ,QACRC,WACIC,OAAQ,GACRC,QAAS,EACTC,MAAO,IACPC,SAAU,EACVC,cAAe,GAEnBC,MACID,cAAc,EACdE,QAAQ,EACRC,aAAc,GACdC,YAAa,KAEjBC,MACIC,WAAW,GAGfC,UAAU,EAEVC,UAAW,KACXC,eAAe,EAEfC,iBAAiB,EACjBC,mBAAmB,EACnB5D,uBAAuB,EAEvB6D,SAAS,EAETC,aAAc,EACdC,cAAe,EACfC,gBAAiB,EACjBC,oBAAqB,SACrBC,eAAgB,EAChBC,gBAAgB,EAEhBC,WAAY,EACZC,WAAY,GACZC,eAAe,EACfC,aAAa,EACbC,YAAY,EACZC,gBAAiB,GACjBC,aAAc,IACdC,cAAc,EACdC,cAAc,EACdC,UAAW,EACXC,0BAA0B,EAE1BC,WAAY,KACZC,qBAAqB,EACrBC,gBAAgB,EAChBC,uBAAwB,KAExBC,YAAY,EACZC,gBAAiB,IAEjBC,WAAY,KACZC,WAAY,KAEZC,qBAAqB,EACrBC,uBAAuB,EAEvBC,YAAY,EAEZC,eAAe,EACfC,0BAA0B,EAC1BC,qBAAqB,EAErBC,aAAa,EACbC,uBAAuB,EACvBC,8BAA8B,EAE9BC,eAAe,EACfC,qBAAqB,EAErB9K,MAAM,EACN+K,qBAAsB,EACtBC,aAAc,KAEdC,QAAS9J,OACT+J,gBAAgB,EAEhBC,kBAAkB,EAClBC,kBAAkB,EAClBC,aAAc,KACdC,WAAW,EACXC,eAAgB,oBAEhBC,WAAY,eACZC,iBAAkB,sBAClBC,kBAAmB,uBACnBC,oBAAqB,yBACrBC,eAAgB,oBAChBC,eAAgB,oBAChBC,aAAc,iBACdC,YAAa,2BACbC,kBAAmB,kCACnBC,oBAAqB,yBACrBC,sBAAuB,2BAEvBxK,UAAU,EACVyK,gBAAgB,EAEhBC,MAAM,EACNC,iBAAkB,iBAClBC,iBAAkB,aAClBC,kBAAmB,0BACnBC,iBAAkB,yBAElBC,oBAAoB,GA8BpBC,EAAyBvN,GAAUA,EAAOoI,gBAE9CpI,GAASA,KACT,KAAK,GAAIwN,KAAO9F,GACZ,GAA2B,mBAAhB1H,GAAOwN,GACdxN,EAAOwN,GAAO9F,EAAS8F,OAEtB,IAA2B,gBAAhBxN,GAAOwN,GACnB,IAAK,GAAIC,KAAW/F,GAAS8F,GACW,mBAAzBxN,GAAOwN,GAAKC,KACnBzN,EAAOwN,GAAKC,GAAW/F,EAAS8F,GAAKC,GAOrD,IAAIrN,GAAIF,IAGRE,GAAEJ,OAASA,EAGXI,EAAEsN,aAIF,IAAIlM,EAOJ,IALIA,EADgB,mBAATmM,GACHrN,OAAOqN,MAAQrN,OAAOsN,OAAStN,OAAOuN,OAGtCF,EAEHnM,IAGLpB,EAAEoB,EAAIA,EAINpB,EAAEG,UAAYiB,EAAEjB,GACW,IAAvBH,EAAEG,UAAU0B,QAAhB,CACA,GAAI7B,EAAEG,UAAU0B,OAAS,EAIrB,WAHA7B,GAAEG,UAAUJ,KAAK,WACb,GAAIE,QAAOH,KAAMF,IAMzBI,GAAEG,UAAU,GAAGR,OAASK,EACxBA,EAAEG,UAAUuN,KAAK,SAAU1N,GAE3BA,EAAEsN,WAAWxK,KAAK,oBAAsB9C,EAAEJ,OAAOS,WAE7CL,EAAEJ,OAAOkG,UACT9F,EAAEsN,WAAWxK,KAAK,8BAEjB9C,EAAE2N,QAAQC,UACX5N,EAAEsN,WAAWxK,KAAK,+BAClB9C,EAAEJ,OAAO0J,gBAAkB,IAG3BtJ,EAAEJ,OAAOkJ,UAAY9I,EAAEJ,OAAOkL,yBAC9B9K,EAAEJ,OAAOiL,qBAAsB,IAG9B,OAAQ,aAAa9D,QAAQ/G,EAAEJ,OAAOqI,SAAW,IAC9CjI,EAAE2N,QAAQE,cACV7N,EAAEJ,OAAOiL,qBAAsB,EAC/B7K,EAAEsN,WAAWxK,KAAK,wBAGlB9C,EAAEJ,OAAOqI,OAAS,SAGF,UAApBjI,EAAEJ,OAAOqI,QACTjI,EAAEsN,WAAWxK,KAAK,oBAAsB9C,EAAEJ,OAAOqI,QAE7B,SAApBjI,EAAEJ,OAAOqI,SACTjI,EAAEJ,OAAO8K,gBAAkB,EAC3B1K,EAAEJ,OAAOyJ,cAAgB,EACzBrJ,EAAEJ,OAAO0J,gBAAkB,EAC3BtJ,EAAEJ,OAAO4J,eAAiB,EAC1BxJ,EAAEJ,OAAO6J,gBAAiB,EAC1BzJ,EAAEJ,OAAOwJ,aAAe,EACxBpJ,EAAEJ,OAAOoI,kBAAmB,EAC5BhI,EAAEJ,OAAOmI,gBAAiB,GAEN,SAApB/H,EAAEJ,OAAOqI,SACTjI,EAAEJ,OAAOyJ,cAAgB,EACzBrJ,EAAEJ,OAAO0J,gBAAkB,EAC3BtJ,EAAEJ,OAAO4J,eAAiB,EAC1BxJ,EAAEJ,OAAOiL,qBAAsB,EAC/B7K,EAAEJ,OAAOwJ,aAAe,EACc,mBAA3B+D,KACPnN,EAAEJ,OAAOoI,kBAAmB,IAKhChI,EAAEJ,OAAOmL,YAAc/K,EAAE2N,QAAQG,QACjC9N,EAAEJ,OAAOmL,YAAa,GAI1B/K,EAAE+N,QAAU/N,EAAEG,UAAU6N,SAAS,IAAMhO,EAAEJ,OAAO2M,cAG5CvM,EAAEJ,OAAOyK,aACTrK,EAAEiO,oBAAsB7M,EAAEpB,EAAEJ,OAAOyK,YAC/BrK,EAAEJ,OAAO0K,qBACTtK,EAAEiO,oBAAoBC,SAAS,gCAUvClO,EAAEmO,IAAM/N,MAA+C,QAArCJ,EAAEG,UAAU,GAAGiO,IAAIzK,eAA4D,QAAjC3D,EAAEG,UAAUkO,IAAI,cAC5ErO,EAAEmO,KACFnO,EAAEsN,WAAWxK,KAAK,wBAIlB9C,EAAEmO,MACFnO,EAAEsO,SAAwC,gBAA7BtO,EAAE+N,QAAQM,IAAI,YAI3BrO,EAAEJ,OAAO0J,gBAAkB,GAC3BtJ,EAAEsN,WAAWxK,KAAK,6BAIlB9C,EAAEuO,OAAOC,SACTxO,EAAEsN,WAAWxK,KAAK,4BAItB9C,EAAEG,UAAU+N,SAASlO,EAAEsN,WAAWmB,KAAK,MAGvCzO,EAAE0O,UAAY,EAGd1O,EAAE0G,SAAW,EAGb1G,EAAE2O,SAAW,EAGb3O,EAAE4O,gBAAkB,WAChB5O,EAAEJ,OAAOiM,kBAAmB,GAEhC7L,EAAE6O,gBAAkB,WAChB7O,EAAEJ,OAAOgM,kBAAmB,GAEhC5L,EAAE8O,WAAa,WACX9O,EAAEJ,OAAOiM,iBAAmB7L,EAAEJ,OAAOgM,kBAAmB,GAE5D5L,EAAE+O,kBAAoB,WAClB/O,EAAEJ,OAAOiM,kBAAmB,GAEhC7L,EAAEgP,kBAAoB,WAClBhP,EAAEJ,OAAOgM,kBAAmB,GAEhC5L,EAAEiP,aAAe,WACbjP,EAAEJ,OAAOiM,iBAAmB7L,EAAEJ,OAAOgM,kBAAmB,GAOxD5L,EAAEJ,OAAOmL,aACT/K,EAAEG,UAAU,GAAG+O,MAAMC,OAAS,OAC9BnP,EAAEG,UAAU,GAAG+O,MAAMC,OAAS,eAC9BnP,EAAEG,UAAU,GAAG+O,MAAMC,OAAS,YAC9BnP,EAAEG,UAAU,GAAG+O,MAAMC,OAAS,QAKlCnP,EAAEoP,gBACFpP,EAAEqP,aAAe,EAEjBrP,EAAEsP,UAAY,SAAUC,EAAYC,EAAKC,EAAkBC,GAEvD,QAASC,KACDD,GAAUA,IAFlB,GAAIE,EAICL,GAAWM,UAAaJ,EAWzBE,IAVIH,GACAI,EAAQ,GAAIE,OACZF,EAAMG,OAASJ,EACfC,EAAMI,QAAUL,EAChBC,EAAMJ,IAAMA,GAEZG,KAOZ3P,EAAEsL,cAAgB,WAEd,QAAS2E,KACY,mBAANjQ,IAA2B,OAANA,IACT4B,SAAnB5B,EAAEqP,cAA4BrP,EAAEqP,eAChCrP,EAAEqP,eAAiBrP,EAAEoP,aAAavN,SAC9B7B,EAAEJ,OAAO2L,qBAAqBvL,EAAEkQ,SACpClQ,EAAEwC,KAAK,gBAAiBxC,KANhCA,EAAEoP,aAAepP,EAAEG,UAAUgQ,KAAK,MASlC,KAAK,GAAIxL,GAAI,EAAGA,EAAI3E,EAAEoP,aAAavN,OAAQ8C,IACvC3E,EAAEsP,UAAUtP,EAAEoP,aAAazK,GAAK3E,EAAEoP,aAAazK,GAAGyL,YAAcpQ,EAAEoP,aAAazK,GAAG0L,aAAa,QAAS,EAAMJ,IAOtHjQ,EAAEO,kBAAoBqB,OACtB5B,EAAEsQ,aAAc,EAChBtQ,EAAEuQ,gBAAiB,EAsBnBvQ,EAAEwQ,cAAgB,WACd,MAAmC,mBAAxBxQ,GAAEO,mBAA0C,EAClDP,EAAEJ,OAAOU,SACVN,EAAEsQ,aAAoB,GAC1BtQ,EAAEsQ,aAAc,EAChBtQ,EAAEwC,KAAK,kBAAmBxC,OAC1BM,OAJ+B,GAMnCN,EAAEc,aAAe,WACRd,EAAEO,oBACHP,EAAEO,mBAAmBkQ,aAAazQ,EAAEO,mBACxCP,EAAEsQ,aAAc,EAChBtQ,EAAEO,kBAAoBqB,OACtB5B,EAAEwC,KAAK,iBAAkBxC,KAE7BA,EAAE0Q,cAAgB,SAAUjJ,GACpBzH,EAAEuQ,iBACFvQ,EAAEO,mBAAmBkQ,aAAazQ,EAAEO,mBACxCP,EAAEuQ,gBAAiB,EACL,IAAV9I,GACAzH,EAAEuQ,gBAAiB,EACnBjQ,KAGAN,EAAE+N,QAAQ4C,cAAc,WACpB3Q,EAAEuQ,gBAAiB,EACdvQ,EAAEsQ,YAIHhQ,IAHAN,EAAEc,mBAWlBd,EAAE4Q,aAAe,WACb,OAAS5Q,EAAE6Q,SAAS,IAExB7Q,EAAEiG,aAAe,WACb,OAASjG,EAAE6Q,SAAS7Q,EAAE6Q,SAAShP,OAAS,IAK5C7B,EAAE8Q,oBAAsB,WACpB9Q,EAAEyE,MAAQzE,EAAEG,UAAU,GAAG4Q,YACzB/Q,EAAE0E,OAAS1E,EAAEG,UAAU,GAAG6Q,aAC1BhR,EAAEiR,KAAO7Q,IAAQJ,EAAEyE,MAAQzE,EAAE0E,QAGjC1E,EAAEkR,iBAAmB,WACjBlR,EAAEmR,OAASnR,EAAE+N,QAAQC,SAAS,IAAMhO,EAAEJ,OAAOqM,YAC7CjM,EAAE6Q,YACF7Q,EAAEoR,cACFpR,EAAEqR,kBAEF,IAEI1M,GAFAyE,EAAepJ,EAAEJ,OAAOwJ,aACxBkI,EAAgB,EAEhBC,EAAgB,EAChB7P,EAAQ,CACgB,iBAAjB0H,IAA6BA,EAAarC,QAAQ,MAAQ,IACjEqC,EAAeoI,WAAWpI,EAAaqI,QAAQ,IAAK,KAAO,IAAMzR,EAAEiR,MAGvEjR,EAAE0R,aAAetI,EAENpJ,EAAEmR,OAAO9C,IAAhBrO,EAAEmO,KAAmBwD,WAAY,GAAIC,UAAW,KACjCC,YAAa,GAAIC,aAAc,IAElD,IAAIC,EACA/R,GAAEJ,OAAO0J,gBAAkB,IAEvByI,EADAxM,KAAKyM,MAAMhS,EAAEmR,OAAOtP,OAAS7B,EAAEJ,OAAO0J,mBAAqBtJ,EAAEmR,OAAOtP,OAAS7B,EAAEJ,OAAO0J,gBAC7DtJ,EAAEmR,OAAOtP,OAGT0D,KAAK0M,KAAKjS,EAAEmR,OAAOtP,OAAS7B,EAAEJ,OAAO0J,iBAAmBtJ,EAAEJ,OAAO0J,gBAKlG,IAAI4I,EACJ,KAAKvN,EAAI,EAAGA,EAAI3E,EAAEmR,OAAOtP,OAAQ8C,IAAK,CAClCuN,EAAY,CACZ,IAAIC,GAAQnS,EAAEmR,OAAOiB,GAAGzN,EACxB,IAAI3E,EAAEJ,OAAO0J,gBAAkB,EAAG,CAE9B,GAAI+I,GACAC,EAAQC,EAERC,EADAlJ,EAAkBtJ,EAAEJ,OAAO0J,eAEM,YAAjCtJ,EAAEJ,OAAO2J,qBACT+I,EAAS/M,KAAKyM,MAAMrN,EAAI2E,GACxBiJ,EAAM5N,EAAI2N,EAAShJ,EACnB+I,EAAqBC,EAASC,EAAMR,EAAyBzI,EAC7D6I,EACK9D,KACGoE,4BAA6BJ,EAC7BK,yBAA0BL,EAC1BM,iBAAkBN,EAClBO,gBAAiBP,EACjBQ,MAASR,MAIjBG,EAAeT,EAAyBzI,EACxCiJ,EAAMhN,KAAKyM,MAAMrN,EAAI6N,GACrBF,EAAS3N,EAAI4N,EAAMC,GAGvBL,EACK9D,KACGyE,aAAuB,IAARP,GAAavS,EAAEJ,OAAOwJ,cAAkBpJ,EAAEJ,OAAOwJ,aAAe,OAElFtC,KAAK,qBAAsBwL,GAC3BxL,KAAK,kBAAmByL,GAGJ,SAAzBJ,EAAM9D,IAAI,aACiB,SAA3BrO,EAAEJ,OAAOyJ,cACT6I,EAAY9R,IAAQ+R,EAAMY,YAAW,GAAQZ,EAAMa,aAAY,IAG/Dd,GAAalS,EAAEiR,MAAQjR,EAAEJ,OAAOyJ,cAAgB,GAAKD,GAAgBpJ,EAAEJ,OAAOyJ,cAC1EjJ,IACAJ,EAAEmR,OAAOxM,GAAGuK,MAAMzK,MAAQyN,EAAY,KAGtClS,EAAEmR,OAAOxM,GAAGuK,MAAMxK,OAASwN,EAAY,MAG/ClS,EAAEmR,OAAOxM,GAAGsO,gBAAkBf,EAC9BlS,EAAEqR,gBAAgBvO,KAAKoP,GAGnBlS,EAAEJ,OAAO6J,gBACT6H,EAAgBA,EAAgBY,EAAY,EAAIX,EAAgB,EAAInI,EAC1D,IAANzE,IAAS2M,EAAgBA,EAAgBtR,EAAEiR,KAAO,EAAI7H,GACtD7D,KAAKC,IAAI8L,GAAiB,OAAUA,EAAgB,GACpD,EAAUtR,EAAEJ,OAAO4J,iBAAmB,GAAGxJ,EAAE6Q,SAAS/N,KAAKwO,GAC7DtR,EAAEoR,WAAWtO,KAAKwO,KAGd,EAAUtR,EAAEJ,OAAO4J,iBAAmB,GAAGxJ,EAAE6Q,SAAS/N,KAAKwO,GAC7DtR,EAAEoR,WAAWtO,KAAKwO,GAClBA,EAAgBA,EAAgBY,EAAY9I,GAGhDpJ,EAAE0R,aAAeQ,EAAY9I,EAE7BmI,EAAgBW,EAEhBxQ,KAEJ1B,EAAE0R,YAAcnM,KAAK2N,IAAIlT,EAAE0R,YAAa1R,EAAEiR,KAE1C,IAAIkC,EAWJ,IARInT,EAAEmO,KAAOnO,EAAEsO,WAAiC,UAApBtO,EAAEJ,OAAOqI,QAA0C,cAApBjI,EAAEJ,OAAOqI,SAChEjI,EAAE+N,QAAQM,KAAK5J,MAAOzE,EAAE0R,YAAc1R,EAAEJ,OAAOwJ,aAAe,SAE7DpJ,EAAE2N,QAAQC,SAAW5N,EAAEJ,OAAOmI,iBACpB/H,EAAE+N,QAAQM,IAAjBjO,KAAsBqE,MAAOzE,EAAE0R,YAAc1R,EAAEJ,OAAOwJ,aAAe,OACrD1E,OAAQ1E,EAAE0R,YAAc1R,EAAEJ,OAAOwJ,aAAe,OAGpEpJ,EAAEJ,OAAO0J,gBAAkB,IAC3BtJ,EAAE0R,aAAeQ,EAAYlS,EAAEJ,OAAOwJ,cAAgB2I,EACtD/R,EAAE0R,YAAcnM,KAAK0M,KAAKjS,EAAE0R,YAAc1R,EAAEJ,OAAO0J,iBAAmBtJ,EAAEJ,OAAOwJ,aAC/EpJ,EAAE+N,QAAQM,KAAK5J,MAAOzE,EAAE0R,YAAc1R,EAAEJ,OAAOwJ,aAAe,OAC1DpJ,EAAEJ,OAAO6J,gBAAgB,CAEzB,IADA0J,KACKxO,EAAI,EAAGA,EAAI3E,EAAE6Q,SAAShP,OAAQ8C,IAC3B3E,EAAE6Q,SAASlM,GAAK3E,EAAE0R,YAAc1R,EAAE6Q,SAAS,IAAIsC,EAAcrQ,KAAK9C,EAAE6Q,SAASlM,GAErF3E,GAAE6Q,SAAWsC,EAKrB,IAAKnT,EAAEJ,OAAO6J,eAAgB,CAE1B,IADA0J,KACKxO,EAAI,EAAGA,EAAI3E,EAAE6Q,SAAShP,OAAQ8C,IAC3B3E,EAAE6Q,SAASlM,IAAM3E,EAAE0R,YAAc1R,EAAEiR,MACnCkC,EAAcrQ,KAAK9C,EAAE6Q,SAASlM,GAGtC3E,GAAE6Q,SAAWsC,EACT5N,KAAKyM,MAAMhS,EAAE0R,YAAc1R,EAAEiR,MAAQ1L,KAAKyM,MAAMhS,EAAE6Q,SAAS7Q,EAAE6Q,SAAShP,OAAS,KAC/E7B,EAAE6Q,SAAS/N,KAAK9C,EAAE0R,YAAc1R,EAAEiR,MAGhB,IAAtBjR,EAAE6Q,SAAShP,SAAc7B,EAAE6Q,UAAY,IAEb,IAA1B7Q,EAAEJ,OAAOwJ,cAEMpJ,EAAEmR,OAAO9C,IADpBjO,IACIJ,EAAEmO,KAAmBwD,WAAYvI,EAAe,OACjCyI,YAAazI,EAAe,OAEhC0I,aAAc1I,EAAe,OAEhDpJ,EAAEJ,OAAOiL,qBACT7K,EAAEoT,sBAGVpT,EAAEoT,mBAAqB,WACnB,IAAK,GAAIzO,GAAI,EAAGA,EAAI3E,EAAEmR,OAAOtP,OAAQ8C,IACjC3E,EAAEmR,OAAOxM,GAAG0O,kBAAoBjT,IAAQJ,EAAEmR,OAAOxM,GAAG2O,WAAatT,EAAEmR,OAAOxM,GAAG4O,WAOrFvT,EAAEwT,qBAAuB,SAAU9E,GAI/B,GAHyB,mBAAdA,KACPA,EAAY1O,EAAE0O,WAAa,GAEP,IAApB1O,EAAEmR,OAAOtP,OAAb,CAC6C,mBAAlC7B,GAAEmR,OAAO,GAAGkC,mBAAmCrT,EAAEoT,oBAE5D,IAAIK,GAAezT,EAAEJ,OAAO6J,gBAAkBiF,EAAY1O,EAAEiR,KAAO,GAAKvC,CACpE1O,GAAEmO,MAAKsF,EAAezT,EAAEJ,OAAO6J,eAAiBiF,EAAY1O,EAAEiR,KAAO,EAAIvC,EAG7E,EAAmB1O,EAAEG,UAAU,GAAGuT,wBACjBtT,IAAQ,OAAS,MAClBA,IAAQ,QAAU,SAClCJ,EAAEmR,OAAOwC,YAAY3T,EAAEJ,OAAOuM,kBAC9B,KAAK,GAAIxH,GAAI,EAAGA,EAAI3E,EAAEmR,OAAOtP,OAAQ8C,IAAK,CACtC,GAAIwN,GAAQnS,EAAEmR,OAAOxM,GACjBiP,EAAqB5T,EAAEJ,OAAO6J,kBAAmB,EAAQ0I,EAAMc,gBAAkB,EAAI,EACrFY,GAAiBJ,EAAetB,EAAMkB,kBAAoBO,IAAsBzB,EAAMc,gBAAkBjT,EAAEJ,OAAOwJ,aACrH,IAAIpJ,EAAEJ,OAAOkL,sBAAuB,CAChC,GAAIgJ,KAAgBL,EAAetB,EAAMkB,kBAAoBO,GACzDG,EAAaD,EAAc9T,EAAEqR,gBAAgB1M,GAC7CqP,EACCF,GAAe,GAAKA,EAAc9T,EAAEiR,MACpC8C,EAAa,GAAKA,GAAc/T,EAAEiR,MACnB,GAAf6C,GAAoBC,GAAc/T,EAAEiR,IACrC+C,IACAhU,EAAEmR,OAAOiB,GAAGzN,GAAGuJ,SAASlO,EAAEJ,OAAOuM,mBAGzCgG,EAAMzL,SAAW1G,EAAEmO,KAAO0F,EAAgBA,KAGlD7T,EAAEoG,eAAiB,SAAUsI,GACA,mBAAdA,KACPA,EAAY1O,EAAE0O,WAAa,EAE/B,IAAIuF,GAAiBjU,EAAEiG,eAAiBjG,EAAE4Q,cACnB,KAAnBqD,GACAjU,EAAE0G,SAAW,EACb1G,EAAEkU,YAAclU,EAAEY,OAAQ,IAG1BZ,EAAE0G,UAAYgI,EAAY1O,EAAE4Q,gBAAkB,EAC9C5Q,EAAEkU,YAAclU,EAAE0G,UAAY,EAC9B1G,EAAEY,MAAQZ,EAAE0G,UAAY,GAExB1G,EAAEkU,aAAalU,EAAEwC,KAAK,mBAAoBxC,GAC1CA,EAAEY,OAAOZ,EAAEwC,KAAK,aAAcxC,GAE9BA,EAAEJ,OAAOiL,qBAAqB7K,EAAEwT,qBAAqB9E,GACzD1O,EAAEwC,KAAK,aAAcxC,EAAGA,EAAE0G,WAE9B1G,EAAEqG,kBAAoB,WAClB,GACI8N,GAAgBxP,EAAGyP,EADnB1F,EAAY1O,EAAEmO,IAAMnO,EAAE0O,WAAa1O,EAAE0O,SAEzC,KAAK/J,EAAI,EAAGA,EAAI3E,EAAEoR,WAAWvP,OAAQ8C,IACE,mBAAxB3E,GAAEoR,WAAWzM,EAAI,GACpB+J,GAAa1O,EAAEoR,WAAWzM,IAAM+J,EAAY1O,EAAEoR,WAAWzM,EAAI,IAAM3E,EAAEoR,WAAWzM,EAAI,GAAK3E,EAAEoR,WAAWzM,IAAM,EAC5GwP,EAAiBxP,EAEZ+J,GAAa1O,EAAEoR,WAAWzM,IAAM+J,EAAY1O,EAAEoR,WAAWzM,EAAI,KAClEwP,EAAiBxP,EAAI,GAIrB+J,GAAa1O,EAAEoR,WAAWzM,KAC1BwP,EAAiBxP,IAKR,EAAjBwP,GAAgD,mBAAnBA,MAAgCA,EAAiB,GAMlFC,EAAY7O,KAAKyM,MAAMmC,EAAiBnU,EAAEJ,OAAO4J,gBAC7C4K,GAAapU,EAAE6Q,SAAShP,SAAQuS,EAAYpU,EAAE6Q,SAAShP,OAAS,GAEhEsS,IAAmBnU,EAAEqU,cAGzBrU,EAAEoU,UAAYA,EACdpU,EAAEsU,cAAgBtU,EAAEqU,YACpBrU,EAAEqU,YAAcF,EAChBnU,EAAEuU,kBAMNvU,EAAEuU,cAAgB,WACdvU,EAAEmR,OAAOwC,YAAY3T,EAAEJ,OAAOsM,iBAAmB,IAAMlM,EAAEJ,OAAOyM,eAAiB,IAAMrM,EAAEJ,OAAO0M,eAChG,IAAIkI,GAAcxU,EAAEmR,OAAOiB,GAAGpS,EAAEqU,YAOhC,IALAG,EAAYtG,SAASlO,EAAEJ,OAAOsM,kBAC9BsI,EAAYC,KAAK,IAAMzU,EAAEJ,OAAOqM,YAAYiC,SAASlO,EAAEJ,OAAOyM,gBAC9DmI,EAAYE,KAAK,IAAM1U,EAAEJ,OAAOqM,YAAYiC,SAASlO,EAAEJ,OAAO0M,gBAG1DtM,EAAE2U,SAAW3U,EAAE2U,QAAQ9S,OAAS,EAAG,CACnC7B,EAAE2U,QAAQhB,YAAY3T,EAAEJ,OAAO6M,kBAC/B,IAAImI,EACA5U,GAAEJ,OAAOa,MACTmU,EAAcrP,KAAK0M,KAAKjS,EAAEqU,YAAcrU,EAAEyL,cAAczL,EAAEJ,OAAO4J,eAC7DoL,EAAc5U,EAAEmR,OAAOtP,OAAS,EAAqB,EAAjB7B,EAAEyL,eACtCmJ,GAA6B5U,EAAEmR,OAAOtP,OAA0B,EAAjB7B,EAAEyL,cAEjDmJ,EAAc5U,EAAE2U,QAAQ9S,OAAS,IAAG+S,GAA4B5U,EAAE2U,QAAQ9S,SAI1E+S,EADuB,mBAAhB5U,GAAEoU,UACKpU,EAAEoU,UAGFpU,EAAEqU,aAAe,EAGnCrU,EAAEiO,oBAAoBpM,OAAS,EAC/B7B,EAAE2U,QAAQ5U,KAAK,WACPqB,EAAEtB,MAAM4B,UAAYkT,GAAaxT,EAAEtB,MAAMoO,SAASlO,EAAEJ,OAAO6M,qBAInEzM,EAAE2U,QAAQvC,GAAGwC,GAAa1G,SAASlO,EAAEJ,OAAO6M,mBAK/CzM,EAAEJ,OAAOa,OACNT,EAAEJ,OAAOgL,aACL5K,EAAEkU,aACF9S,EAAEpB,EAAEJ,OAAOgL,YAAYsD,SAASlO,EAAEJ,OAAO8M,qBACrC1M,EAAEJ,OAAOiN,MAAQ7M,EAAE6M,MAAM7M,EAAE6M,KAAKgI,QAAQzT,EAAEpB,EAAEJ,OAAOgL,eAGvDxJ,EAAEpB,EAAEJ,OAAOgL,YAAY+I,YAAY3T,EAAEJ,OAAO8M,qBACxC1M,EAAEJ,OAAOiN,MAAQ7M,EAAE6M,MAAM7M,EAAE6M,KAAKiI,OAAO1T,EAAEpB,EAAEJ,OAAOgL,eAG1D5K,EAAEJ,OAAO+K,aACL3K,EAAEY,OACFQ,EAAEpB,EAAEJ,OAAO+K,YAAYuD,SAASlO,EAAEJ,OAAO8M,qBACrC1M,EAAEJ,OAAOiN,MAAQ7M,EAAE6M,MAAM7M,EAAE6M,KAAKgI,QAAQzT,EAAEpB,EAAEJ,OAAO+K,eAGvDvJ,EAAEpB,EAAEJ,OAAO+K,YAAYgJ,YAAY3T,EAAEJ,OAAO8M,qBACxC1M,EAAEJ,OAAOiN,MAAQ7M,EAAE6M,MAAM7M,EAAE6M,KAAKiI,OAAO1T,EAAEpB,EAAEJ,OAAO+K,iBAStE3K,EAAE+U,iBAAmB,WACjB,GAAK/U,EAAEJ,OAAOyK,YACVrK,EAAEiO,qBAAuBjO,EAAEiO,oBAAoBpM,OAAS,EAAG,CAG3D,IAAK,GAFDmT,GAAc,GACdC,EAAkBjV,EAAEJ,OAAOa,KAAO8E,KAAK0M,MAAMjS,EAAEmR,OAAOtP,OAA0B,EAAjB7B,EAAEyL,cAAoBzL,EAAEJ,OAAO4J,gBAAkBxJ,EAAE6Q,SAAShP,OACtH8C,EAAI,EAAOsQ,EAAJtQ,EAAqBA,IAE7BqQ,GADAhV,EAAEJ,OAAO4K,uBACMxK,EAAEJ,OAAO4K,uBAAuB7F,EAAG3E,EAAEJ,OAAO4M,aAG5C,gBAAkBxM,EAAEJ,OAAO4M,YAAc,WAGhExM,GAAEiO,oBAAoBiH,KAAKF,GAC3BhV,EAAE2U,QAAU3U,EAAEiO,oBAAoBkC,KAAK,IAAMnQ,EAAEJ,OAAO4M,eAM9DxM,EAAEkQ,OAAS,SAAUiF,GASjB,QAASC,KACLC,EAAe9P,KAAK+P,IAAI/P,KAAK2N,IAAIlT,EAAE0O,UAAW1O,EAAEiG,gBAAiBjG,EAAE4Q,gBACnE5Q,EAAEmG,oBAAoBkP,GACtBrV,EAAEqG,oBACFrG,EAAEuU,gBAEN,GAdAvU,EAAE8Q,sBACF9Q,EAAEkR,mBACFlR,EAAEoG,iBACFpG,EAAE+U,mBACF/U,EAAEuU,gBACEvU,EAAEJ,OAAOmJ,WAAa/I,EAAE+I,WACxB/I,EAAE+I,UAAUwM,MAQZJ,EAAiB,CACjB,GAAIK,GAAYH,CACZrV,GAAEJ,OAAOkG,SACTsP,KAIII,EAD2B,SAA3BxV,EAAEJ,OAAOyJ,eAA4BrJ,EAAEY,QAAUZ,EAAEJ,OAAO6J,eAC7CzJ,EAAEyV,QAAQzV,EAAEmR,OAAOtP,OAAS,EAAG,GAAG,GAAO,GAGzC7B,EAAEyV,QAAQzV,EAAEqU,YAAa,GAAG,GAAO,GAE/CmB,GACDJ,OAUhBpV,EAAEuC,SAAW,WAQT,GAPAvC,EAAE8Q,sBACF9Q,EAAEkR,mBACFlR,EAAEoG,kBAC6B,SAA3BpG,EAAEJ,OAAOyJ,eAA4BrJ,EAAEJ,OAAOkG,WAAU9F,EAAE+U,mBAC1D/U,EAAEJ,OAAOmJ,WAAa/I,EAAE+I,WACxB/I,EAAE+I,UAAUwM,MAEZvV,EAAEJ,OAAOkG,SAAU,CACnB,GAAIuP,GAAe9P,KAAK+P,IAAI/P,KAAK2N,IAAIlT,EAAE0O,UAAW1O,EAAEiG,gBAAiBjG,EAAE4Q,eACvE5Q,GAAEmG,oBAAoBkP,GACtBrV,EAAEqG,oBACFrG,EAAEuU,oBAGFvU,GAAEuU,gBAC6B,SAA3BvU,EAAEJ,OAAOyJ,eAA4BrJ,EAAEY,QAAUZ,EAAEJ,OAAO6J,eAC1DzJ,EAAEyV,QAAQzV,EAAEmR,OAAOtP,OAAS,EAAG,GAAG,GAAO,GAGzC7B,EAAEyV,QAAQzV,EAAEqU,YAAa,GAAG,GAAO,GAW/C,IAAIqB,IAAiB,YAAa,YAAa,UAC3CxV,QAAOyV,UAAUC,eAAgBF,GAAiB,cAAe,cAAe,aAC3ExV,OAAOyV,UAAUE,mBAAkBH,GAAiB,gBAAiB,gBAAiB,gBAC/F1V,EAAE8V,aACEC,MAAQ/V,EAAE2N,QAAQG,QAAU9N,EAAEJ,OAAOgK,cAAiB,aAAe8L,EAAc,GACnFM,KAAOhW,EAAE2N,QAAQG,QAAU9N,EAAEJ,OAAOgK,cAAgB,YAAc8L,EAAc,GAChFO,IAAMjW,EAAE2N,QAAQG,QAAU9N,EAAEJ,OAAOgK,cAAgB,WAAa8L,EAAc,KAK9ExV,OAAOyV,UAAUC,gBAAkB1V,OAAOyV,UAAUE,oBACpB,cAA/B7V,EAAEJ,OAAO2H,kBAAoCvH,EAAEG,UAAYH,EAAE+N,SAASG,SAAS,cAAgBlO,EAAEJ,OAAOS,WAI7GL,EAAEkW,WAAa,SAAUC,GACrB,GAAIC,GAAYD,EAAS,MAAQ,KAC7BE,EAASF,EAAS,sBAAwB,mBAC1C5O,EAAmD,cAA/BvH,EAAEJ,OAAO2H,kBAAoCvH,EAAEG,UAAU,GAAKH,EAAE+N,QAAQ,GAC5F1M,EAASrB,EAAE2N,QAAQG,MAAQvG,EAAoB/D,SAE/C8S,EAActW,EAAEJ,OAAO2W,QAAS,GAAO,CAGvCvW,GAAEwW,QAAQC,IACVlP,EAAkB8O,GAAQrW,EAAE8V,YAAYC,MAAO/V,EAAE0W,cAAc,GAC/DrV,EAAOgV,GAAQrW,EAAE8V,YAAYE,KAAMhW,EAAE2W,YAAaL,GAClDjV,EAAOgV,GAAQrW,EAAE8V,YAAYG,IAAKjW,EAAE4W,YAAY,KAG5C5W,EAAE2N,QAAQG,QACVvG,EAAkB8O,GAAQrW,EAAE8V,YAAYC,MAAO/V,EAAE0W,cAAc,GAC/DnP,EAAkB8O,GAAQrW,EAAE8V,YAAYE,KAAMhW,EAAE2W,YAAaL,GAC7D/O,EAAkB8O,GAAQrW,EAAE8V,YAAYG,IAAKjW,EAAE4W,YAAY,KAE3DhX,EAAOgK,eAAkB5J,EAAEuO,OAAOsI,KAAQ7W,EAAEuO,OAAOC,UACnDjH,EAAkB8O,GAAQ,YAAarW,EAAE0W,cAAc,GACvDrV,EAAOgV,GAAQ,YAAarW,EAAE2W,YAAaL,GAC3CjV,EAAOgV,GAAQ,UAAWrW,EAAE4W,YAAY,KAGhD1W,OAAOmW,GAAQ,SAAUrW,EAAEuC,UAGvBvC,EAAEJ,OAAO+K,aACTvJ,EAAEpB,EAAEJ,OAAO+K,YAAYyL,GAAW,QAASpW,EAAE8W,aACzC9W,EAAEJ,OAAOiN,MAAQ7M,EAAE6M,MAAMzL,EAAEpB,EAAEJ,OAAO+K,YAAYyL,GAAW,UAAWpW,EAAE6M,KAAKkK,aAEjF/W,EAAEJ,OAAOgL,aACTxJ,EAAEpB,EAAEJ,OAAOgL,YAAYwL,GAAW,QAASpW,EAAEgX,aACzChX,EAAEJ,OAAOiN,MAAQ7M,EAAE6M,MAAMzL,EAAEpB,EAAEJ,OAAOgL,YAAYwL,GAAW,UAAWpW,EAAE6M,KAAKkK,aAEjF/W,EAAEJ,OAAOyK,YAAcrK,EAAEJ,OAAO0K,qBAChClJ,EAAEpB,EAAEiO,qBAAqBmI,GAAW,QAAS,IAAMpW,EAAEJ,OAAO4M,YAAaxM,EAAEiX,eAI3EjX,EAAEJ,OAAOoL,eAAiBhL,EAAEJ,OAAOqL,2BAA0B1D,EAAkB8O,GAAQ,QAASrW,EAAEgL,eAAe,IAEzHhL,EAAEkX,aAAe,WACblX,EAAEkW,cAENlW,EAAEmX,aAAe,WACbnX,EAAEkW,YAAW,IAOjBlW,EAAEoX,YAAa,EACfpX,EAAEgL,cAAgB,SAAU/J,GACnBjB,EAAEoX,aACCpX,EAAEJ,OAAOoL,eAAe/J,EAAE4D,iBAC1B7E,EAAEJ,OAAOqL,2BACThK,EAAEoW,kBACFpW,EAAEqW,8BAKdtX,EAAE8W,YAAc,SAAU7V,GACtBA,EAAE4D,iBACF7E,EAAE+E,aAEN/E,EAAEgX,YAAc,SAAU/V,GACtBA,EAAE4D,iBACF7E,EAAEgF,aAENhF,EAAEiX,aAAe,SAAUhW,GACvBA,EAAE4D,gBACF,IAAInD,GAAQN,EAAEtB,MAAM4B,QAAU1B,EAAEJ,OAAO4J,cACnCxJ,GAAEJ,OAAOa,OAAMiB,GAAgB1B,EAAEyL,cACrCzL,EAAEyV,QAAQ/T,IA0Bd1B,EAAEuX,mBAAqB,SAAUtW,GAC7B,GAAIkR,GAAQnR,EAAmBC,EAAG,IAAMjB,EAAEJ,OAAOqM,WACjD,KAAIkG,EAOA,MAFAnS,GAAEwX,aAAe5V,YACjB5B,EAAEyX,aAAe7V,OAGrB,IARI5B,EAAEwX,aAAerF,EACjBnS,EAAEyX,aAAerW,EAAE+Q,GAAOzQ,QAO1B1B,EAAEJ,OAAOsL,qBAA0CtJ,SAAnB5B,EAAEyX,cAA8BzX,EAAEyX,eAAiBzX,EAAEqU,YAAa,CAClG,GACIqD,GADAC,EAAe3X,EAAEyX,YAErB,IAAIzX,EAAEJ,OAAOa,KAET,GADAiX,EAAYtW,EAAEpB,EAAEwX,cAAc1Q,KAAK,2BAC/B6Q,EAAe3X,EAAEmR,OAAOtP,OAAS7B,EAAEJ,OAAOyJ,cAC1CrJ,EAAEU,UACFiX,EAAe3X,EAAE+N,QAAQC,SAAS,IAAMhO,EAAEJ,OAAOqM,WAAa,6BAA+ByL,EAAY,MAAMtF,GAAG,GAAG1Q,QACrHlB,WAAW,WACPR,EAAEyV,QAAQkC,IACX,OAEF,IAAIA,EAAe3X,EAAEJ,OAAOyJ,cAAgB,EAAG,CAChDrJ,EAAEU,SACF,IAAIkX,GAAmB5X,EAAE+N,QAAQC,SAAS,IAAMhO,EAAEJ,OAAOqM,WAAa,6BAA+ByL,EAAY,KACjHC,GAAeC,EAAiBxF,GAAGwF,EAAiB/V,OAAS,GAAGH,QAChElB,WAAW,WACPR,EAAEyV,QAAQkC,IACX,OAGH3X,GAAEyV,QAAQkC,OAId3X,GAAEyV,QAAQkC,IAKtB,IAAIE,GACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EAI4BC,EAG5BC,EALAC,EAAe,kCAEfC,EAAgBjS,KAAKkS,MAErBC,IAIJzY,GAAE0Y,WAAY,EAGd1Y,EAAE2Y,SACEC,OAAQ,EACRC,OAAQ,EACRC,SAAU,EACVC,SAAU,EACVC,KAAM,EAIV,IAAIC,GAAcC,CAszClB,IArzCAlZ,EAAE0W,aAAe,SAAUzV,GAGvB,GAFIA,EAAE+B,gBAAe/B,EAAIA,EAAE+B,eAC3BiW,EAA0B,eAAXhY,EAAEkY,KACZF,KAAgB,SAAWhY,KAAiB,IAAZA,EAAEmY,MAAvC,CACA,GAAIpZ,EAAEJ,OAAOmM,WAAa/K,EAAmBC,EAAG,IAAMjB,EAAEJ,OAAOoM,gBAE3D,YADAhM,EAAEoX,YAAa,EAGnB,KAAIpX,EAAEJ,OAAOkM,cACJ9K,EAAmBC,EAAGjB,EAAEJ,OAAOkM,cADxC,CAcA,GAXA+L,GAAY,EACZC,GAAU,EACVE,EAAcpW,OACdsX,EAActX,OACd5B,EAAE2Y,QAAQC,OAAS5Y,EAAE2Y,QAAQG,SAAsB,eAAX7X,EAAEkY,KAAwBlY,EAAEoY,cAAc,GAAGC,MAAQrY,EAAEqY,MAC/FtZ,EAAE2Y,QAAQE,OAAS7Y,EAAE2Y,QAAQI,SAAsB,eAAX9X,EAAEkY,KAAwBlY,EAAEoY,cAAc,GAAGE,MAAQtY,EAAEsY,MAC/FxB,EAAiBzR,KAAKkS,MACtBxY,EAAEoX,YAAa,EACfpX,EAAE8Q,sBACF9Q,EAAEwZ,eAAiB5X,OACf5B,EAAEJ,OAAOuK,UAAY,IAAGgO,GAAqB,GAClC,eAAXlX,EAAEkY,KAAuB,CACzB,GAAItU,IAAiB,CACjBzD,GAAEH,EAAEI,QAAQC,GAAGgX,KAAezT,GAAiB,GAC/CrB,SAASC,eAAiBrC,EAAEoC,SAASC,eAAenC,GAAGgX,IACvD9U,SAASC,cAAcgW,OAEvB5U,GACA5D,EAAE4D,iBAGV7E,EAAEwC,KAAK,eAAgBxC,EAAGiB,MAG9BjB,EAAE2W,YAAc,SAAU1V,GAEtB,GADIA,EAAE+B,gBAAe/B,EAAIA,EAAE+B,iBACvBiW,GAA2B,cAAXhY,EAAEkY,MAClBlY,EAAEyY,yBAAN,CACA,GAAI1Z,EAAEJ,OAAOsK,aAGT,MAFA4N,IAAU,OACV9X,EAAEoX,YAAa,EAGnB,IAAI6B,GAAgBzV,SAASC,eACrBxC,EAAEI,SAAWmC,SAASC,eAAiBrC,EAAEH,EAAEI,QAAQC,GAAGgX,GAGtD,MAFAR,IAAU,OACV9X,EAAEoX,YAAa,EAOvB,IAFApX,EAAEwC,KAAK,cAAexC,EAAGiB,KAErBA,EAAEoY,eAAiBpY,EAAEoY,cAAcxX,OAAS,GAAhD,CAKA,GAHA7B,EAAE2Y,QAAQG,SAAsB,cAAX7X,EAAEkY,KAAuBlY,EAAEoY,cAAc,GAAGC,MAAQrY,EAAEqY,MAC3EtZ,EAAE2Y,QAAQI,SAAsB,cAAX9X,EAAEkY,KAAuBlY,EAAEoY,cAAc,GAAGE,MAAQtY,EAAEsY,MAEhD,mBAAhBvB,GAA6B,CACpC,GAAIrO,GAA4H,IAA/GpE,KAAKoU,MAAMpU,KAAKC,IAAIxF,EAAE2Y,QAAQI,SAAW/Y,EAAE2Y,QAAQE,QAAStT,KAAKC,IAAIxF,EAAE2Y,QAAQG,SAAW9Y,EAAE2Y,QAAQC,SAAiBrT,KAAKqU,EAC3I5B,GAAc5X,IAAQuJ,EAAa3J,EAAEJ,OAAO+J,WAAc,GAAKA,EAAa3J,EAAEJ,OAAO+J,WAUzF,GARIqO,GACAhY,EAAEwC,KAAK,sBAAuBxC,EAAGiB,GAEV,mBAAhBiY,IAA+BlZ,EAAEwW,QAAQqD,UAC5C7Z,EAAE2Y,QAAQG,WAAa9Y,EAAE2Y,QAAQC,QAAU5Y,EAAE2Y,QAAQI,WAAa/Y,EAAE2Y,QAAQE,UAC5EK,GAAc,GAGjBrB,EAAL,CACA,GAAIG,EAEA,YADAH,GAAY,EAGhB,IAAKqB,IAAelZ,EAAEwW,QAAQqD,QAA9B,CAGA7Z,EAAEoX,YAAa,EACfpX,EAAEwC,KAAK,eAAgBxC,EAAGiB,GAC1BA,EAAE4D,iBACE7E,EAAEJ,OAAOwK,2BAA6BpK,EAAEJ,OAAO2W,QAC/CtV,EAAEoW,kBAGDS,IACGlY,EAAOa,MACPT,EAAEU,UAENwX,EAAiBlY,EAAEgG,sBACnBhG,EAAEkG,qBAAqB,GACnBlG,EAAE0Y,WACF1Y,EAAE+N,QAAQ+L,QAAQ,oFAElB9Z,EAAEJ,OAAOU,UAAYN,EAAEsQ,cACnBtQ,EAAEJ,OAAO8H,6BACT1H,EAAEc,eAGFd,EAAE0Q,iBAGV2H,GAAsB,EAElBrY,EAAEJ,OAAOmL,aACT/K,EAAEG,UAAU,GAAG+O,MAAMC,OAAS,OAC9BnP,EAAEG,UAAU,GAAG+O,MAAMC,OAAS,mBAC9BnP,EAAEG,UAAU,GAAG+O,MAAMC,OAAS,eAC9BnP,EAAEG,UAAU,GAAG+O,MAAMC,OAAS,aAGtC2I,GAAU,CAEV,IAAIkB,GAAOhZ,EAAE2Y,QAAQK,KAAO5Y,IAAQJ,EAAE2Y,QAAQG,SAAW9Y,EAAE2Y,QAAQC,OAAS5Y,EAAE2Y,QAAQI,SAAW/Y,EAAE2Y,QAAQE,MAE3GG,IAAchZ,EAAEJ,OAAO8J,WACnB1J,EAAEmO,MAAK6K,GAAQA,GAEnBhZ,EAAEwZ,eAAiBR,EAAO,EAAI,OAAS,OACvCf,EAAmBe,EAAOd,CAE1B,IAAI6B,IAAsB,CAsB1B,IArBKf,EAAO,GAAKf,EAAmBjY,EAAE4Q,gBAClCmJ,GAAsB,EAClB/Z,EAAEJ,OAAO6K,aAAYwN,EAAmBjY,EAAE4Q,eAAiB,EAAIrL,KAAKyU,KAAKha,EAAE4Q,eAAiBsH,EAAiBc,EAAMhZ,EAAEJ,OAAO8K,mBAEpH,EAAPsO,GAAYf,EAAmBjY,EAAEiG,iBACtC8T,GAAsB,EAClB/Z,EAAEJ,OAAO6K,aAAYwN,EAAmBjY,EAAEiG,eAAiB,EAAIV,KAAKyU,IAAIha,EAAEiG,eAAiBiS,EAAiBc,EAAMhZ,EAAEJ,OAAO8K,mBAG/HqP,IACA9Y,EAAEyY,yBAA0B,IAI3B1Z,EAAEJ,OAAOiM,kBAAyC,SAArB7L,EAAEwZ,gBAAgDtB,EAAnBD,IAC7DA,EAAmBC,IAElBlY,EAAEJ,OAAOgM,kBAAyC,SAArB5L,EAAEwZ,gBAA6BvB,EAAmBC,IAChFD,EAAmBC,GAGlBlY,EAAEJ,OAAOqK,aAAd,CAGA,GAAIjK,EAAEJ,OAAOuK,UAAY,EAAG,CACxB,KAAI5E,KAAKC,IAAIwT,GAAQhZ,EAAEJ,OAAOuK,WAAagO,GAYvC,YADAF,EAAmBC,EAVnB,KAAKC,EAMD,MALAA,IAAqB,EACrBnY,EAAE2Y,QAAQC,OAAS5Y,EAAE2Y,QAAQG,SAC7B9Y,EAAE2Y,QAAQE,OAAS7Y,EAAE2Y,QAAQI,SAC7Bd,EAAmBC,OACnBlY,EAAE2Y,QAAQK,KAAO5Y,IAAQJ,EAAE2Y,QAAQG,SAAW9Y,EAAE2Y,QAAQC,OAAS5Y,EAAE2Y,QAAQI,SAAW/Y,EAAE2Y,QAAQE,SAUxG7Y,EAAEJ,OAAOkG,UAAY9F,EAAEJ,OAAOiL,sBAC9B7K,EAAEqG,oBAEFrG,EAAEJ,OAAOkG,WAEiB,IAAtB2S,EAAW5W,QACX4W,EAAW3V,MACPiD,SAAU/F,EAAE2Y,QAAQvY,IAAQ,SAAW,UACvC6Z,KAAMlC,IAGdU,EAAW3V,MACPiD,SAAU/F,EAAE2Y,QAAQvY,IAAQ,WAAa,YACzC6Z,MAAM,GAAK3T,OAAQC,aAI3BvG,EAAEoG,eAAe6R,GAEjBjY,EAAEmG,oBAAoB8R,SAE1BjY,EAAE4W,WAAa,SAAU3V,GAGrB,GAFIA,EAAE+B,gBAAe/B,EAAIA,EAAE+B,eAC3BhD,EAAEwC,KAAK,aAAcxC,EAAGiB,GACnB4W,EAAL,CAEI7X,EAAEJ,OAAOmL,YAAc+M,GAAWD,IAClC7X,EAAEG,UAAU,GAAG+O,MAAMC,OAAS,OAC9BnP,EAAEG,UAAU,GAAG+O,MAAMC,OAAS,eAC9BnP,EAAEG,UAAU,GAAG+O,MAAMC,OAAS,YAC9BnP,EAAEG,UAAU,GAAG+O,MAAMC,OAAS,OAIlC,IAAI+K,GAAe5T,KAAKkS,MACpB2B,EAAWD,EAAenC,CA4B9B,IAzBI/X,EAAEoX,aACFpX,EAAEuX,mBAAmBtW,GACrBjB,EAAEwC,KAAK,QAASxC,EAAGiB,GACJ,IAAXkZ,GAAmBD,EAAe3B,EAAiB,MAC/CH,GAAc3H,aAAa2H,GAC/BA,EAAe5X,WAAW,WACjBR,IACDA,EAAEJ,OAAO2K,gBAAkBvK,EAAEiO,oBAAoBpM,OAAS,IAAMT,EAAEH,EAAEI,QAAQ+Y,SAASpa,EAAEJ,OAAO4M,cAC9FxM,EAAEiO,oBAAoBoM,YAAYra,EAAEJ,OAAO+M,uBAE/C3M,EAAEwC,KAAK,UAAWxC,EAAGiB,KACtB,MAGQ,IAAXkZ,GAAmD,IAAhCD,EAAe3B,IAC9BH,GAAc3H,aAAa2H,GAC/BpY,EAAEwC,KAAK,cAAexC,EAAGiB,KAIjCsX,EAAgBjS,KAAKkS,MACrBhY,WAAW,WACHR,GAAKA,EAAEoX,aAAYpX,EAAEoX,YAAa,IACvC,IAEES,IAAcC,IAAY9X,EAAEwZ,gBAAqC,IAAnBxZ,EAAE2Y,QAAQK,MAAcf,IAAqBC,EAE5F,YADAL,EAAYC,GAAU,EAG1BD,GAAYC,GAAU,CAEtB,IAAIwC,EAOJ,IALIA,EADAta,EAAEJ,OAAOqK,aACIjK,EAAEmO,IAAMnO,EAAE0O,WAAa1O,EAAE0O,WAGxBuJ,EAEdjY,EAAEJ,OAAOkG,SAAU,CACnB,GAAIwU,GAActa,EAAE4Q,eAEhB,WADA5Q,GAAEyV,QAAQzV,EAAEqU,YAGX,IAAIiG,GAActa,EAAEiG,eAErB,WADAjG,GAAEyV,QAAQzV,EAAEmR,OAAOtP,OAAS,EAIhC,IAAI7B,EAAEJ,OAAO+H,iBAAkB,CAC3B,GAAI8Q,EAAW5W,OAAS,EAAG,CACvB,GAAI0Y,GAAgB9B,EAAW+B,MAAOC,EAAgBhC,EAAW+B,MAE7DE,EAAWH,EAAcxU,SAAW0U,EAAc1U,SAClDkU,EAAOM,EAAcN,KAAOQ,EAAcR,IAC9Cja,GAAE2O,SAAW+L,EAAWT,EACxBja,EAAE2O,SAAW3O,EAAE2O,SAAW,EACtBpJ,KAAKC,IAAIxF,EAAE2O,UAAY,MACvB3O,EAAE2O,SAAW,IAIbsL,EAAO,MAAQ,GAAI3T,OAAOC,UAAYgU,EAAcN,KAAQ,OAC5Dja,EAAE2O,SAAW,OAGjB3O,GAAE2O,SAAW,CAGjB8J,GAAW5W,OAAS,CACpB,IAAI8Y,GAAmB,IAAO3a,EAAEJ,OAAOgI,sBACnCgT,EAAmB5a,EAAE2O,SAAWgM,EAEhCE,EAAc7a,EAAE0O,UAAYkM,CAC5B5a,GAAEmO,MAAK0M,GAAgBA,EAC3B,IACIC,GADAC,GAAW,EAEXC,EAAsC,GAAvBzV,KAAKC,IAAIxF,EAAE2O,UAAiB3O,EAAEJ,OAAOkI,2BACpD+S,GAAc7a,EAAEiG,iBACZjG,EAAEJ,OAAOiI,wBACLgT,EAAc7a,EAAEiG,gBAAkB+U,IAClCH,EAAc7a,EAAEiG,eAAiB+U,GAErCF,EAAsB9a,EAAEiG,eACxB8U,GAAW,EACX1C,GAAsB,GAGtBwC,EAAc7a,EAAEiG,gBAGpB4U,EAAc7a,EAAE4Q,iBACZ5Q,EAAEJ,OAAOiI,wBACLgT,EAAc7a,EAAE4Q,eAAiBoK,IACjCH,EAAc7a,EAAE4Q,eAAiBoK,GAErCF,EAAsB9a,EAAE4Q,eACxBmK,GAAW,EACX1C,GAAsB,GAGtBwC,EAAc7a,EAAE4Q,gBAIL,IAAf5Q,EAAE2O,WAEEgM,EAAmBpV,KAAKC,IADxBxF,EAAEmO,MAC4B0M,EAAc7a,EAAE0O,WAAa1O,EAAE2O,UAGhCkM,EAAc7a,EAAE0O,WAAa1O,EAAE2O,WAIhE3O,EAAEJ,OAAOiI,wBAA0BkT,GACnC/a,EAAEoG,eAAe0U,GACjB9a,EAAEkG,qBAAqByU,GACvB3a,EAAEmG,oBAAoB0U,GACtB7a,EAAEib,oBACFjb,EAAE0Y,WAAY,EACd1Y,EAAE+N,QAAQ4C,cAAc,WACf0H,IACLrY,EAAEwC,KAAK,mBAAoBxC,GAE3BA,EAAEkG,qBAAqBlG,EAAEJ,OAAO6H,OAChCzH,EAAEmG,oBAAoB2U,GACtB9a,EAAE+N,QAAQ4C,cAAc,WACpB3Q,EAAEkb,wBAGHlb,EAAE2O,UACT3O,EAAEoG,eAAeyU,GACjB7a,EAAEkG,qBAAqByU,GACvB3a,EAAEmG,oBAAoB0U,GACtB7a,EAAEib,oBACGjb,EAAE0Y,YACH1Y,EAAE0Y,WAAY,EACd1Y,EAAE+N,QAAQ4C,cAAc,WACpB3Q,EAAEkb,sBAKVlb,EAAEoG,eAAeyU,GAGrB7a,EAAEqG,oBAMN,cAJKrG,EAAEJ,OAAO+H,kBAAoBwS,GAAYna,EAAEJ,OAAOoK,gBACnDhK,EAAEoG,iBACFpG,EAAEqG,sBAMV,GAAI1B,GAAGwW,EAAY,EAAGC,EAAYpb,EAAEqR,gBAAgB,EACpD,KAAK1M,EAAI,EAAGA,EAAI3E,EAAEoR,WAAWvP,OAAQ8C,GAAK3E,EAAEJ,OAAO4J,eACU,mBAA9CxJ,GAAEoR,WAAWzM,EAAI3E,EAAEJ,OAAO4J,gBAC7B8Q,GAActa,EAAEoR,WAAWzM,IAAM2V,EAAata,EAAEoR,WAAWzM,EAAI3E,EAAEJ,OAAO4J,kBACxE2R,EAAYxW,EACZyW,EAAYpb,EAAEoR,WAAWzM,EAAI3E,EAAEJ,OAAO4J,gBAAkBxJ,EAAEoR,WAAWzM,IAIrE2V,GAActa,EAAEoR,WAAWzM,KAC3BwW,EAAYxW,EACZyW,EAAYpb,EAAEoR,WAAWpR,EAAEoR,WAAWvP,OAAS,GAAK7B,EAAEoR,WAAWpR,EAAEoR,WAAWvP,OAAS,GAMnG,IAAIwZ,IAASf,EAAata,EAAEoR,WAAW+J,IAAcC,CAErD,IAAIjB,EAAWna,EAAEJ,OAAOoK,aAAc,CAElC,IAAKhK,EAAEJ,OAAOkK,WAEV,WADA9J,GAAEyV,QAAQzV,EAAEqU,YAGS,UAArBrU,EAAEwZ,gBACqCxZ,EAAEyV,QAArC4F,GAASrb,EAAEJ,OAAOmK,gBAA2BoR,EAAYnb,EAAEJ,OAAO4J,eACvD2R,GAGM,SAArBnb,EAAEwZ,gBAC0CxZ,EAAEyV,QAA1C4F,EAAS,EAAIrb,EAAEJ,OAAOmK,gBAA4BoR,EAAYnb,EAAEJ,OAAO4J,eAC5D2R,OAGlB,CAED,IAAKnb,EAAEJ,OAAOiK,YAEV,WADA7J,GAAEyV,QAAQzV,EAAEqU,YAGS,UAArBrU,EAAEwZ,gBACFxZ,EAAEyV,QAAQ0F,EAAYnb,EAAEJ,OAAO4J,gBAGV,SAArBxJ,EAAEwZ,gBACFxZ,EAAEyV,QAAQ0F,MAOtBnb,EAAEe,SAAW,SAAUua,EAAY7T,GAC/B,MAAOzH,GAAEyV,QAAQ6F,EAAY7T,GAAO,GAAM,IAE9CzH,EAAEyV,QAAU,SAAU6F,EAAY7T,EAAO8T,EAAcC,GACvB,mBAAjBD,KAA8BA,GAAe,GAC9B,mBAAfD,KAA4BA,EAAa,GACnC,EAAbA,IAAgBA,EAAa,GACjCtb,EAAEoU,UAAY7O,KAAKyM,MAAMsJ,EAAatb,EAAEJ,OAAO4J,gBAC3CxJ,EAAEoU,WAAapU,EAAE6Q,SAAShP,SAAQ7B,EAAEoU,UAAYpU,EAAE6Q,SAAShP,OAAS,EAExE,IAAI6M,IAAc1O,EAAE6Q,SAAS7Q,EAAEoU,UAI3BpU,GAAEJ,OAAOU,UAAYN,EAAEsQ,cACnBkL,IAAaxb,EAAEJ,OAAO8H,6BACtB1H,EAAE0Q,cAAcjJ,GAGhBzH,EAAEc,gBAIVd,EAAEoG,eAAesI,EAGjB,KAAK,GAAI/J,GAAI,EAAGA,EAAI3E,EAAEoR,WAAWvP,OAAQ8C,KAC/B+J,GAAa1O,EAAEoR,WAAWzM,KAC5B2W,EAAa3W,EAQrB,IAJqB,mBAAV8C,KAAuBA,EAAQzH,EAAEJ,OAAO6H,OACnDzH,EAAEsU,cAAgBtU,EAAEqU,aAAe,EACnCrU,EAAEqU,YAAciH,EAEZ5M,IAAc1O,EAAE0O,UAEhB,MADA1O,GAAEuU,iBACK,CAEXvU,GAAEib,kBAAkBM,EACHnb,KAAQsO,EAAY,EAAgBtO,IAAQ,EAAIsO,CAkBjE,OAjBc,KAAVjH,GACAzH,EAAEkG,qBAAqB,GACvBlG,EAAEmG,oBAAoBuI,GACtB1O,EAAEkb,gBAAgBK,KAGlBvb,EAAEkG,qBAAqBuB,GACvBzH,EAAEmG,oBAAoBuI,GACjB1O,EAAE0Y,YACH1Y,EAAE0Y,WAAY,EACd1Y,EAAE+N,QAAQ4C,cAAc,WACpB3Q,EAAEkb,gBAAgBK,OAK9Bvb,EAAEuU,iBACK,GAGXvU,EAAEib,kBAAoB,SAAUM,GACA,mBAAjBA,KAA8BA,GAAe,GACpDvb,EAAEyb,MAAMzb,EAAEyb,KAAKR,oBACfM,IACAvb,EAAEwC,KAAK,oBAAqBxC,GACxBA,EAAEqU,cAAgBrU,EAAEsU,eACpBtU,EAAEwC,KAAK,qBAAsBxC,KAIzCA,EAAEkb,gBAAkB,SAAUK,GAC1Bvb,EAAE0Y,WAAY,EACd1Y,EAAEkG,qBAAqB,GACK,mBAAjBqV,KAA8BA,GAAe,GACpDvb,EAAEyb,MAAMzb,EAAEyb,KAAKP,kBACfK,IACAvb,EAAEwC,KAAK,kBAAmBxC,GACtBA,EAAEqU,cAAgBrU,EAAEsU,eACpBtU,EAAEwC,KAAK,mBAAoBxC,IAG/BA,EAAEJ,OAAOuJ,SAAWnJ,EAAEmJ,SACtBnJ,EAAEmJ,QAAQuS,WAIlB1b,EAAE+E,UAAY,SAAUwW,EAAc9T,EAAO+T,GACzC,GAAIxb,EAAEJ,OAAOa,KAAM,CACf,GAAIT,EAAE0Y,UAAW,OAAO,CACxB1Y,GAAEU,SACF,EAAiBV,EAAEG,UAAU,GAAGwb,WAChC,MAAO3b,GAAEyV,QAAQzV,EAAEqU,YAAcrU,EAAEJ,OAAO4J,eAAgB/B,EAAO8T,EAAcC,GAE9E,MAAOxb,GAAEyV,QAAQzV,EAAEqU,YAAcrU,EAAEJ,OAAO4J,eAAgB/B,EAAO8T,EAAcC,IAExFxb,EAAEW,WAAa,SAAU8G,GACrB,MAAOzH,GAAE+E,WAAU,EAAM0C,GAAO,IAEpCzH,EAAEgF,UAAY,SAAUuW,EAAc9T,EAAO+T,GACzC,GAAIxb,EAAEJ,OAAOa,KAAM,CACf,GAAIT,EAAE0Y,UAAW,OAAO,CACxB1Y,GAAEU,SACF,EAAiBV,EAAEG,UAAU,GAAGwb,WAChC,MAAO3b,GAAEyV,QAAQzV,EAAEqU,YAAc,EAAG5M,EAAO8T,EAAcC,GAExD,MAAOxb,GAAEyV,QAAQzV,EAAEqU,YAAc,EAAG5M,EAAO8T,EAAcC,IAElExb,EAAE4b,WAAa,SAAUnU,GACrB,MAAOzH,GAAEgF,WAAU,EAAMyC,GAAO,IAEpCzH,EAAE6b,WAAa,SAAUN,EAAc9T,GACnC,MAAOzH,GAAEyV,QAAQzV,EAAEqU,YAAa5M,EAAO8T,IAM3Cvb,EAAEkG,qBAAuB,SAAU4V,EAAUC,GACzC/b,EAAE+N,QAAQiO,WAAWF,GACG,UAApB9b,EAAEJ,OAAOqI,QAAsBjI,EAAEic,QAAQjc,EAAEJ,OAAOqI,SAClDjI,EAAEic,QAAQjc,EAAEJ,OAAOqI,QAAQiU,cAAcJ,GAEzC9b,EAAEJ,OAAOkJ,UAAY9I,EAAE8I,UACvB9I,EAAE8I,SAASoT,cAAcJ,GAEzB9b,EAAEJ,OAAOmJ,WAAa/I,EAAE+I,WACxB/I,EAAE+I,UAAUmT,cAAcJ,GAE1B9b,EAAEJ,OAAO8L,SAAW1L,EAAEmc,YACtBnc,EAAEmc,WAAWD,cAAcJ,EAAUC,GAEzC/b,EAAEwC,KAAK,kBAAmBxC,EAAG8b,IAEjC9b,EAAEmG,oBAAsB,SAAUuI,EAAWrI,EAAmB0V,GAC5D,GAAIK,GAAI,EAAGC,EAAI,EAAGC,EAAI,CAClBlc,KACAgc,EAAIpc,EAAEmO,KAAOO,EAAYA,EAGzB2N,EAAI3N,EAEH1O,EAAEJ,OAAOoI,kBACkBhI,EAAE+N,QAAQ9G,UAAlCjH,EAAE2N,QAAQE,aAAkC,eAAiBuO,EAAI,OAASC,EAAI,OAASC,EAAI,MACtE,aAAeF,EAAI,OAASC,EAAI,OAG7Drc,EAAE0O,UAAYtO,IAAQgc,EAAIC,EAEtBhW,GAAmBrG,EAAEqG,oBACD,UAApBrG,EAAEJ,OAAOqI,QAAsBjI,EAAEic,QAAQjc,EAAEJ,OAAOqI,SAClDjI,EAAEic,QAAQjc,EAAEJ,OAAOqI,QAAQsU,aAAavc,EAAE0O,WAE1C1O,EAAEJ,OAAOkJ,UAAY9I,EAAE8I,UACvB9I,EAAE8I,SAASyT,aAAavc,EAAE0O,WAE1B1O,EAAEJ,OAAOmJ,WAAa/I,EAAE+I,WACxB/I,EAAE+I,UAAUwT,aAAavc,EAAE0O,WAE3B1O,EAAEJ,OAAO8L,SAAW1L,EAAEmc,YACtBnc,EAAEmc,WAAWI,aAAavc,EAAE0O,UAAWqN,GAE3C/b,EAAEwC,KAAK,iBAAkBxC,EAAGA,EAAE0O,YAGlC1O,EAAEwc,aAAe,SAAUrb,EAAIsb,GAC3B,GAAIC,GAAQC,EAAcC,EAAUC,CAOpC,OAJoB,mBAATJ,KACPA,EAAO,KAGPzc,EAAEJ,OAAOoI,iBACFhI,EAAEmO,KAAOnO,EAAE0O,UAAY1O,EAAE0O,WAGpCkO,EAAW1c,OAAO4c,iBAAiB3b,EAAI,MACnCjB,OAAO6c,gBAGPF,EAAkB,GAAIE,iBAA6C,SAA7BH,EAASI,gBAA6B,GAAKJ,EAASI,kBAG1FH,EAAkBD,EAASK,cAAgBL,EAASM,YAAcN,EAASO,aAAeP,EAASQ,aAAgBR,EAAS3V,WAAa2V,EAASS,iBAAiB,aAAa5L,QAAQ,aAAc,sBACtMiL,EAASG,EAAgBS,WAAWC,MAAM,MAGjC,MAATd,IAGIE,EADAzc,OAAO6c,gBACQF,EAAgBW,IAGhBhM,WADQ,KAAlBkL,EAAO7a,OACc6a,EAAO,IAGPA,EAAO,KAE5B,MAATD,IAGIE,EADAzc,OAAO6c,gBACQF,EAAgBY,IAGhBjM,WADQ,KAAlBkL,EAAO7a,OACc6a,EAAO,IAGPA,EAAO,KAErC1c,EAAEmO,KAAOwO,IAAcA,GAAgBA,GACpCA,GAAgB,IAE3B3c,EAAEgG,oBAAsB,SAAUyW,GAI9B,MAHoB,mBAATA,KACPA,EAAOrc,IAAQ,IAAM,KAElBJ,EAAEwc,aAAaxc,EAAE+N,QAAQ,GAAI0O,IAMxCzc,EAAE6C,aAoBF7C,EAAE0d,cAAgB,WACd,GAAI1d,EAAEJ,OAAOgN,eAET,IAAK,GADD+Q,GAAmB3d,EAAEG,UAAUoB,UAC1BoD,EAAI,EAAGA,EAAIgZ,EAAiB9b,OAAQ8C,IACzC7C,EAAa6b,EAAiBhZ,GAKtC7C,GAAa9B,EAAEG,UAAU,IAAKwC,WAAW,IAGzCb,EAAa9B,EAAE+N,QAAQ,IAAKrL,YAAY,KAE5C1C,EAAE4d,oBAAsB,WACpB,IAAK,GAAIjZ,GAAI,EAAGA,EAAI3E,EAAE6C,UAAUhB,OAAQ8C,IACpC3E,EAAE6C,UAAU8B,GAAGkZ,YAEnB7d,GAAE6C,cAMN7C,EAAE8d,WAAa,WAEX9d,EAAE+N,QAAQC,SAAS,IAAMhO,EAAEJ,OAAOqM,WAAa,IAAMjM,EAAEJ,OAAOwM,qBAAqB2R,QAEnF;GAAI5M,GAASnR,EAAE+N,QAAQC,SAAS,IAAMhO,EAAEJ,OAAOqM,WAC/CjM,GAAEyL,aAAezE,SAAShH,EAAEJ,OAAO6L,cAAgBzL,EAAEJ,OAAOyJ,cAAe,IAC3ErJ,EAAEyL,aAAezL,EAAEyL,aAAezL,EAAEJ,OAAO4L,qBACvCxL,EAAEyL,aAAe0F,EAAOtP,SACxB7B,EAAEyL,aAAe0F,EAAOtP,OAG5B,IAA2C8C,GAAvCqZ,KAAoBC,IAOxB,KANA9M,EAAOpR,KAAK,SAAU2B,EAAOP,GACzB,GAAIgR,GAAQ/Q,EAAEtB,KACV4B,GAAQ1B,EAAEyL,cAAcwS,EAAanb,KAAK3B,GAC1CO,EAAQyP,EAAOtP,QAAUH,GAASyP,EAAOtP,OAAS7B,EAAEyL,cAAcuS,EAAclb,KAAK3B,GACzFgR,EAAMrL,KAAK,0BAA2BpF,KAErCiD,EAAI,EAAGA,EAAIsZ,EAAapc,OAAQ8C,IACjC3E,EAAE+N,QAAQmQ,OAAO9c,EAAE6c,EAAatZ,GAAGwZ,WAAU,IAAOjQ,SAASlO,EAAEJ,OAAOwM,qBAE1E,KAAKzH,EAAIqZ,EAAcnc,OAAS,EAAG8C,GAAK,EAAGA,IACvC3E,EAAE+N,QAAQqQ,QAAQhd,EAAE4c,EAAcrZ,GAAGwZ,WAAU,IAAOjQ,SAASlO,EAAEJ,OAAOwM,uBAGhFpM,EAAEqe,YAAc,WACZre,EAAE+N,QAAQC,SAAS,IAAMhO,EAAEJ,OAAOqM,WAAa,IAAMjM,EAAEJ,OAAOwM,qBAAqB2R,SACnF/d,EAAEmR,OAAOmN,WAAW,4BAExBte,EAAEU,QAAU,WACR,GAAI6d,EAEAve,GAAEqU,YAAcrU,EAAEyL,cAClB8S,EAAWve,EAAEmR,OAAOtP,OAA0B,EAAjB7B,EAAEyL,aAAmBzL,EAAEqU,YACpDkK,GAAsBve,EAAEyL,aACxBzL,EAAEyV,QAAQ8I,EAAU,GAAG,GAAO,KAGG,SAA3Bve,EAAEJ,OAAOyJ,eAA4BrJ,EAAEqU,aAAgC,EAAjBrU,EAAEyL,cAAsBzL,EAAEqU,YAAcrU,EAAEmR,OAAOtP,OAAkC,EAAzB7B,EAAEJ,OAAOyJ,iBAC/HkV,GAAYve,EAAEmR,OAAOtP,OAAS7B,EAAEqU,YAAcrU,EAAEyL,aAChD8S,GAAsBve,EAAEyL,aACxBzL,EAAEyV,QAAQ8I,EAAU,GAAG,GAAO,KAMtCve,EAAEwe,YAAc,SAAUrN,GAItB,GAHInR,EAAEJ,OAAOa,MACTT,EAAEqe,cAEgB,gBAAXlN,IAAuBA,EAAOtP,OACrC,IAAK,GAAI8C,GAAI,EAAGA,EAAIwM,EAAOtP,OAAQ8C,IAC3BwM,EAAOxM,IAAI3E,EAAE+N,QAAQmQ,OAAO/M,EAAOxM,QAI3C3E,GAAE+N,QAAQmQ,OAAO/M,EAEjBnR,GAAEJ,OAAOa,MACTT,EAAE8d,aAEA9d,EAAEJ,OAAOuC,UAAYnC,EAAE2N,QAAQxL,UACjCnC,EAAEkQ,QAAO,IAGjBlQ,EAAEye,aAAe,SAAUtN,GACnBnR,EAAEJ,OAAOa,MACTT,EAAEqe,aAEN,IAAIlK,GAAiBnU,EAAEqU,YAAc,CACrC,IAAsB,gBAAXlD,IAAuBA,EAAOtP,OAAQ,CAC7C,IAAK,GAAI8C,GAAI,EAAGA,EAAIwM,EAAOtP,OAAQ8C,IAC3BwM,EAAOxM,IAAI3E,EAAE+N,QAAQqQ,QAAQjN,EAAOxM,GAE5CwP,GAAiBnU,EAAEqU,YAAclD,EAAOtP,WAGxC7B,GAAE+N,QAAQqQ,QAAQjN,EAElBnR,GAAEJ,OAAOa,MACTT,EAAE8d,aAEA9d,EAAEJ,OAAOuC,UAAYnC,EAAE2N,QAAQxL,UACjCnC,EAAEkQ,QAAO,GAEblQ,EAAEyV,QAAQtB,EAAgB,GAAG,IAEjCnU,EAAE0e,YAAc,SAAUC,GAClB3e,EAAEJ,OAAOa,MACTT,EAAEqe,aAEN,IACIO,GADAzK,EAAiBnU,EAAEqU,WAEvB,IAA6B,gBAAlBsK,IAA8BA,EAAc9c,OAAQ,CAC3D,IAAK,GAAI8C,GAAI,EAAGA,EAAIga,EAAc9c,OAAQ8C,IACtCia,EAAgBD,EAAcha,GAC1B3E,EAAEmR,OAAOyN,IAAgB5e,EAAEmR,OAAOiB,GAAGwM,GAAeb,SACpC5J,EAAhByK,GAAgCzK,GAExCA,GAAiB5O,KAAK2N,IAAIiB,EAAgB,OAG1CyK,GAAgBD,EACZ3e,EAAEmR,OAAOyN,IAAgB5e,EAAEmR,OAAOiB,GAAGwM,GAAeb,SACpC5J,EAAhByK,GAAgCzK,IACpCA,EAAiB5O,KAAK2N,IAAIiB,EAAgB,EAGxCnU,GAAEJ,OAAOuC,UAAYnC,EAAE2N,QAAQxL,UACjCnC,EAAEkQ,QAAO,GAEblQ,EAAEyV,QAAQtB,EAAgB,GAAG,IAEjCnU,EAAE6e,gBAAkB,WAEhB,IAAK,GADDF,MACKha,EAAI,EAAGA,EAAI3E,EAAEmR,OAAOtP,OAAQ8C,IACjCga,EAAc7b,KAAK6B,EAEvB3E,GAAE0e,YAAYC,IAOlB3e,EAAEic,SACErT,MACIkW,UAAW,KACXvC,aAAc,WACV,IAAK,GAAI5X,GAAI,EAAGA,EAAI3E,EAAEmR,OAAOtP,OAAQ8C,IAAK,CACtC,GAAIwN,GAAQnS,EAAEmR,OAAOiB,GAAGzN,GACpBJ,EAAS4N,EAAM,GAAGkB,kBAClB0L,GAAMxa,CACLvE,GAAEJ,OAAOoI,mBAAkB+W,GAAU/e,EAAE0O,UAC5C,IAAIsQ,GAAK,CACJ5e,OACD4e,EAAKD,EACLA,EAAK,EAET,IAAIE,GAAejf,EAAEJ,OAAOgJ,KAAKC,UACzBtD,KAAK2N,IAAI,EAAI3N,KAAKC,IAAI2M,EAAM,GAAGzL,UAAW,GAC1C,EAAInB,KAAK+P,IAAI/P,KAAK2N,IAAIf,EAAM,GAAGzL,SAAU,IAAK,EAClDuY,GAAe,GAAoB,EAAfA,IACpBjf,EAAEic,QAAQrT,KAAKkW,UAAYna,GAE/BwN,EACK9D,KACG6Q,QAASD,IAEZhY,UAAU,eAAiB8X,EAAK,OAASC,EAAK,cAI3D9C,cAAe,SAAUJ,GAErB,GADA9b,EAAEmR,OAAO6K,WAAWF,GAChB9b,EAAEJ,OAAOoI,kBAAiC,IAAb8T,EAAgB,CAC7C,GAAIgD,GAAyC,OAA7B9e,EAAEic,QAAQrT,KAAKkW,UAAqB9e,EAAEic,QAAQrT,KAAKkW,UAAY9e,EAAEqU,WACjFrU,GAAEmR,OAAOiB,GAAG0M,GAAWnO,cAAc,WAEjC,IAAK,GADDwO,IAAiB,sBAAuB,gBAAiB,iBAAkB,kBAAmB,mBACzFxa,EAAI,EAAGA,EAAIwa,EAActd,OAAQ8C,IACtC3E,EAAE+N,QAAQ+L,QAAQqF,EAAcxa,SAMpD6D,MACI+T,aAAc,WACV,GAAuB6C,GAAnBC,EAAgB,CAChBrf,GAAEJ,OAAO4I,KAAKC,SACVrI,KACAgf,EAAapf,EAAE+N,QAAQoC,KAAK,uBACF,IAAtBiP,EAAWvd,SACXud,EAAahe,EAAE,0CACfpB,EAAE+N,QAAQmQ,OAAOkB,IAErBA,EAAW/Q,KAAK3J,OAAQ1E,EAAEyE,MAAQ,SAGlC2a,EAAapf,EAAEG,UAAUgQ,KAAK,uBACJ,IAAtBiP,EAAWvd,SACXud,EAAahe,EAAE,0CACfpB,EAAEG,UAAU+d,OAAOkB,KAI/B,KAAK,GAAIza,GAAI,EAAGA,EAAI3E,EAAEmR,OAAOtP,OAAQ8C,IAAK,CACtC,GAAIwN,GAAQnS,EAAEmR,OAAOiB,GAAGzN,GACpB2a,EAAiB,GAAJ3a,EACb4a,EAAQha,KAAKyM,MAAMsN,EAAa,IAChCtf,GAAEmO,MACFmR,GAAcA,EACdC,EAAQha,KAAKyM,OAAOsN,EAAa,KAErC,IAAI5Y,GAAWnB,KAAK2N,IAAI3N,KAAK+P,IAAInD,EAAM,GAAGzL,SAAU,GAAI,IACpDqY,EAAK,EAAGC,EAAK,EAAGQ,EAAK,CACrB7a,GAAI,IAAM,GACVoa,EAAe,GAARQ,EAAYvf,EAAEiR,KACrBuO,EAAK,IAEC7a,EAAI,GAAK,IAAM,GACrBoa,EAAK,EACLS,EAAe,GAARD,EAAYvf,EAAEiR,OAEftM,EAAI,GAAK,IAAM,GACrBoa,EAAK/e,EAAEiR,KAAe,EAARsO,EAAYvf,EAAEiR,KAC5BuO,EAAKxf,EAAEiR,OAEDtM,EAAI,GAAK,IAAM,IACrBoa,GAAO/e,EAAEiR,KACTuO,EAAK,EAAIxf,EAAEiR,KAAgB,EAATjR,EAAEiR,KAAWsO,GAE/Bvf,EAAEmO,MACF4Q,GAAMA,GAGL3e,MACD4e,EAAKD,EACLA,EAAK,EAGT,IAAI9X,GAAY,YAAc7G,IAAQ,GAAKkf,GAAc,iBAAmBlf,IAAQkf,EAAa,GAAK,oBAAsBP,EAAK,OAASC,EAAK,OAASQ,EAAK,KAM7J,IALgB,GAAZ9Y,GAAiBA,EAAW,KAC5B2Y,EAAoB,GAAJ1a,EAAoB,GAAX+B,EACrB1G,EAAEmO,MAAKkR,EAAqB,IAAJ1a,EAAoB,GAAX+B,IAEzCyL,EAAMlL,UAAUA,GACZjH,EAAEJ,OAAO4I,KAAKD,aAAc,CAE5B,GAAIkX,GAAuBtN,EAAMhC,KAAd/P,IAAmB,4BAA0C,4BAC5Esf,EAAsBvN,EAAMhC,KAAd/P,IAAmB,6BAA2C,8BACpD,KAAxBqf,EAAa5d,SACb4d,EAAere,EAAE,oCAAsChB,IAAQ,OAAS,OAAS,YACjF+R,EAAM+L,OAAOuB,IAEU,IAAvBC,EAAY7d,SACZ6d,EAActe,EAAE,oCAAsChB,IAAQ,QAAU,UAAY,YACpF+R,EAAM+L,OAAOwB,GAEjB,EAAoBvN,EAAM,GAAGzL,SACzB+Y,EAAa5d,SAAQ4d,EAAa,GAAGvQ,MAAMgQ,SAAW/M,EAAM,GAAGzL,UAC/DgZ,EAAY7d,SAAQ6d,EAAY,GAAGxQ,MAAMgQ,QAAU/M,EAAM,GAAGzL,WAUxE,GAPA1G,EAAE+N,QAAQM,KACNsR,2BAA4B,YAAe3f,EAAEiR,KAAO,EAAK,KACzD2O,wBAAyB,YAAe5f,EAAEiR,KAAO,EAAK,KACtD4O,uBAAwB,YAAe7f,EAAEiR,KAAO,EAAK,KACrD6O,mBAAoB,YAAe9f,EAAEiR,KAAO,EAAK,OAGjDjR,EAAEJ,OAAO4I,KAAKC,OACd,GAAIrI,IACAgf,EAAWnY,UAAU,qBAAuBjH,EAAEyE,MAAQ,EAAIzE,EAAEJ,OAAO4I,KAAKE,cAAgB,QAAW1I,EAAEyE,MAAQ,EAAK,0CAA6CzE,EAAEJ,OAAO4I,KAAgB,YAAI,SAE3L,CACD,GAAIuX,GAAcxa,KAAKC,IAAI6Z,GAA4D,GAA3C9Z,KAAKyM,MAAMzM,KAAKC,IAAI6Z,GAAiB,IAC7EW,EAAa,KAAOza,KAAK0a,IAAkB,EAAdF,EAAkBxa,KAAKqU,GAAK,KAAO,EAAIrU,KAAK2a,IAAkB,EAAdH,EAAkBxa,KAAKqU,GAAK,KAAO,GAChHuG,EAASngB,EAAEJ,OAAO4I,KAAKG,YACvByX,EAASpgB,EAAEJ,OAAO4I,KAAKG,YAAcqX,EACrCzb,EAASvE,EAAEJ,OAAO4I,KAAKE,YAC3B0W,GAAWnY,UAAU,WAAakZ,EAAS,QAAUC,EAAS,uBAAyBpgB,EAAE0E,OAAS,EAAIH,GAAU,QAAWvE,EAAE0E,OAAS,EAAI0b,EAAU,uBAG5J,GAAIC,GAAWrgB,EAAEsgB,UAAYtgB,EAAEugB,aAAiBvgB,EAAEiR,KAAO,EAAK,CAC9DjR,GAAE+N,QAAQ9G,UAAU,qBAAuBoZ,EAAU,gBAAkBjgB,IAAQ,EAAIif,GAAiB,iBAAmBjf,KAASif,EAAgB,GAAK,SAEzJnD,cAAe,SAAUJ,GACrB9b,EAAEmR,OAAO6K,WAAWF,GAAU3L,KAAK,gHAAgH6L,WAAWF,GAC1J9b,EAAEJ,OAAO4I,KAAKC,SAAWrI,KACzBJ,EAAEG,UAAUgQ,KAAK,uBAAuB6L,WAAWF,KAI/D5T,WACIqU,aAAc,WAMV,IAAK,GALDtV,GAAYjH,EAAE0O,UACd8R,EAASpgB,KAAS6G,EAAYjH,EAAEyE,MAAQ,GAAKwC,EAAYjH,EAAE0E,OAAS,EACpEyD,EAAS/H,IAAQJ,EAAEJ,OAAOsI,UAAUC,QAASnI,EAAEJ,OAAOsI,UAAUC,OAChEuG,EAAY1O,EAAEJ,OAAOsI,UAAUG,MAE1B1D,EAAI,EAAG9C,EAAS7B,EAAEmR,OAAOtP,OAAYA,EAAJ8C,EAAYA,IAAK,CACvD,GAAIwN,GAAQnS,EAAEmR,OAAOiB,GAAGzN,GACpBuN,EAAYlS,EAAEqR,gBAAgB1M,GAC9B8b,EAActO,EAAM,GAAGkB,kBACvBqN,GAAoBF,EAASC,EAAcvO,EAAY,GAAKA,EAAYlS,EAAEJ,OAAOsI,UAAUI,SAE3FqY,EAAUvgB,IAAQ+H,EAASuY,EAAmB,EAC9CE,EAAUxgB,IAAQ,EAAI+H,EAASuY,EAE/BG,GAAcnS,EAAYnJ,KAAKC,IAAIkb,GAEnCI,EAAa1gB,IAAQ,EAAIJ,EAAEJ,OAAOsI,UAAUE,QAAU,EACtD2Y,EAAa3gB,IAAQJ,EAAEJ,OAAOsI,UAAUE,QAAU,EAAqB,CAGvE7C,MAAKC,IAAIub,GAAc,OAAOA,EAAa,GAC3Cxb,KAAKC,IAAIsb,GAAc,OAAOA,EAAa,GAC3Cvb,KAAKC,IAAIqb,GAAc,OAAOA,EAAa,GAC3Ctb,KAAKC,IAAImb,GAAW,OAAOA,EAAU,GACrCpb,KAAKC,IAAIob,GAAW,OAAOA,EAAU,EAEzC,IAAII,GAAiB,eAAiBD,EAAa,MAAQD,EAAa,MAAQD,EAAa,gBAAkBD,EAAU,gBAAkBD,EAAU,MAIrJ,IAFAxO,EAAMlL,UAAU+Z,GAChB7O,EAAM,GAAGjD,MAAM+R,QAAU1b,KAAKC,IAAID,KAAKga,MAAMmB,IAAqB,EAC9D1gB,EAAEJ,OAAOsI,UAAUK,aAAc,CAEjC,GAAIkX,GAAuBtN,EAAMhC,KAAd/P,IAAmB,4BAA0C,4BAC5Esf,EAAsBvN,EAAMhC,KAAd/P,IAAmB,6BAA2C,8BACpD,KAAxBqf,EAAa5d,SACb4d,EAAere,EAAE,oCAAsChB,IAAQ,OAAS,OAAS,YACjF+R,EAAM+L,OAAOuB,IAEU,IAAvBC,EAAY7d,SACZ6d,EAActe,EAAE,oCAAsChB,IAAQ,QAAU,UAAY,YACpF+R,EAAM+L,OAAOwB,IAEbD,EAAa5d,SAAQ4d,EAAa,GAAGvQ,MAAMgQ,QAAUwB,EAAmB,EAAIA,EAAmB,GAC/FhB,EAAY7d,SAAQ6d,EAAY,GAAGxQ,MAAMgQ,SAAYwB,EAAoB,GAAKA,EAAmB,IAK7G,GAAI1gB,EAAEwW,QAAQC,GAAI,CACd,GAAIyK,GAAKlhB,EAAE+N,QAAQ,GAAGmB,KACtBgS,GAAGC,kBAAoBX,EAAS,WAGxCtE,cAAe,SAAUJ,GACrB9b,EAAEmR,OAAO6K,WAAWF,GAAU3L,KAAK,gHAAgH6L,WAAWF,MAQ1K9b,EAAEyb,MACE2F,oBAAoB,EACpBC,iBAAkB,SAAU3f,GACxB,GAAqB,mBAAVA,IACa,IAApB1B,EAAEmR,OAAOtP,OAAb,CAEA,GAAIsQ,GAAQnS,EAAEmR,OAAOiB,GAAG1Q,GACpB4f,EAAMnP,EAAMhC,KAAK,qEACF,KAAfmR,EAAIzf,QAERyf,EAAIvhB,KAAK,WACL,GAAIwhB,GAAOngB,EAAEtB,KACbyhB,GAAKrT,SAAS,sBAEd,IAAIsB,GAAM+R,EAAKza,KAAK,WAEpB9G,GAAEsP,UAAUiS,EAAK,GAAI/R,GAAK,EAAO,WAC7B+R,EAAKza,KAAK,MAAO0I,GACjB+R,EAAKjD,WAAW,YAChBiD,EAAKrT,SAAS,sBAAsByF,YAAY,uBAChDxB,EAAMhC,KAAK,sCAAsC4N,SAEjD/d,EAAEwC,KAAK,mBAAoBxC,EAAGmS,EAAM,GAAIoP,EAAK,MAGjDvhB,EAAEwC,KAAK,kBAAmBxC,EAAGmS,EAAM,GAAIoP,EAAK,QAIpDC,KAAM,WACF,GAAIxhB,EAAEJ,OAAOkL,sBACT9K,EAAE+N,QAAQC,SAAS,IAAMhO,EAAEJ,OAAOuM,mBAAmBpM,KAAK,WACtDC,EAAEyb,KAAK4F,iBAAiBjgB,EAAEtB,MAAM4B,eAIpC,IAAI1B,EAAEJ,OAAOyJ,cAAgB,EACzB,IAAK,GAAI1E,GAAI3E,EAAEqU,YAAa1P,EAAI3E,EAAEqU,YAAcrU,EAAEJ,OAAOyJ,cAAgB1E,IACjE3E,EAAEmR,OAAOxM,IAAI3E,EAAEyb,KAAK4F,iBAAiB1c,OAI7C3E,GAAEyb,KAAK4F,iBAAiBrhB,EAAEqU,YAGlC,IAAIrU,EAAEJ,OAAOwL,sBAAuB,CAChC,GAAIqW,GAAYzhB,EAAE+N,QAAQC,SAAS,IAAMhO,EAAEJ,OAAOyM,eAC9CoV,GAAU5f,OAAS,GAAG7B,EAAEyb,KAAK4F,iBAAiBI,EAAU/f,QAE5D,IAAIggB,GAAY1hB,EAAE+N,QAAQC,SAAS,IAAMhO,EAAEJ,OAAO0M,eAC9CoV,GAAU7f,OAAS,GAAG7B,EAAEyb,KAAK4F,iBAAiBK,EAAUhgB,WAGpEuZ,kBAAmB,WACXjb,EAAEJ,OAAOuL,cACLnL,EAAEJ,OAAOyL,+BAAkCrL,EAAEJ,OAAOyL,+BAAiCrL,EAAEyb,KAAK2F,sBAC5FphB,EAAEyb,KAAK2F,oBAAqB,EAC5BphB,EAAEyb,KAAK+F,SAInBtG,gBAAiB,WACTlb,EAAEJ,OAAOuL,cAAgBnL,EAAEJ,OAAOyL,8BAClCrL,EAAEyb,KAAK+F,SASnBxhB,EAAE+I,WACEwM,IAAK,WACD,GAAKvV,EAAEJ,OAAOmJ,UAAd,CACA,GAAI4Y,GAAK3hB,EAAE+I,SACX4Y,GAAGC,MAAQxgB,EAAEpB,EAAEJ,OAAOmJ,WACtB4Y,EAAGE,KAAOF,EAAGC,MAAMzR,KAAK,0BACD,IAAnBwR,EAAGE,KAAKhgB,SACR8f,EAAGE,KAAOzgB,EAAE,6CACZugB,EAAGC,MAAM1D,OAAOyD,EAAGE,OAEvBF,EAAGE,KAAK,GAAG3S,MAAMzK,MAAQ,GACzBkd,EAAGE,KAAK,GAAG3S,MAAMxK,OAAS,GAC1Bid,EAAGG,UAAY1hB,IAAQuhB,EAAGC,MAAM,GAAGG,YAAcJ,EAAGC,MAAM,GAAGI,aAE7DL,EAAGM,QAAUjiB,EAAEiR,KAAOjR,EAAE0R,YACxBiQ,EAAGO,YAAcP,EAAGM,SAAWN,EAAGG,UAAY9hB,EAAEiR,MAChD0Q,EAAGQ,SAAWR,EAAGG,UAAYH,EAAGM,QAE5B7hB,IACAuhB,EAAGE,KAAK,GAAG3S,MAAMzK,MAAQkd,EAAGQ,SAAW,KAGvCR,EAAGE,KAAK,GAAG3S,MAAMxK,OAASid,EAAGQ,SAAW,KAIxCR,EAAGC,MAAM,GAAG1S,MAAMkT,QADlBT,EAAGM,SAAW,EACc,OAGA,GAE5BjiB,EAAEJ,OAAOoJ,gBACT2Y,EAAGC,MAAM,GAAG1S,MAAMgQ,QAAU,KAGpC3C,aAAc,WACV,GAAKvc,EAAEJ,OAAOmJ,UAAd,CACA,GAGIsZ,GAFAV,EAAK3hB,EAAE+I,UAIPuZ,GAHYtiB,EAAE0O,WAAa,EAGjBiT,EAAGQ,SACjBE,IAAUV,EAAGG,UAAYH,EAAGQ,UAAYniB,EAAE0G,SACtC1G,EAAEmO,KAAO/N,KACTiiB,GAAUA,EACNA,EAAS,GACTC,EAAUX,EAAGQ,SAAWE,EACxBA,EAAS,IAEHA,EAASV,EAAGQ,SAAWR,EAAGG,YAChCQ,EAAUX,EAAGG,UAAYO,IAIhB,EAATA,GACAC,EAAUX,EAAGQ,SAAWE,EACxBA,EAAS,GAEJA,EAASV,EAAGQ,SAAWR,EAAGG,YAC/BQ,EAAUX,EAAGG,UAAYO,GAG7BjiB,KAEIuhB,EAAGE,KAAK5a,UADRjH,EAAE2N,QAAQE,aACQ,eAAiB,EAAW,YAG5B,cAAgB,EAAW,OAEjD8T,EAAGE,KAAK,GAAG3S,MAAMzK,MAAQ6d,EAAU,OAI/BX,EAAGE,KAAK5a,UADRjH,EAAE2N,QAAQE,aACQ,oBAAsB,EAAW,SAGjC,cAAgB,EAAW,OAEjD8T,EAAGE,KAAK,GAAG3S,MAAMxK,OAAS4d,EAAU,MAEpCtiB,EAAEJ,OAAOoJ,gBACTyH,aAAakR,EAAGY,SAChBZ,EAAGC,MAAM,GAAG1S,MAAMgQ,QAAU,EAC5ByC,EAAGY,QAAU/hB,WAAW,WACpBmhB,EAAGC,MAAM,GAAG1S,MAAMgQ,QAAU,EAC5ByC,EAAGC,MAAM5F,WAAW,MACrB,QAGXE,cAAe,SAAUJ,GAChB9b,EAAEJ,OAAOmJ,WACd/I,EAAE+I,UAAU8Y,KAAK7F,WAAWF,KAOpC9b,EAAEmc,YACEI,aAAc,SAAU7N,EAAWqN,GAC/B,GACIiE,GAAYwC,EADZC,EAAaziB,EAAEJ,OAAO8L,OAE1B,IAAI1L,EAAE0iB,QAAQD,GACV,IAAK,GAAI9d,GAAI,EAAGA,EAAI8d,EAAW5gB,OAAQ8C,IAC/B8d,EAAW9d,KAAOoX,GAAgB0G,EAAW9d,YAAc1E,UAC3DyO,EAAY+T,EAAW9d,GAAGwJ,KAA0C,eAAnCsU,EAAW9d,GAAG/E,OAAOS,WAA8BL,EAAE0O,UAAY1O,EAAE0O,UACpGsR,GAAcyC,EAAW9d,GAAGsB,eAAiBwc,EAAW9d,GAAGiM,iBAAmB5Q,EAAEiG,eAAiBjG,EAAE4Q,gBACnG4R,GAAuB9T,EAAY1O,EAAE4Q,gBAAkBoP,EAAayC,EAAW9d,GAAGiM,eAC9E5Q,EAAEJ,OAAO+L,iBACT6W,EAAsBC,EAAW9d,GAAGsB,eAAiBuc,GAEzDC,EAAW9d,GAAGyB,eAAeoc,GAC7BC,EAAW9d,GAAGwB,oBAAoBqc,GAAqB,EAAOxiB,GAC9DyiB,EAAW9d,GAAG0B,yBAIjBoc,aAAsBxiB,SAAU8b,IAAiB0G,IACtD/T,EAAY+T,EAAWtU,KAAuC,eAAhCsU,EAAW7iB,OAAOS,WAA8BL,EAAE0O,UAAY1O,EAAE0O,UAC9FsR,GAAcyC,EAAWxc,eAAiBwc,EAAW7R,iBAAmB5Q,EAAEiG,eAAiBjG,EAAE4Q,gBAC7F4R,GAAuB9T,EAAY1O,EAAE4Q,gBAAkBoP,EAAayC,EAAW7R,eAC3E5Q,EAAEJ,OAAO+L,iBACT6W,EAAsBC,EAAWxc,eAAiBuc,GAEtDC,EAAWrc,eAAeoc,GAC1BC,EAAWtc,oBAAoBqc,GAAqB,EAAOxiB,GAC3DyiB,EAAWpc,sBAGnB6V,cAAe,SAAUJ,EAAUC,GAC/B,GAAI0G,GAAaziB,EAAEJ,OAAO8L,OAC1B,IAAI1L,EAAE0iB,QAAQD,GACV,IAAK,GAAI9d,GAAI,EAAGA,EAAI8d,EAAW5gB,OAAQ8C,IAC/B8d,EAAW9d,KAAOoX,GAAgB0G,EAAW9d,YAAc1E,SAC3DwiB,EAAW9d,GAAGuB,qBAAqB4V,EAAU9b,OAIhDyiB,aAAsBxiB,SAAU8b,IAAiB0G,GACtDA,EAAWvc,qBAAqB4V,EAAU9b,KAQtDA,EAAEmJ,SACEwZ,KAAM,WACF,GAAK3iB,EAAEJ,OAAOuJ,QAAd,CACAnJ,EAAEmJ,QAAQyZ,aAAc,CACxB,IAAIC,GAAOrf,SAASsf,SAASD,KAAKpR,QAAQ,IAAK,GAC/C,IAAKoR,EAEL,IAAK,GADDpb,GAAQ,EACH9C,EAAI,EAAG9C,EAAS7B,EAAEmR,OAAOtP,OAAYA,EAAJ8C,EAAYA,IAAK,CACvD,GAAIwN,GAAQnS,EAAEmR,OAAOiB,GAAGzN,GACpBoe,EAAY5Q,EAAMrL,KAAK,YAC3B,IAAIic,IAAcF,IAAS1Q,EAAMiI,SAASpa,EAAEJ,OAAOwM,qBAAsB,CACrE,GAAI1K,GAAQyQ,EAAMzQ,OAClB1B,GAAEyV,QAAQ/T,EAAO+F,EAAOzH,EAAEJ,OAAOsN,oBAAoB,OAIjEwO,QAAS,WACA1b,EAAEmJ,QAAQyZ,aAAgB5iB,EAAEJ,OAAOuJ,UACxC3F,SAASsf,SAASD,KAAO7iB,EAAEmR,OAAOiB,GAAGpS,EAAEqU,aAAavN,KAAK,cAAgB,MAiEjF9G,EAAEgjB,uBAAyB,WACvB5hB,EAAEoC,UAAUyf,IAAI,UAAWlgB,IAE/B/C,EAAEkjB,sBAAwB,WACtB9hB,EAAEoC,UAAU2f,GAAG,UAAWpgB,IAO9B/C,EAAEmF,aAAc,EAChBnF,EAAEwG,sBAAuB,GAAKF,OAAQC,UAClCvG,EAAEJ,OAAOsJ,kBAAmB,CAI5B,GAH8BtH,SAA1B4B,SAAS4f,eACTpjB,EAAEmF,YAAc,eAEfnF,EAAEmF,YACH,IACI,GAAIke,YAAW,SACfrjB,EAAEmF,YAAc,QAClB,MAAOlE,IAERjB,EAAEmF,cACHnF,EAAEmF,YAAc,kBAyExBnF,EAAEsjB,yBAA2B,WACzB,MAAKtjB,GAAEmF,aACPnF,EAAEG,UAAU8iB,IAAIjjB,EAAEmF,YAAaF,IACxB,IAFoB,GAK/BjF,EAAEujB,wBAA0B,WACxB,MAAKvjB,GAAEmF,aACPnF,EAAEG,UAAUgjB,GAAGnjB,EAAEmF,YAAaF,IACvB,IAFoB,GA2C/BjF,EAAE8I,UACEyT,aAAc,WACVvc,EAAEG,UAAU6N,SAAS,8EAA8EjO,KAAK,WACpG0G,EAAqB3G,KAAME,EAAE0G,YAGjC1G,EAAEmR,OAAOpR,KAAK,WACV,GAAIoS,GAAQ/Q,EAAEtB,KACdqS,GAAMhC,KAAK,8EAA8EpQ,KAAK,WAC1F,GAAI2G,GAAWnB,KAAK+P,IAAI/P,KAAK2N,IAAIf,EAAM,GAAGzL,SAAU,IAAK,EACzDD,GAAqB3G,KAAM4G,QAIvCwV,cAAe,SAAUJ,GACG,mBAAbA,KAA0BA,EAAW9b,EAAEJ,OAAO6H,OACzDzH,EAAEG,UAAUgQ,KAAK,8EAA8EpQ,KAAK,WAChG,GAAIoB,GAAKC,EAAEtB,MACP0jB,EAAmBxc,SAAS7F,EAAG2F,KAAK,iCAAkC,KAAOgV,CAChE,KAAbA,IAAgB0H,EAAmB,GACvCriB,EAAG6a,WAAWwH,OAS1BxjB,EAAEyjB,WACF,KAAK,GAAIC,KAAU1jB,GAAE2jB,QAAS,CAC1B,GAAIhd,GAAI3G,EAAE2jB,QAAQD,GAAQ1jB,EAAGA,EAAEJ,OAAO8jB,GAClC/c,IAAG3G,EAAEyjB,SAAS3gB,KAAK6D,GAqR3B,MAlRA3G,GAAE4jB,YAAc,SAAUzc,GACtB,IAAK,GAAIxC,GAAI,EAAGA,EAAI3E,EAAEyjB,SAAS5hB,OAAQ8C,IAC/BwC,IAAanH,GAAEyjB,SAAS9e,IACxB3E,EAAEyjB,SAAS9e,GAAGwC,GAAW0c,UAAU,GAAIA,UAAU,GAAIA,UAAU,GAAIA,UAAU,GAAIA,UAAU,KAmBvG7jB,EAAE8jB,yBAGF9jB,EAAEwC,KAAO,SAAU2E,GAEXnH,EAAEJ,OAAOuH,IACTnH,EAAEJ,OAAOuH,GAAW0c,UAAU,GAAIA,UAAU,GAAIA,UAAU,GAAIA,UAAU,GAAIA,UAAU,GAE1F,IAAIlf,EAEJ,IAAI3E,EAAE8jB,sBAAsB3c,GACxB,IAAKxC,EAAI,EAAGA,EAAI3E,EAAE8jB,sBAAsB3c,GAAWtF,OAAQ8C,IACvD3E,EAAE8jB,sBAAsB3c,GAAWxC,GAAGkf,UAAU,GAAIA,UAAU,GAAIA,UAAU,GAAIA,UAAU,GAAIA,UAAU,GAI5G7jB,GAAE4jB,aAAa5jB,EAAE4jB,YAAYzc,EAAW0c,UAAU,GAAIA,UAAU,GAAIA,UAAU,GAAIA,UAAU,GAAIA,UAAU,KAElH7jB,EAAEmjB,GAAK,SAAUhc,EAAW4c,GAIxB,MAHA5c,GAAYD,EAAmBC,GAC1BnH,EAAE8jB,sBAAsB3c,KAAYnH,EAAE8jB,sBAAsB3c,OACjEnH,EAAE8jB,sBAAsB3c,GAAWrE,KAAKihB,GACjC/jB,GAEXA,EAAEijB,IAAM,SAAU9b,EAAW4c,GACzB,GAAIpf,EAEJ,IADAwC,EAAYD,EAAmBC,GACR,mBAAZ4c,GAGP,MADA/jB,GAAE8jB,sBAAsB3c,MACjBnH,CAEX,IAAKA,EAAE8jB,sBAAsB3c,IAA4D,IAA9CnH,EAAE8jB,sBAAsB3c,GAAWtF,OAA9E,CACA,IAAK8C,EAAI,EAAGA,EAAI3E,EAAE8jB,sBAAsB3c,GAAWtF,OAAQ8C,IACpD3E,EAAE8jB,sBAAsB3c,GAAWxC,KAAOof,GAAS/jB,EAAE8jB,sBAAsB3c,GAAW6c,OAAOrf,EAAG,EAEvG,OAAO3E,KAEXA,EAAEikB,KAAO,SAAU9c,EAAW4c,GAC1B5c,EAAYD,EAAmBC,EAC/B,IAAI+c,GAAW,WACXH,EAAQF,UAAU,GAAIA,UAAU,GAAIA,UAAU,GAAIA,UAAU,GAAIA,UAAU,IAC1E7jB,EAAEijB,IAAI9b,EAAW+c,GAGrB,OADAlkB,GAAEmjB,GAAGhc,EAAW+c,GACTlkB,GAIXA,EAAE6M,MACEsX,cAAe,SAAUC,GAErB,MADAA,GAAI,GAAGC,SAAW,IACXD,GAEXE,QAAS,SAAUF,EAAKG,GAEpB,MADAH,GAAItd,KAAK,OAAQyd,GACVH,GAGXI,SAAU,SAAUJ,EAAKK,GAErB,MADAL,GAAItd,KAAK,aAAc2d,GAChBL,GAGXvP,QAAS,SAAUuP,GAEf,MADAA,GAAItd,KAAK,iBAAiB,GACnBsd,GAGXtP,OAAQ,SAAUsP,GAEd,MADAA,GAAItd,KAAK,iBAAiB,GACnBsd,GAGXrN,WAAY,SAAU2N,GACI,KAAlBA,EAAMxhB,UACN9B,EAAEsjB,EAAMrjB,QAAQC,GAAGtB,EAAEJ,OAAO+K,aAC5B3K,EAAE8W,YAAY4N,GAEV1kB,EAAE6M,KAAK8X,OADP3kB,EAAEY,MACYZ,EAAEJ,OAAOglB,aAGT5kB,EAAEJ,OAAOilB,eAGtBzjB,EAAEsjB,EAAMrjB,QAAQC,GAAGtB,EAAEJ,OAAOgL,cACjC5K,EAAEgX,YAAY0N,GAEV1kB,EAAE6M,KAAK8X,OADP3kB,EAAEkU,YACYlU,EAAEJ,OAAOklB,cAGT9kB,EAAEJ,OAAOmlB,iBAKnCC,WAAY5jB,EAAE,sFAEdujB,OAAQ,SAAUM,GACd,GAAIC,GAAellB,EAAE6M,KAAKmY,UACE,KAAxBE,EAAarjB,SACjBqjB,EAAahQ,KAAK,IAClBgQ,EAAahQ,KAAK+P,KAEtBtC,KAAM,WAEF,GAAI3iB,EAAEJ,OAAO+K,WAAY,CACrB,GAAIA,GAAavJ,EAAEpB,EAAEJ,OAAO+K,WAC5B3K,GAAE6M,KAAKsX,cAAcxZ,GACrB3K,EAAE6M,KAAKyX,QAAQ3Z,EAAY,UAC3B3K,EAAE6M,KAAK2X,SAAS7Z,EAAY3K,EAAEJ,OAAOilB,cAEzC,GAAI7kB,EAAEJ,OAAOgL,WAAY,CACrB,GAAIA,GAAaxJ,EAAEpB,EAAEJ,OAAOgL,WAC5B5K,GAAE6M,KAAKsX,cAAcvZ,GACrB5K,EAAE6M,KAAKyX,QAAQ1Z,EAAY,UAC3B5K,EAAE6M,KAAK2X,SAAS5Z,EAAY5K,EAAEJ,OAAOmlB,cAGzC3jB,EAAEpB,EAAEG,WAAW+d,OAAOle,EAAE6M,KAAKmY,aAEjCG,QAAS,WACDnlB,EAAE6M,KAAKmY,YAAchlB,EAAE6M,KAAKmY,WAAWnjB,OAAS,GAAG7B,EAAE6M,KAAKmY,WAAWjH,WAQjF/d,EAAE2iB,KAAO,WACD3iB,EAAEJ,OAAOa,MAAMT,EAAE8d,aACrB9d,EAAE8Q,sBACF9Q,EAAEkR,mBACFlR,EAAE+U,mBACE/U,EAAEJ,OAAOmJ,WAAa/I,EAAE+I,WACxB/I,EAAE+I,UAAUwM,MAEQ,UAApBvV,EAAEJ,OAAOqI,QAAsBjI,EAAEic,QAAQjc,EAAEJ,OAAOqI,UAC7CjI,EAAEJ,OAAOa,MAAMT,EAAEoG,iBACtBpG,EAAEic,QAAQjc,EAAEJ,OAAOqI,QAAQsU,gBAE3Bvc,EAAEJ,OAAOa,KACTT,EAAEyV,QAAQzV,EAAEJ,OAAO4H,aAAexH,EAAEyL,aAAc,EAAGzL,EAAEJ,OAAOsN,qBAG9DlN,EAAEyV,QAAQzV,EAAEJ,OAAO4H,aAAc,EAAGxH,EAAEJ,OAAOsN,oBACf,IAA1BlN,EAAEJ,OAAO4H,eACLxH,EAAE8I,UAAY9I,EAAEJ,OAAOkJ,UAAU9I,EAAE8I,SAASyT,eAC5Cvc,EAAEyb,MAAQzb,EAAEJ,OAAOuL,aAAanL,EAAEyb,KAAK+F,SAGnDxhB,EAAEkX,eACElX,EAAEJ,OAAOuC,UAAYnC,EAAE2N,QAAQxL,UAC/BnC,EAAE0d,gBAEF1d,EAAEJ,OAAO0L,gBAAkBtL,EAAEJ,OAAOuL,aACpCnL,EAAEsL,gBAEFtL,EAAEJ,OAAOU,UACTN,EAAEwQ,gBAEFxQ,EAAEJ,OAAOqJ,iBACLjJ,EAAEkjB,uBAAuBljB,EAAEkjB,wBAE/BljB,EAAEJ,OAAOsJ,mBACLlJ,EAAEujB,yBAAyBvjB,EAAEujB,0BAEjCvjB,EAAEJ,OAAOuJ,SACLnJ,EAAEmJ,SAASnJ,EAAEmJ,QAAQwZ,OAEzB3iB,EAAEJ,OAAOiN,MAAQ7M,EAAE6M,MAAM7M,EAAE6M,KAAK8V,OACpC3iB,EAAEwC,KAAK,SAAUxC,IAIrBA,EAAEolB,cAAgB,WAEdplB,EAAEG,UAAUwT,YAAY3T,EAAEsN,WAAWmB,KAAK,MAAM6P,WAAW,SAG3Dte,EAAE+N,QAAQuQ,WAAW,SAGjBte,EAAEmR,QAAUnR,EAAEmR,OAAOtP,QACrB7B,EAAEmR,OACGwC,aACC3T,EAAEJ,OAAOuM,kBACTnM,EAAEJ,OAAOsM,iBACTlM,EAAEJ,OAAOyM,eACTrM,EAAEJ,OAAO0M,gBACTmC,KAAK,MACN6P,WAAW,SACXA,WAAW,sBACXA,WAAW,mBAIhBte,EAAEiO,qBAAuBjO,EAAEiO,oBAAoBpM,QAC/C7B,EAAEiO,oBAAoB0F,YAAY3T,EAAEJ,OAAO+M,uBAE3C3M,EAAE2U,SAAW3U,EAAE2U,QAAQ9S,QACvB7B,EAAE2U,QAAQhB,YAAY3T,EAAEJ,OAAO6M,mBAI/BzM,EAAEJ,OAAOgL,YAAYxJ,EAAEpB,EAAEJ,OAAOgL,YAAY+I,YAAY3T,EAAEJ,OAAO8M,qBACjE1M,EAAEJ,OAAO+K,YAAYvJ,EAAEpB,EAAEJ,OAAO+K,YAAYgJ,YAAY3T,EAAEJ,OAAO8M,qBAGjE1M,EAAEJ,OAAOmJ,WAAa/I,EAAE+I,YACpB/I,EAAE+I,UAAU6Y,OAAS5hB,EAAE+I,UAAU6Y,MAAM/f,QAAQ7B,EAAE+I,UAAU6Y,MAAMtD,WAAW,SAC5Ete,EAAE+I,UAAU8Y,MAAQ7hB,EAAE+I,UAAU8Y,KAAKhgB,QAAQ7B,EAAE+I,UAAU8Y,KAAKvD,WAAW,WAKrFte,EAAEmlB,QAAU,SAAUE,EAAgBD,GAElCplB,EAAEmX,eAEFnX,EAAEc,eAEEd,EAAEJ,OAAOa,MACTT,EAAEqe,cAGF+G,GACAplB,EAAEolB,gBAGNplB,EAAE4d,sBAEE5d,EAAEJ,OAAOqJ,iBACLjJ,EAAEgjB,wBAAwBhjB,EAAEgjB,yBAEhChjB,EAAEJ,OAAOsJ,mBACLlJ,EAAEsjB,0BAA0BtjB,EAAEsjB,2BAGlCtjB,EAAEJ,OAAOiN,MAAQ7M,EAAE6M,MAAM7M,EAAE6M,KAAKsY,UAEpCnlB,EAAEwC,KAAK,aAEH6iB,KAAmB,IAAOrlB,EAAI,OAGtCA,EAAE2iB,OAKK3iB,IAOXC,OAAOqlB,WACHhF,SAAU,WACN,GAAIiF,GAAK5P,UAAU6P,UAAU7hB,aAC7B,OAAQ4hB,GAAGxe,QAAQ,WAAa,GAAKwe,EAAGxe,QAAQ,UAAY,GAAKwe,EAAGxe,QAAQ,WAAa,KAE7FwZ,YAAa,+CAA+CkF,KAAK9P,UAAU6P,WAC3E9C,QAAS,SAAUgD,GACf,MAAgD,mBAAzCC,OAAOL,UAAUhI,SAASsI,MAAMF,IAK3ClP,SACIC,GAAIvW,OAAOyV,UAAUC,gBAAkB1V,OAAOyV,UAAUE,iBACxDgE,QAAU3Z,OAAOyV,UAAUE,kBAAoB3V,OAAOyV,UAAUkQ,iBAAmB,GAAO3lB,OAAOyV,UAAUC,gBAAkB1V,OAAOyV,UAAUmQ,eAAiB,GAKnKvX,OAAQ,WACJ,GAAIgX,GAAK5P,UAAU6P,UACfhX,EAAU+W,EAAGQ,MAAM,+BACnBC,EAAOT,EAAGQ,MAAM,wBAEhBE,GADOV,EAAGQ,MAAM,4BACNC,GAAQT,EAAGQ,MAAM,0BAC/B,QACIlP,IAAKmP,GAAQC,GAAUD,EACvBxX,QAASA,MAMjBb,SACIG,MAAS5N,OAAOgmB,WAAaA,UAAUpY,SAAU,GAAS,WACtD,SAAW,gBAAkB5N,SAAWA,OAAOimB,eAAiB3iB,mBAAoB2iB,mBAGxFtY,aAAgB3N,OAAOgmB,WAAaA,UAAUE,mBAAoB,GAAS,WACvE,GAAIC,GAAM7iB,SAAS8iB,cAAc,OAAOpX,KACxC,OAAQ,qBAAuBmX,IAAO,kBAAoBA,IAAO,gBAAkBA,IAAO,iBAAmBA,IAAO,eAAiBA,MAGzIzY,QAAS,WAGL,IAAK,GAFDyY,GAAM7iB,SAAS8iB,cAAc,OAAOpX,MACpCqX,EAAS,yKAA2KhJ,MAAM,KACrL5Y,EAAI,EAAGA,EAAI4hB,EAAO1kB,OAAQ8C,IAC/B,GAAI4hB,EAAO5hB,IAAM0hB,GAAK,OAAO,KAIrClkB,SAAU,WACN,MAAQ,oBAAsBjC,SAAU,0BAA4BA,YAM5EyjB,WA8pBJ,KAAK,GAvpBDpW,IAAO,WACP,GAAIA,GAAO,SAAUmY,GACjB,GAAIc,GAAQ1mB,KAAM6E,EAAI,CAEtB,KAAKA,EAAI,EAAGA,EAAI+gB,EAAI7jB,OAAQ8C,IACxB6hB,EAAM7hB,GAAK+gB,EAAI/gB,EAInB,OAFA6hB,GAAM3kB,OAAS6jB,EAAI7jB,OAEZ/B,MAEPsB,EAAI,SAAUF,EAAUulB,GACxB,GAAIf,MAAU/gB,EAAI,CAClB,IAAIzD,IAAaulB,GACTvlB,YAAoBqM,GACpB,MAAOrM,EAGf,IAAIA,EAEA,GAAwB,gBAAbA,GAAuB,CAC9B,GAAIwlB,GAAKC,EAAYzR,EAAOhU,EAAS0lB,MACrC,IAAI1R,EAAKnO,QAAQ,MAAQ,GAAKmO,EAAKnO,QAAQ,MAAQ,EAAG,CAClD,GAAI8f,GAAW,KAQf,KAP4B,IAAxB3R,EAAKnO,QAAQ,SAAc8f,EAAW,MACd,IAAxB3R,EAAKnO,QAAQ,SAAc8f,EAAW,UACd,IAAxB3R,EAAKnO,QAAQ,QAAwC,IAAxBmO,EAAKnO,QAAQ,UAAc8f,EAAW,MACxC,IAA3B3R,EAAKnO,QAAQ,YAAiB8f,EAAW,SACb,IAA5B3R,EAAKnO,QAAQ,aAAkB8f,EAAW,UAC9CF,EAAanjB,SAAS8iB,cAAcO,GACpCF,EAAWG,UAAY5lB,EAClByD,EAAI,EAAGA,EAAIgiB,EAAWI,WAAWllB,OAAQ8C,IAC1C+gB,EAAI5iB,KAAK6jB,EAAWI,WAAWpiB,QAYnC,KAFI+hB,EANCD,GAA2B,MAAhBvlB,EAAS,IAAeA,EAAS6kB,MAAM,aAM5CU,GAAWjjB,UAAUwjB,iBAAiB9lB,IAJtCsC,SAASyjB,eAAe/lB,EAASqc,MAAM,KAAK,KAMlD5Y,EAAI,EAAGA,EAAI+hB,EAAI7kB,OAAQ8C,IACpB+hB,EAAI/hB,IAAI+gB,EAAI5iB,KAAK4jB,EAAI/hB,QAKhC,IAAIzD,EAASM,UAAYN,IAAahB,QAAUgB,IAAasC,SAC9DkiB,EAAI5iB,KAAK5B,OAGR,IAAIA,EAASW,OAAS,GAAKX,EAAS,GAAGM,SACxC,IAAKmD,EAAI,EAAGA,EAAIzD,EAASW,OAAQ8C,IAC7B+gB,EAAI5iB,KAAK5B,EAASyD,GAI9B,OAAO,IAAI4I,GAAKmY,GAykBpB,OAvkBAnY,GAAK+X,WAEDpX,SAAU,SAAUgZ,GAChB,GAAyB,mBAAdA,GACP,MAAOpnB,KAGX,KAAK,GADDqnB,GAAUD,EAAU3J,MAAM,KACrB5Y,EAAI,EAAGA,EAAIwiB,EAAQtlB,OAAQ8C,IAChC,IAAK,GAAIyiB,GAAI,EAAGA,EAAItnB,KAAK+B,OAAQulB,IAC7BtnB,KAAKsnB,GAAGC,UAAUC,IAAIH,EAAQxiB,GAGtC,OAAO7E,OAEX6T,YAAa,SAAUuT,GAEnB,IAAK,GADDC,GAAUD,EAAU3J,MAAM,KACrB5Y,EAAI,EAAGA,EAAIwiB,EAAQtlB,OAAQ8C,IAChC,IAAK,GAAIyiB,GAAI,EAAGA,EAAItnB,KAAK+B,OAAQulB,IAC7BtnB,KAAKsnB,GAAGC,UAAUtJ,OAAOoJ,EAAQxiB,GAGzC,OAAO7E,OAEXsa,SAAU,SAAU8M,GAChB,MAAKpnB,MAAK,GACEA,KAAK,GAAGunB,UAAUE,SAASL,IADlB,GAGzB7M,YAAa,SAAU6M,GAEnB,IAAK,GADDC,GAAUD,EAAU3J,MAAM,KACrB5Y,EAAI,EAAGA,EAAIwiB,EAAQtlB,OAAQ8C,IAChC,IAAK,GAAIyiB,GAAI,EAAGA,EAAItnB,KAAK+B,OAAQulB,IAC7BtnB,KAAKsnB,GAAGC,UAAUG,OAAOL,EAAQxiB,GAGzC,OAAO7E,OAEXgH,KAAM,SAAU2gB,EAAOC,GACnB,GAAyB,IAArB7D,UAAUhiB,QAAiC,gBAAV4lB,GAEjC,MAAI3nB,MAAK,GAAWA,KAAK,GAAGuQ,aAAaoX,GAC7B7lB,MAIZ,KAAK,GAAI+C,GAAI,EAAGA,EAAI7E,KAAK+B,OAAQ8C,IAC7B,GAAyB,IAArBkf,UAAUhiB,OAEV/B,KAAK6E,GAAGgjB,aAAaF,EAAOC,OAI5B,KAAK,GAAIE,KAAYH,GACjB3nB,KAAK6E,GAAGijB,GAAYH,EAAMG,GAC1B9nB,KAAK6E,GAAGgjB,aAAaC,EAAUH,EAAMG,GAIjD,OAAO9nB,OAGfwe,WAAY,SAAUxX,GAClB,IAAK,GAAInC,GAAI,EAAGA,EAAI7E,KAAK+B,OAAQ8C,IAC7B7E,KAAK6E,GAAGkjB,gBAAgB/gB,EAE5B,OAAOhH,OAEX4N,KAAM,SAAUoa,EAAKJ,GACjB,GAAqB,mBAAVA,GAAuB,CAE9B,GAAI5nB,KAAK,GAAI,CACT,GAAIioB,GAAUjoB,KAAK,GAAGuQ,aAAa,QAAUyX,EAC7C,OAAIC,GAAgBA,EACXjoB,KAAK,GAAGkoB,wBAA2BF,IAAOhoB,MAAK,GAAGkoB,uBAAgCloB,KAAK,GAAGkoB,uBAAuBF,GAC9GlmB,OAEX,MAAOA,QAIZ,IAAK,GAAI+C,GAAI,EAAGA,EAAI7E,KAAK+B,OAAQ8C,IAAK,CAClC,GAAIxD,GAAKrB,KAAK6E,EACTxD,GAAG6mB,yBAAwB7mB,EAAG6mB,2BACnC7mB,EAAG6mB,uBAAuBF,GAAOJ,EAErC,MAAO5nB,OAIfmH,UAAY,SAAUA,GAClB,IAAK,GAAItC,GAAI,EAAGA,EAAI7E,KAAK+B,OAAQ8C,IAAK,CAClC,GAAIsjB,GAAUnoB,KAAK6E,GAAGuK,KACtB+Y,GAAQjL,gBAAkBiL,EAAQ9K,YAAc8K,EAAQ7K,YAAc6K,EAAQhL,aAAegL,EAAQ/K,WAAa+K,EAAQhhB,UAAYA,EAE1I,MAAOnH,OAEXkc,WAAY,SAAUF,GACM,gBAAbA,KACPA,GAAsB,KAE1B,KAAK,GAAInX,GAAI,EAAGA,EAAI7E,KAAK+B,OAAQ8C,IAAK,CAClC,GAAIsjB,GAAUnoB,KAAK6E,GAAGuK,KACtB+Y,GAAQC,yBAA2BD,EAAQE,qBAAuBF,EAAQG,qBAAuBH,EAAQI,sBAAwBJ,EAAQK,oBAAsBL,EAAQM,mBAAqBzM,EAEhM,MAAOhc,OAGXqjB,GAAI,SAAUhc,EAAWqhB,EAAgBC,EAAUC,GAC/C,QAASC,GAAgB1nB,GACrB,GAAII,GAASJ,EAAEI,MACf,IAAID,EAAEC,GAAQC,GAAGknB,GAAiBC,EAASG,KAAKvnB,EAAQJ,OAGpD,KAAK,GADDM,GAAUH,EAAEC,GAAQE,UACfsnB,EAAI,EAAGA,EAAItnB,EAAQM,OAAQgnB,IAC5BznB,EAAEG,EAAQsnB,IAAIvnB,GAAGknB,IAAiBC,EAASG,KAAKrnB,EAAQsnB,GAAI5nB,GAI5E,GACI0D,GAAGyiB,EADH0B,EAAS3hB,EAAUoW,MAAM,IAE7B,KAAK5Y,EAAI,EAAGA,EAAI7E,KAAK+B,OAAQ8C,IACzB,GAA8B,kBAAnB6jB,IAAiCA,KAAmB,EAM3D,IAJ8B,kBAAnBA,KACPC,EAAW5E,UAAU,GACrB6E,EAAU7E,UAAU,KAAM,GAEzBuD,EAAI,EAAGA,EAAI0B,EAAOjnB,OAAQulB,IAC3BtnB,KAAK6E,GAAGokB,iBAAiBD,EAAO1B,GAAIqB,EAAUC,OAKlD,KAAKtB,EAAI,EAAGA,EAAI0B,EAAOjnB,OAAQulB,IACtBtnB,KAAK6E,GAAGqkB,oBAAmBlpB,KAAK6E,GAAGqkB,sBACxClpB,KAAK6E,GAAGqkB,kBAAkBlmB,MAAM2lB,SAAUA,EAAUQ,aAAcN,IAClE7oB,KAAK6E,GAAGokB,iBAAiBD,EAAO1B,GAAIuB,EAAiBD,EAKjE,OAAO5oB,OAEXmjB,IAAK,SAAU9b,EAAWqhB,EAAgBC,EAAUC,GAEhD,IAAK,GADDI,GAAS3hB,EAAUoW,MAAM,KACpB5Y,EAAI,EAAGA,EAAImkB,EAAOjnB,OAAQ8C,IAC/B,IAAK,GAAIyiB,GAAI,EAAGA,EAAItnB,KAAK+B,OAAQulB,IAC7B,GAA8B,kBAAnBoB,IAAiCA,KAAmB,EAE7B,kBAAnBA,KACPC,EAAW5E,UAAU,GACrB6E,EAAU7E,UAAU,KAAM,GAE9B/jB,KAAKsnB,GAAG8B,oBAAoBJ,EAAOnkB,GAAI8jB,EAAUC,OAIjD,IAAI5oB,KAAKsnB,GAAG4B,kBACR,IAAK,GAAIH,GAAI,EAAGA,EAAI/oB,KAAKsnB,GAAG4B,kBAAkBnnB,OAAQgnB,IAC9C/oB,KAAKsnB,GAAG4B,kBAAkBH,GAAGJ,WAAaA,GAC1C3oB,KAAKsnB,GAAG8B,oBAAoBJ,EAAOnkB,GAAI7E,KAAKsnB,GAAG4B,kBAAkBH,GAAGI,aAAcP,EAO1G,OAAO5oB,OAEXmkB,KAAM,SAAU9c,EAAWqhB,EAAgBC,EAAUC,GAOjD,QAASS,GAAMloB,GACXwnB,EAASxnB,GACTmoB,EAAInG,IAAI9b,EAAWqhB,EAAgBW,EAAOT,GAR9C,GAAIU,GAAMtpB,IACoB,mBAAnB0oB,KACPA,GAAiB,EACjBC,EAAW5E,UAAU,GACrB6E,EAAU7E,UAAU,IAMxBuF,EAAIjG,GAAGhc,EAAWqhB,EAAgBW,EAAOT,IAE7C5O,QAAS,SAAU3S,EAAWkiB,GAC1B,IAAK,GAAI1kB,GAAI,EAAGA,EAAI7E,KAAK+B,OAAQ8C,IAAK,CAClC,GAAI2kB,EACJ,KACIA,EAAM,GAAIC,aAAYpiB,GAAY9B,OAAQgkB,EAAWG,SAAS,EAAMC,YAAY,IAEpF,MAAOxoB,GACHqoB,EAAM9lB,SAASkmB,YAAY,SAC3BJ,EAAIK,UAAUxiB,GAAW,GAAM,GAC/BmiB,EAAIjkB,OAASgkB,EAEjBvpB,KAAK6E,GAAGilB,cAAcN,GAE1B,MAAOxpB,OAEX6Q,cAAe,SAAUjB,GAGrB,QAASma,GAAa5oB,GAElB,GAAIA,EAAEI,SAAWvB,KAEjB,IADA4P,EAASkZ,KAAK9oB,KAAMmB,GACf0D,EAAI,EAAGA,EAAImkB,EAAOjnB,OAAQ8C,IAC3BykB,EAAInG,IAAI6F,EAAOnkB,GAAIklB,GAP3B,GACIllB,GADAmkB,GAAU,sBAAuB,gBAAiB,iBAAkB,kBAAmB,mBACjFM,EAAMtpB,IAShB,IAAI4P,EACA,IAAK/K,EAAI,EAAGA,EAAImkB,EAAOjnB,OAAQ8C,IAC3BykB,EAAIjG,GAAG2F,EAAOnkB,GAAIklB,EAG1B,OAAO/pB,OAGX2E,MAAO,WACH,MAAI3E,MAAK,KAAOI,OACLA,OAAOiE,WAGVrE,KAAK+B,OAAS,EACP2P,WAAW1R,KAAKuO,IAAI,UAGpB,MAInB0E,WAAY,SAAU+W,GAClB,MAAIhqB,MAAK+B,OAAS,EACVioB,EACOhqB,KAAK,GAAGiiB,YAAcvQ,WAAW1R,KAAKuO,IAAI,iBAAmBmD,WAAW1R,KAAKuO,IAAI,gBAEjFvO,KAAK,GAAGiiB,YAEX,MAEhBrd,OAAQ,WACJ,MAAI5E,MAAK,KAAOI,OACLA,OAAOmE,YAGVvE,KAAK+B,OAAS,EACP2P,WAAW1R,KAAKuO,IAAI,WAGpB,MAInB2E,YAAa,SAAU8W,GACnB,MAAIhqB,MAAK+B,OAAS,EACVioB,EACOhqB,KAAK,GAAGkiB,aAAexQ,WAAW1R,KAAKuO,IAAI,eAAiBmD,WAAW1R,KAAKuO,IAAI,kBAEhFvO,KAAK,GAAGkiB,aAEX,MAEhBzd,OAAQ,WACJ,GAAIzE,KAAK+B,OAAS,EAAG,CACjB,GAAIV,GAAKrB,KAAK,GACViqB,EAAM5oB,EAAGuS,wBACTsW,EAAOxmB,SAASwmB,KAChBC,EAAa9oB,EAAG8oB,WAAcD,EAAKC,WAAc,EACjDtO,EAAaxa,EAAGwa,YAAcqO,EAAKrO,YAAc,EACjDuO,EAAahqB,OAAO+D,aAAe9C,EAAG+oB,UACtCC,EAAajqB,OAAO6D,aAAe5C,EAAGgpB,UAC1C,QACInmB,IAAK+lB,EAAI/lB,IAAOkmB,EAAaD,EAC7BnmB,KAAMimB,EAAIjmB,KAAOqmB,EAAaxO,GAIlC,MAAO,OAGftN,IAAK,SAAU+b,EAAO1C,GAClB,GAAI/iB,EACJ,IAAyB,IAArBkf,UAAUhiB,OAAc,CACxB,GAAqB,gBAAVuoB,GAGN,CACD,IAAKzlB,EAAI,EAAGA,EAAI7E,KAAK+B,OAAQ8C,IACzB,IAAK,GAAI0lB,KAAQD,GACbtqB,KAAK6E,GAAGuK,MAAMmb,GAAQD,EAAMC,EAGpC,OAAOvqB,MARP,GAAIA,KAAK,GAAI,MAAOI,QAAO4c,iBAAiBhd,KAAK,GAAI,MAAMud,iBAAiB+M,GAWpF,GAAyB,IAArBvG,UAAUhiB,QAAiC,gBAAVuoB,GAAoB,CACrD,IAAKzlB,EAAI,EAAGA,EAAI7E,KAAK+B,OAAQ8C,IACzB7E,KAAK6E,GAAGuK,MAAMkb,GAAS1C,CAE3B,OAAO5nB,MAEX,MAAOA,OAIXC,KAAM,SAAU2P,GACZ,IAAK,GAAI/K,GAAI,EAAGA,EAAI7E,KAAK+B,OAAQ8C,IAC7B+K,EAASkZ,KAAK9oB,KAAK6E,GAAIA,EAAG7E,KAAK6E,GAEnC,OAAO7E,OAEXoV,KAAM,SAAUA,GACZ,GAAoB,mBAATA,GACP,MAAOpV,MAAK,GAAKA,KAAK,GAAGgnB,UAAYllB,MAGrC,KAAK,GAAI+C,GAAI,EAAGA,EAAI7E,KAAK+B,OAAQ8C,IAC7B7E,KAAK6E,GAAGmiB,UAAY5R,CAExB,OAAOpV,OAGfwB,GAAI,SAAUJ,GACV,IAAKpB,KAAK,GAAI,OAAO,CACrB,IAAIwqB,GAAa3lB,CACjB,IAAwB,gBAAbzD,GAAuB,CAC9B,GAAIC,GAAKrB,KAAK,EACd,IAAIqB,IAAOqC,SAAU,MAAOtC,KAAasC,QACzC,IAAIrC,IAAOjB,OAAQ,MAAOgB,KAAahB,MAEvC,IAAIiB,EAAGopB,QAAS,MAAOppB,GAAGopB,QAAQrpB,EAC7B,IAAIC,EAAGqpB,sBAAuB,MAAOrpB,GAAGqpB,sBAAsBtpB,EAC9D,IAAIC,EAAGspB,mBAAoB,MAAOtpB,GAAGspB,mBAAmBvpB,EACxD,IAAIC,EAAGupB,kBAAmB,MAAOvpB,GAAGupB,kBAAkBxpB,EAGvD,KADAopB,EAAclpB,EAAEF,GACXyD,EAAI,EAAGA,EAAI2lB,EAAYzoB,OAAQ8C,IAChC,GAAI2lB,EAAY3lB,KAAO7E,KAAK,GAAI,OAAO,CAE3C,QAAO,EAGV,GAAIoB,IAAasC,SAAU,MAAO1D,MAAK,KAAO0D,QAC9C,IAAItC,IAAahB,OAAQ,MAAOJ,MAAK,KAAOI,MAE7C,IAAIgB,EAASM,UAAYN,YAAoBqM,GAAM,CAE/C,IADA+c,EAAcppB,EAASM,UAAYN,GAAYA,EAC1CyD,EAAI,EAAGA,EAAI2lB,EAAYzoB,OAAQ8C,IAChC,GAAI2lB,EAAY3lB,KAAO7E,KAAK,GAAI,OAAO,CAE3C,QAAO,EAEX,OAAO,GAIf4B,MAAO,WACH,GAAI5B,KAAK,GAAI,CAGT,IAFA,GAAI6qB,GAAQ7qB,KAAK,GACb6E,EAAI,EACmC,QAAnCgmB,EAAQA,EAAMC,kBACK,IAAnBD,EAAMnpB,UAAgBmD,GAE9B,OAAOA,GAEN,MAAO/C,SAEhBwQ,GAAI,SAAU1Q,GACV,GAAqB,mBAAVA,GAAuB,MAAO5B,KACzC,IACI+qB,GADAhpB,EAAS/B,KAAK+B,MAElB,OAAIH,GAAQG,EAAS,EACV,GAAI0L,OAEH,EAAR7L,GACAmpB,EAAchpB,EAASH,EACK,GAAI6L,GAAd,EAAdsd,MACkB/qB,KAAK+qB,MAExB,GAAItd,IAAMzN,KAAK4B,MAE1Bwc,OAAQ,SAAU4M,GACd,GAAInmB,GAAGyiB,CACP,KAAKziB,EAAI,EAAGA,EAAI7E,KAAK+B,OAAQ8C,IACzB,GAAwB,gBAAbmmB,GAAuB,CAC9B,GAAIC,GAAUvnB,SAAS8iB,cAAc,MAErC,KADAyE,EAAQjE,UAAYgE,EACbC,EAAQC,YACXlrB,KAAK6E,GAAGsmB,YAAYF,EAAQC,gBAG/B,IAAIF,YAAoBvd,GACzB,IAAK6Z,EAAI,EAAGA,EAAI0D,EAASjpB,OAAQulB,IAC7BtnB,KAAK6E,GAAGsmB,YAAYH,EAAS1D,QAIjCtnB,MAAK6E,GAAGsmB,YAAYH,EAG5B,OAAOhrB,OAEXse,QAAS,SAAU0M,GACf,GAAInmB,GAAGyiB,CACP,KAAKziB,EAAI,EAAGA,EAAI7E,KAAK+B,OAAQ8C,IACzB,GAAwB,gBAAbmmB,GAAuB,CAC9B,GAAIC,GAAUvnB,SAAS8iB,cAAc,MAErC,KADAyE,EAAQjE,UAAYgE,EACf1D,EAAI2D,EAAQhE,WAAWllB,OAAS,EAAGulB,GAAK,EAAGA,IAC5CtnB,KAAK6E,GAAGumB,aAAaH,EAAQhE,WAAWK,GAAItnB,KAAK6E,GAAGoiB,WAAW,QAIlE,IAAI+D,YAAoBvd,GACzB,IAAK6Z,EAAI,EAAGA,EAAI0D,EAASjpB,OAAQulB,IAC7BtnB,KAAK6E,GAAGumB,aAAaJ,EAAS1D,GAAItnB,KAAK6E,GAAGoiB,WAAW,QAIzDjnB,MAAK6E,GAAGumB,aAAaJ,EAAUhrB,KAAK6E,GAAGoiB,WAAW,GAG1D,OAAOjnB,OAEXorB,aAAc,SAAUhqB,GAEpB,IAAK,GADDiqB,GAAS/pB,EAAEF,GACNyD,EAAI,EAAGA,EAAI7E,KAAK+B,OAAQ8C,IAC7B,GAAsB,IAAlBwmB,EAAOtpB,OACPspB,EAAO,GAAGC,WAAWF,aAAaprB,KAAK6E,GAAIwmB,EAAO,QAEjD,IAAIA,EAAOtpB,OAAS,EACrB,IAAK,GAAIulB,GAAI,EAAGA,EAAI+D,EAAOtpB,OAAQulB,IAC/B+D,EAAO/D,GAAGgE,WAAWF,aAAaprB,KAAK6E,GAAGwZ,WAAU,GAAOgN,EAAO/D,KAKlFiE,YAAa,SAAUnqB,GAEnB,IAAK,GADDoqB,GAAQlqB,EAAEF,GACLyD,EAAI,EAAGA,EAAI7E,KAAK+B,OAAQ8C,IAC7B,GAAqB,IAAjB2mB,EAAMzpB,OACNypB,EAAM,GAAGF,WAAWF,aAAaprB,KAAK6E,GAAI2mB,EAAM,GAAGC,iBAElD,IAAID,EAAMzpB,OAAS,EACpB,IAAK,GAAIulB,GAAI,EAAGA,EAAIkE,EAAMzpB,OAAQulB,IAC9BkE,EAAMlE,GAAGgE,WAAWF,aAAaprB,KAAK6E,GAAGwZ,WAAU,GAAOmN,EAAMlE,GAAGmE,cAKnF9W,KAAM,SAAUvT,GACZ,MAE6F,IAAIqM,GAF7FzN,KAAK+B,OAAS,EACVX,EACIpB,KAAK,GAAG0rB,oBAAsBpqB,EAAEtB,KAAK,GAAG0rB,oBAAoBlqB,GAAGJ,IAA4BpB,KAAK,GAAG0rB,uBAInG1rB,KAAK,GAAG0rB,oBAAqC1rB,KAAK,GAAG0rB,4BAMrEC,QAAS,SAAUvqB,GACf,GAAIwqB,MACAvqB,EAAKrB,KAAK,EACd,KAAKqB,EAAI,MAAO,IAAIoM,MACpB,MAAOpM,EAAGqqB,oBAAoB,CAC1B,GAAI/W,GAAOtT,EAAGqqB,kBACVtqB,GACGE,EAAEqT,GAAMnT,GAAGJ,IAAWwqB,EAAQ5oB,KAAK2R,GAErCiX,EAAQ5oB,KAAK2R,GAClBtT,EAAKsT,EAET,MAAO,IAAIlH,GAAKme,IAEpBhX,KAAM,SAAUxT,GACZ,MAEqG,IAAIqM,GAFrGzN,KAAK+B,OAAS,EACVX,EACIpB,KAAK,GAAG6rB,wBAA0BvqB,EAAEtB,KAAK,GAAG6rB,wBAAwBrqB,GAAGJ,IAA4BpB,KAAK,GAAG6rB,2BAI3G7rB,KAAK,GAAG6rB,wBAAyC7rB,KAAK,GAAG6rB,gCAMzEC,QAAS,SAAU1qB,GACf,GAAI2qB,MACA1qB,EAAKrB,KAAK,EACd,KAAKqB,EAAI,MAAO,IAAIoM,MACpB,MAAOpM,EAAGwqB,wBAAwB,CAC9B,GAAIjX,GAAOvT,EAAGwqB,sBACVzqB,GACGE,EAAEsT,GAAMpT,GAAGJ,IAAW2qB,EAAQ/oB,KAAK4R,GAErCmX,EAAQ/oB,KAAK4R,GAClBvT,EAAKuT,EAET,MAAO,IAAInH,GAAKse,IAEpBC,OAAQ,SAAU5qB,GAEd,IAAK,GADDK,MACKoD,EAAI,EAAGA,EAAI7E,KAAK+B,OAAQ8C,IACzBzD,EACIE,EAAEtB,KAAK6E,GAAGymB,YAAY9pB,GAAGJ,IAAWK,EAAQuB,KAAKhD,KAAK6E,GAAGymB,YAG7D7pB,EAAQuB,KAAKhD,KAAK6E,GAAGymB,WAG7B,OAAOhqB,GAAEA,EAAE2qB,OAAOxqB,KAEtBA,QAAS,SAAUL,GAEf,IAAK,GADDK,MACKoD,EAAI,EAAGA,EAAI7E,KAAK+B,OAAQ8C,IAE7B,IADA,GAAImnB,GAAShsB,KAAK6E,GAAGymB,WACdU,GACC5qB,EACIE,EAAE0qB,GAAQxqB,GAAGJ,IAAWK,EAAQuB,KAAKgpB,GAGzCvqB,EAAQuB,KAAKgpB,GAEjBA,EAASA,EAAOV,UAGxB,OAAOhqB,GAAEA,EAAE2qB,OAAOxqB,KAEtB4O,KAAO,SAAUjP,GAEb,IAAK,GADD8qB,MACKrnB,EAAI,EAAGA,EAAI7E,KAAK+B,OAAQ8C,IAE7B,IAAK,GADDlD,GAAQ3B,KAAK6E,GAAGqiB,iBAAiB9lB,GAC5BkmB,EAAI,EAAGA,EAAI3lB,EAAMI,OAAQulB,IAC9B4E,EAAclpB,KAAKrB,EAAM2lB,GAGjC,OAAO,IAAI7Z,GAAKye,IAEpBhe,SAAU,SAAU9M,GAEhB,IAAK,GADD8M,MACKrJ,EAAI,EAAGA,EAAI7E,KAAK+B,OAAQ8C,IAG7B,IAAK,GAFDoiB,GAAajnB,KAAK6E,GAAGoiB,WAEhBK,EAAI,EAAGA,EAAIL,EAAWllB,OAAQulB,IAC9BlmB,EAI8B,IAA3B6lB,EAAWK,GAAG5lB,UAAkBJ,EAAE2lB,EAAWK,IAAI9lB,GAAGJ,IAAW8M,EAASlL,KAAKikB,EAAWK,IAH7D,IAA3BL,EAAWK,GAAG5lB,UAAgBwM,EAASlL,KAAKikB,EAAWK,GAOvE,OAAO,IAAI7Z,GAAKnM,EAAE2qB,OAAO/d,KAE7B+P,OAAQ,WACJ,IAAK,GAAIpZ,GAAI,EAAGA,EAAI7E,KAAK+B,OAAQ8C,IACzB7E,KAAK6E,GAAGymB,YAAYtrB,KAAK6E,GAAGymB,WAAWa,YAAYnsB,KAAK6E,GAEhE,OAAO7E,OAEXwnB,IAAK,WACD,GACI3iB,GAAGyiB,EADHgC,EAAMtpB,IAEV,KAAK6E,EAAI,EAAGA,EAAIkf,UAAUhiB,OAAQ8C,IAAK,CACnC,GAAIunB,GAAQ9qB,EAAEyiB,UAAUlf,GACxB,KAAKyiB,EAAI,EAAGA,EAAI8E,EAAMrqB,OAAQulB,IAC1BgC,EAAIA,EAAIvnB,QAAUqqB,EAAM9E,GACxBgC,EAAIvnB,SAGZ,MAAOunB,KAGfhoB,EAAE1B,GAAK6N,EAAK+X,UACZlkB,EAAE2qB,OAAS,SAAUrG,GAEjB,IAAK,GADDqG,MACKpnB,EAAI,EAAGA,EAAI+gB,EAAI7jB,OAAQ8C,IACG,KAA3BonB,EAAOhlB,QAAQ2e,EAAI/gB,KAAYonB,EAAOjpB,KAAK4iB,EAAI/gB,GAEvD,OAAOonB,IAGJ3qB,MAOP+qB,GAAoB,SAAU,QAAS,QAWlCxnB,EAAI,EAAGA,EAAIwnB,EAAiBtqB,OAAQ8C,IACrCzE,OAAOisB,EAAiBxnB,KACxBnF,EAAiBU,OAAOisB,EAAiBxnB,IAIjD,IAAIynB,EAEAA,GADgB,mBAAT7e,GACErN,OAAOqN,MAAQrN,OAAOsN,OAAStN,OAAOuN,OAGtCF,EAET6e,IACM,iBAAmBA,GAAO1sB,KAC5B0sB,EAAO1sB,GAAGiR,cAAgB,SAAUjB,GAGhC,QAASma,GAAa5oB,GAElB,GAAIA,EAAEI,SAAWvB,KAEjB,IADA4P,EAASkZ,KAAK9oB,KAAMmB,GACf0D,EAAI,EAAGA,EAAImkB,EAAOjnB,OAAQ8C,IAC3BykB,EAAInG,IAAI6F,EAAOnkB,GAAIklB,GAP3B,GACIllB,GADAmkB,GAAU,sBAAuB,gBAAiB,iBAAkB,kBAAmB,mBACjFM,EAAMtpB,IAShB,IAAI4P,EACA,IAAK/K,EAAI,EAAGA,EAAImkB,EAAOjnB,OAAQ8C,IAC3BykB,EAAIjG,GAAG2F,EAAOnkB,GAAIklB,EAG1B,OAAO/pB,QAGT,aAAessB,GAAO1sB,KACxB0sB,EAAO1sB,GAAGuH,UAAY,SAAUA,GAC5B,IAAK,GAAItC,GAAI,EAAGA,EAAI7E,KAAK+B,OAAQ8C,IAAK,CAClC,GAAIsjB,GAAUnoB,KAAK6E,GAAGuK,KACtB+Y,GAAQjL,gBAAkBiL,EAAQ9K,YAAc8K,EAAQ7K,YAAc6K,EAAQhL,aAAegL,EAAQ/K,WAAa+K,EAAQhhB,UAAYA,EAE1I,MAAOnH,QAGT,cAAgBssB,GAAO1sB,KACzB0sB,EAAO1sB,GAAGsc,WAAa,SAAUF,GACL,gBAAbA,KACPA,GAAsB,KAE1B,KAAK,GAAInX,GAAI,EAAGA,EAAI7E,KAAK+B,OAAQ8C,IAAK,CAClC,GAAIsjB,GAAUnoB,KAAK6E,GAAGuK,KACtB+Y,GAAQC,yBAA2BD,EAAQE,qBAAuBF,EAAQG,qBAAuBH,EAAQI,sBAAwBJ,EAAQK,oBAAsBL,EAAQM,mBAAqBzM,EAEhM,MAAOhc,YAWA,mBAAb,QAENusB,OAAOC,QAAUrsB,OAEM,kBAAXssB,SAAyBA,OAAOC,KAC5CD,UAAW,WACP,YACA,OAAOtsB", "file": "swiper.min.js", "sourcesContent": ["/**\n * Swiper 3.0.6\n * Most modern mobile touch slider and framework with hardware accelerated transitions\n * \n * http://www.idangero.us/swiper/\n * \n * Copyright 2015, <PERSON>\n * The iDangero.us\n * http://www.idangero.us/\n * \n * Licensed under MIT\n * \n * Released on: March 27, 2015\n */\n(function () {\n    'use strict';\n    /*===========================\n    Swiper\n    ===========================*/\n    window.Swiper = function (container, params) {\n        if (!(this instanceof Swiper)) return new Swiper(container, params);\n\n        var defaults = {\n            direction: 'horizontal',\n            touchEventsTarget: 'container',\n            initialSlide: 0,\n            speed: 300,\n            // autoplay\n            autoplay: false,\n            autoplayDisableOnInteraction: true,\n            // Free mode\n            freeMode: false,\n            freeModeMomentum: true,\n            freeModeMomentumRatio: 1,\n            freeModeMomentumBounce: true,\n            freeModeMomentumBounceRatio: 1,\n            // Set wrapper width\n            setWrapperSize: false,\n            // Virtual Translate\n            virtualTranslate: false,\n            // Effects\n            effect: 'slide', // 'slide' or 'fade' or 'cube' or 'coverflow'\n            coverflow: {\n                rotate: 50,\n                stretch: 0,\n                depth: 100,\n                modifier: 1,\n                slideShadows : true\n            },\n            cube: {\n                slideShadows: true,\n                shadow: true,\n                shadowOffset: 20,\n                shadowScale: 0.94\n            },\n            fade: {\n                crossFade: false\n            },\n            // Parallax\n            parallax: false,\n            // Scrollbar\n            scrollbar: null,\n            scrollbarHide: true,\n            // Keyboard Mousewheel\n            keyboardControl: false,\n            mousewheelControl: false,\n            mousewheelForceToAxis: false,\n            // Hash Navigation\n            hashnav: false,\n            // Slides grid\n            spaceBetween: 0,\n            slidesPerView: 1,\n            slidesPerColumn: 1,\n            slidesPerColumnFill: 'column',\n            slidesPerGroup: 1,\n            centeredSlides: false,\n            // Touches\n            touchRatio: 1,\n            touchAngle: 45,\n            simulateTouch: true,\n            shortSwipes: true,\n            longSwipes: true,\n            longSwipesRatio: 0.5,\n            longSwipesMs: 300,\n            followFinger: true,\n            onlyExternal: false,\n            threshold: 0,\n            touchMoveStopPropagation: true,\n            // Pagination\n            pagination: null,\n            paginationClickable: false,\n            paginationHide: false,\n            paginationBulletRender: null,\n            // Resistance\n            resistance: true,\n            resistanceRatio: 0.85,\n            // Next/prev buttons\n            nextButton: null,\n            prevButton: null,\n            // Progress\n            watchSlidesProgress: false,\n            watchSlidesVisibility: false,\n            // Cursor\n            grabCursor: false,\n            // Clicks\n            preventClicks: true,\n            preventClicksPropagation: true,\n            slideToClickedSlide: false,\n            // Lazy Loading\n            lazyLoading: false,\n            lazyLoadingInPrevNext: false,\n            lazyLoadingOnTransitionStart: false,\n            // Images\n            preloadImages: true,\n            updateOnImagesReady: true,\n            // loop\n            loop: false,\n            loopAdditionalSlides: 0,\n            loopedSlides: null,\n            // Control\n            control: undefined,\n            controlInverse: false,\n            // Swiping/no swiping\n            allowSwipeToPrev: true,\n            allowSwipeToNext: true,\n            swipeHandler: null, //'.swipe-handler',\n            noSwiping: true,\n            noSwipingClass: 'swiper-no-swiping',\n            // NS\n            slideClass: 'swiper-slide',\n            slideActiveClass: 'swiper-slide-active',\n            slideVisibleClass: 'swiper-slide-visible',\n            slideDuplicateClass: 'swiper-slide-duplicate',\n            slideNextClass: 'swiper-slide-next',\n            slidePrevClass: 'swiper-slide-prev',\n            wrapperClass: 'swiper-wrapper',\n            bulletClass: 'swiper-pagination-bullet',\n            bulletActiveClass: 'swiper-pagination-bullet-active',\n            buttonDisabledClass: 'swiper-button-disabled',\n            paginationHiddenClass: 'swiper-pagination-hidden',\n            // Observer\n            observer: false,\n            observeParents: false,\n            // Accessibility\n            a11y: false,\n            prevSlideMessage: 'Previous slide',\n            nextSlideMessage: 'Next slide',\n            firstSlideMessage: 'This is the first slide',\n            lastSlideMessage: 'This is the last slide',\n            // Callbacks\n            runCallbacksOnInit: true,\n            /*\n            Callbacks:\n            onInit: function (swiper)\n            onDestroy: function (swiper)\n            onClick: function (swiper, e)\n            onTap: function (swiper, e)\n            onDoubleTap: function (swiper, e)\n            onSliderMove: function (swiper, e)\n            onSlideChangeStart: function (swiper)\n            onSlideChangeEnd: function (swiper)\n            onTransitionStart: function (swiper)\n            onTransitionEnd: function (swiper)\n            onImagesReady: function (swiper)\n            onProgress: function (swiper, progress)\n            onTouchStart: function (swiper, e)\n            onTouchMove: function (swiper, e)\n            onTouchMoveOpposite: function (swiper, e)\n            onTouchEnd: function (swiper, e)\n            onReachBeginning: function (swiper)\n            onReachEnd: function (swiper)\n            onSetTransition: function (swiper, duration)\n            onSetTranslate: function (swiper, translate)\n            onAutoplayStart: function (swiper)\n            onAutoplayStop: function (swiper),\n            onLazyImageLoad: function (swiper, slide, image)\n            onLazyImageReady: function (swiper, slide, image)\n            */\n        \n        };\n        var initalVirtualTranslate = params && params.virtualTranslate;\n        \n        params = params || {};\n        for (var def in defaults) {\n            if (typeof params[def] === 'undefined') {\n                params[def] = defaults[def];\n            }\n            else if (typeof params[def] === 'object') {\n                for (var deepDef in defaults[def]) {\n                    if (typeof params[def][deepDef] === 'undefined') {\n                        params[def][deepDef] = defaults[def][deepDef];\n                    }\n                }\n            }\n        }\n        \n        // Swiper\n        var s = this;\n        \n        // Params\n        s.params = params;\n        \n        // Classname\n        s.classNames = [];\n        /*=========================\n          Dom Library and plugins\n          ===========================*/\n        var $;\n        if (typeof Dom7 === 'undefined') {\n            $ = window.Dom7 || window.Zepto || window.jQuery;\n        }\n        else {\n            $ = Dom7;\n        }\n        if (!$) return;\n        \n        // Export it to Swiper instance\n        s.$ = $;\n        /*=========================\n          Preparation - Define Container, Wrapper and Pagination\n          ===========================*/\n        s.container = $(container);\n        if (s.container.length === 0) return;\n        if (s.container.length > 1) {\n            s.container.each(function () {\n                new Swiper(this, params);\n            });\n            return;\n        }\n        \n        // Save instance in container HTML Element and in data\n        s.container[0].swiper = s;\n        s.container.data('swiper', s);\n        \n        s.classNames.push('swiper-container-' + s.params.direction);\n        \n        if (s.params.freeMode) {\n            s.classNames.push('swiper-container-free-mode');\n        }\n        if (!s.support.flexbox) {\n            s.classNames.push('swiper-container-no-flexbox');\n            s.params.slidesPerColumn = 1;\n        }\n        // Enable slides progress when required\n        if (s.params.parallax || s.params.watchSlidesVisibility) {\n            s.params.watchSlidesProgress = true;\n        }\n        // Coverflow / 3D\n        if (['cube', 'coverflow'].indexOf(s.params.effect) >= 0) {\n            if (s.support.transforms3d) {\n                s.params.watchSlidesProgress = true;\n                s.classNames.push('swiper-container-3d');\n            }\n            else {\n                s.params.effect = 'slide';\n            }\n        }\n        if (s.params.effect !== 'slide') {\n            s.classNames.push('swiper-container-' + s.params.effect);\n        }\n        if (s.params.effect === 'cube') {\n            s.params.resistanceRatio = 0;\n            s.params.slidesPerView = 1;\n            s.params.slidesPerColumn = 1;\n            s.params.slidesPerGroup = 1;\n            s.params.centeredSlides = false;\n            s.params.spaceBetween = 0;\n            s.params.virtualTranslate = true;\n            s.params.setWrapperSize = false;\n        }\n        if (s.params.effect === 'fade') {\n            s.params.slidesPerView = 1;\n            s.params.slidesPerColumn = 1;\n            s.params.slidesPerGroup = 1;\n            s.params.watchSlidesProgress = true;\n            s.params.spaceBetween = 0;\n            if (typeof initalVirtualTranslate === 'undefined') {\n                s.params.virtualTranslate = true;\n            }\n        }\n        \n        // Grab Cursor\n        if (s.params.grabCursor && s.support.touch) {\n            s.params.grabCursor = false;\n        }\n        \n        // Wrapper\n        s.wrapper = s.container.children('.' + s.params.wrapperClass);\n        \n        // Pagination\n        if (s.params.pagination) {\n            s.paginationContainer = $(s.params.pagination);\n            if (s.params.paginationClickable) {\n                s.paginationContainer.addClass('swiper-pagination-clickable');\n            }\n        }\n        \n        // Is Horizontal\n        function isH() {\n            return s.params.direction === 'horizontal';\n        }\n        \n        // RTL\n        s.rtl = isH() && (s.container[0].dir.toLowerCase() === 'rtl' || s.container.css('direction') === 'rtl');\n        if (s.rtl) {\n            s.classNames.push('swiper-container-rtl');\n        }\n        \n        // Wrong RTL support\n        if (s.rtl) {\n            s.wrongRTL = s.wrapper.css('display') === '-webkit-box';\n        }\n        \n        // Columns\n        if (s.params.slidesPerColumn > 1) {\n            s.classNames.push('swiper-container-multirow');\n        }\n        \n        // Check for Android\n        if (s.device.android) {\n            s.classNames.push('swiper-container-android');\n        }\n        \n        // Add classes\n        s.container.addClass(s.classNames.join(' '));\n        \n        // Translate\n        s.translate = 0;\n        \n        // Progress\n        s.progress = 0;\n        \n        // Velocity\n        s.velocity = 0;\n        \n        // Locks, unlocks\n        s.lockSwipeToNext = function () {\n            s.params.allowSwipeToNext = false;\n        };\n        s.lockSwipeToPrev = function () {\n            s.params.allowSwipeToPrev = false;\n        };\n        s.lockSwipes = function () {\n            s.params.allowSwipeToNext = s.params.allowSwipeToPrev = false;\n        };\n        s.unlockSwipeToNext = function () {\n            s.params.allowSwipeToNext = true;\n        };\n        s.unlockSwipeToPrev = function () {\n            s.params.allowSwipeToPrev = true;\n        };\n        s.unlockSwipes = function () {\n            s.params.allowSwipeToNext = s.params.allowSwipeToPrev = true;\n        };\n        \n        \n        /*=========================\n          Set grab cursor\n          ===========================*/\n        if (s.params.grabCursor) {\n            s.container[0].style.cursor = 'move';\n            s.container[0].style.cursor = '-webkit-grab';\n            s.container[0].style.cursor = '-moz-grab';\n            s.container[0].style.cursor = 'grab';\n        }\n        /*=========================\n          Update on Images Ready\n          ===========================*/\n        s.imagesToLoad = [];\n        s.imagesLoaded = 0;\n        \n        s.loadImage = function (imgElement, src, checkForComplete, callback) {\n            var image;\n            function onReady () {\n                if (callback) callback();\n            }\n            if (!imgElement.complete || !checkForComplete) {\n                if (src) {\n                    image = new Image();\n                    image.onload = onReady;\n                    image.onerror = onReady;\n                    image.src = src;\n                } else {\n                    onReady();\n                }\n        \n            } else {//image already loaded...\n                onReady();\n            }\n        };\n        s.preloadImages = function () {\n            s.imagesToLoad = s.container.find('img');\n            function _onReady() {\n                if (typeof s === 'undefined' || s === null) return;\n                if (s.imagesLoaded !== undefined) s.imagesLoaded++;\n                if (s.imagesLoaded === s.imagesToLoad.length) {\n                    if (s.params.updateOnImagesReady) s.update();\n                    s.emit('onImagesReady', s);\n                }\n            }\n            for (var i = 0; i < s.imagesToLoad.length; i++) {\n                s.loadImage(s.imagesToLoad[i], (s.imagesToLoad[i].currentSrc || s.imagesToLoad[i].getAttribute('src')), true, _onReady);\n            }\n        };\n        \n        /*=========================\n          Autoplay\n          ===========================*/\n        s.autoplayTimeoutId = undefined;\n        s.autoplaying = false;\n        s.autoplayPaused = false;\n        function autoplay() {\n            s.autoplayTimeoutId = setTimeout(function () {\n                if (s.params.loop) {\n                    s.fixLoop();\n                    s._slideNext();\n                }\n                else {\n                    if (!s.isEnd) {\n                        s._slideNext();\n                    }\n                    else {\n                        if (!params.autoplayStopOnLast) {\n                            s._slideTo(0);\n                        }\n                        else {\n                            s.stopAutoplay();\n                        }\n                    }\n                }\n            }, s.params.autoplay);\n        }\n        s.startAutoplay = function () {\n            if (typeof s.autoplayTimeoutId !== 'undefined') return false;\n            if (!s.params.autoplay) return false;\n            if (s.autoplaying) return false;\n            s.autoplaying = true;\n            s.emit('onAutoplayStart', s);\n            autoplay();\n        };\n        s.stopAutoplay = function (internal) {\n            if (!s.autoplayTimeoutId) return;\n            if (s.autoplayTimeoutId) clearTimeout(s.autoplayTimeoutId);\n            s.autoplaying = false;\n            s.autoplayTimeoutId = undefined;\n            s.emit('onAutoplayStop', s);\n        };\n        s.pauseAutoplay = function (speed) {\n            if (s.autoplayPaused) return;\n            if (s.autoplayTimeoutId) clearTimeout(s.autoplayTimeoutId);\n            s.autoplayPaused = true;\n            if (speed === 0) {\n                s.autoplayPaused = false;\n                autoplay();\n            }\n            else {\n                s.wrapper.transitionEnd(function () {\n                    s.autoplayPaused = false;\n                    if (!s.autoplaying) {\n                        s.stopAutoplay();\n                    }\n                    else {\n                        autoplay();\n                    }\n                });\n            }\n        };\n        /*=========================\n          Min/Max Translate\n          ===========================*/\n        s.minTranslate = function () {\n            return (-s.snapGrid[0]);\n        };\n        s.maxTranslate = function () {\n            return (-s.snapGrid[s.snapGrid.length - 1]);\n        };\n        /*=========================\n          Slider/slides sizes\n          ===========================*/\n        s.updateContainerSize = function () {\n            s.width = s.container[0].clientWidth;\n            s.height = s.container[0].clientHeight;\n            s.size = isH() ? s.width : s.height;\n        };\n        \n        s.updateSlidesSize = function () {\n            s.slides = s.wrapper.children('.' + s.params.slideClass);\n            s.snapGrid = [];\n            s.slidesGrid = [];\n            s.slidesSizesGrid = [];\n        \n            var spaceBetween = s.params.spaceBetween,\n                slidePosition = 0,\n                i,\n                prevSlideSize = 0,\n                index = 0;\n            if (typeof spaceBetween === 'string' && spaceBetween.indexOf('%') >= 0) {\n                spaceBetween = parseFloat(spaceBetween.replace('%', '')) / 100 * s.size;\n            }\n        \n            s.virtualSize = -spaceBetween;\n            // reset margins\n            if (s.rtl) s.slides.css({marginLeft: '', marginTop: ''});\n            else s.slides.css({marginRight: '', marginBottom: ''});\n        \n            var slidesNumberEvenToRows;\n            if (s.params.slidesPerColumn > 1) {\n                if (Math.floor(s.slides.length / s.params.slidesPerColumn) === s.slides.length / s.params.slidesPerColumn) {\n                    slidesNumberEvenToRows = s.slides.length;\n                }\n                else {\n                    slidesNumberEvenToRows = Math.ceil(s.slides.length / s.params.slidesPerColumn) * s.params.slidesPerColumn;\n                }\n            }\n        \n            // Calc slides\n            var slideSize;\n            for (i = 0; i < s.slides.length; i++) {\n                slideSize = 0;\n                var slide = s.slides.eq(i);\n                if (s.params.slidesPerColumn > 1) {\n                    // Set slides order\n                    var newSlideOrderIndex;\n                    var column, row;\n                    var slidesPerColumn = s.params.slidesPerColumn;\n                    var slidesPerRow;\n                    if (s.params.slidesPerColumnFill === 'column') {\n                        column = Math.floor(i / slidesPerColumn);\n                        row = i - column * slidesPerColumn;\n                        newSlideOrderIndex = column + row * slidesNumberEvenToRows / slidesPerColumn;\n                        slide\n                            .css({\n                                '-webkit-box-ordinal-group': newSlideOrderIndex,\n                                '-moz-box-ordinal-group': newSlideOrderIndex,\n                                '-ms-flex-order': newSlideOrderIndex,\n                                '-webkit-order': newSlideOrderIndex,\n                                'order': newSlideOrderIndex\n                            });\n                    }\n                    else {\n                        slidesPerRow = slidesNumberEvenToRows / slidesPerColumn;\n                        row = Math.floor(i / slidesPerRow);\n                        column = i - row * slidesPerRow;\n        \n                    }\n                    slide\n                        .css({\n                            'margin-top': (row !== 0 && s.params.spaceBetween) && (s.params.spaceBetween + 'px')\n                        })\n                        .attr('data-swiper-column', column)\n                        .attr('data-swiper-row', row);\n        \n                }\n                if (slide.css('display') === 'none') continue;\n                if (s.params.slidesPerView === 'auto') {\n                    slideSize = isH() ? slide.outerWidth(true) : slide.outerHeight(true);\n                }\n                else {\n                    slideSize = (s.size - (s.params.slidesPerView - 1) * spaceBetween) / s.params.slidesPerView;\n                    if (isH()) {\n                        s.slides[i].style.width = slideSize + 'px';\n                    }\n                    else {\n                        s.slides[i].style.height = slideSize + 'px';\n                    }\n                }\n                s.slides[i].swiperSlideSize = slideSize;\n                s.slidesSizesGrid.push(slideSize);\n        \n        \n                if (s.params.centeredSlides) {\n                    slidePosition = slidePosition + slideSize / 2 + prevSlideSize / 2 + spaceBetween;\n                    if (i === 0) slidePosition = slidePosition - s.size / 2 - spaceBetween;\n                    if (Math.abs(slidePosition) < 1 / 1000) slidePosition = 0;\n                    if ((index) % s.params.slidesPerGroup === 0) s.snapGrid.push(slidePosition);\n                    s.slidesGrid.push(slidePosition);\n                }\n                else {\n                    if ((index) % s.params.slidesPerGroup === 0) s.snapGrid.push(slidePosition);\n                    s.slidesGrid.push(slidePosition);\n                    slidePosition = slidePosition + slideSize + spaceBetween;\n                }\n        \n                s.virtualSize += slideSize + spaceBetween;\n        \n                prevSlideSize = slideSize;\n        \n                index ++;\n            }\n            s.virtualSize = Math.max(s.virtualSize, s.size);\n        \n            var newSlidesGrid;\n        \n            if (\n                s.rtl && s.wrongRTL && (s.params.effect === 'slide' || s.params.effect === 'coverflow')) {\n                s.wrapper.css({width: s.virtualSize + s.params.spaceBetween + 'px'});\n            }\n            if (!s.support.flexbox || s.params.setWrapperSize) {\n                if (isH()) s.wrapper.css({width: s.virtualSize + s.params.spaceBetween + 'px'});\n                else s.wrapper.css({height: s.virtualSize + s.params.spaceBetween + 'px'});\n            }\n        \n            if (s.params.slidesPerColumn > 1) {\n                s.virtualSize = (slideSize + s.params.spaceBetween) * slidesNumberEvenToRows;\n                s.virtualSize = Math.ceil(s.virtualSize / s.params.slidesPerColumn) - s.params.spaceBetween;\n                s.wrapper.css({width: s.virtualSize + s.params.spaceBetween + 'px'});\n                if (s.params.centeredSlides) {\n                    newSlidesGrid = [];\n                    for (i = 0; i < s.snapGrid.length; i++) {\n                        if (s.snapGrid[i] < s.virtualSize + s.snapGrid[0]) newSlidesGrid.push(s.snapGrid[i]);\n                    }\n                    s.snapGrid = newSlidesGrid;\n                }\n            }\n        \n            // Remove last grid elements depending on width\n            if (!s.params.centeredSlides) {\n                newSlidesGrid = [];\n                for (i = 0; i < s.snapGrid.length; i++) {\n                    if (s.snapGrid[i] <= s.virtualSize - s.size) {\n                        newSlidesGrid.push(s.snapGrid[i]);\n                    }\n                }\n                s.snapGrid = newSlidesGrid;\n                if (Math.floor(s.virtualSize - s.size) > Math.floor(s.snapGrid[s.snapGrid.length - 1])) {\n                    s.snapGrid.push(s.virtualSize - s.size);\n                }\n            }\n            if (s.snapGrid.length === 0) s.snapGrid = [0];\n        \n            if (s.params.spaceBetween !== 0) {\n                if (isH()) {\n                    if (s.rtl) s.slides.css({marginLeft: spaceBetween + 'px'});\n                    else s.slides.css({marginRight: spaceBetween + 'px'});\n                }\n                else s.slides.css({marginBottom: spaceBetween + 'px'});\n            }\n            if (s.params.watchSlidesProgress) {\n                s.updateSlidesOffset();\n            }\n        };\n        s.updateSlidesOffset = function () {\n            for (var i = 0; i < s.slides.length; i++) {\n                s.slides[i].swiperSlideOffset = isH() ? s.slides[i].offsetLeft : s.slides[i].offsetTop;\n            }\n        };\n        \n        /*=========================\n          Slider/slides progress\n          ===========================*/\n        s.updateSlidesProgress = function (translate) {\n            if (typeof translate === 'undefined') {\n                translate = s.translate || 0;\n            }\n            if (s.slides.length === 0) return;\n            if (typeof s.slides[0].swiperSlideOffset === 'undefined') s.updateSlidesOffset();\n        \n            var offsetCenter = s.params.centeredSlides ? -translate + s.size / 2 : -translate;\n            if (s.rtl) offsetCenter = s.params.centeredSlides ? translate - s.size / 2 : translate;\n        \n            // Visible Slides\n            var containerBox = s.container[0].getBoundingClientRect();\n            var sideBefore = isH() ? 'left' : 'top';\n            var sideAfter = isH() ? 'right' : 'bottom';\n            s.slides.removeClass(s.params.slideVisibleClass);\n            for (var i = 0; i < s.slides.length; i++) {\n                var slide = s.slides[i];\n                var slideCenterOffset = (s.params.centeredSlides === true) ? slide.swiperSlideSize / 2 : 0;\n                var slideProgress = (offsetCenter - slide.swiperSlideOffset - slideCenterOffset) / (slide.swiperSlideSize + s.params.spaceBetween);\n                if (s.params.watchSlidesVisibility) {\n                    var slideBefore = -(offsetCenter - slide.swiperSlideOffset - slideCenterOffset);\n                    var slideAfter = slideBefore + s.slidesSizesGrid[i];\n                    var isVisible =\n                        (slideBefore >= 0 && slideBefore < s.size) ||\n                        (slideAfter > 0 && slideAfter <= s.size) ||\n                        (slideBefore <= 0 && slideAfter >= s.size);\n                    if (isVisible) {\n                        s.slides.eq(i).addClass(s.params.slideVisibleClass);\n                    }\n                }\n                slide.progress = s.rtl ? -slideProgress : slideProgress;\n            }\n        };\n        s.updateProgress = function (translate) {\n            if (typeof translate === 'undefined') {\n                translate = s.translate || 0;\n            }\n            var translatesDiff = s.maxTranslate() - s.minTranslate();\n            if (translatesDiff === 0) {\n                s.progress = 0;\n                s.isBeginning = s.isEnd = true;\n            }\n            else {\n                s.progress = (translate - s.minTranslate()) / (translatesDiff);\n                s.isBeginning = s.progress <= 0;\n                s.isEnd = s.progress >= 1;\n            }\n            if (s.isBeginning) s.emit('onReachBeginning', s);\n            if (s.isEnd) s.emit('onReachEnd', s);\n        \n            if (s.params.watchSlidesProgress) s.updateSlidesProgress(translate);\n            s.emit('onProgress', s, s.progress);\n        };\n        s.updateActiveIndex = function () {\n            var translate = s.rtl ? s.translate : -s.translate;\n            var newActiveIndex, i, snapIndex;\n            for (i = 0; i < s.slidesGrid.length; i ++) {\n                if (typeof s.slidesGrid[i + 1] !== 'undefined') {\n                    if (translate >= s.slidesGrid[i] && translate < s.slidesGrid[i + 1] - (s.slidesGrid[i + 1] - s.slidesGrid[i]) / 2) {\n                        newActiveIndex = i;\n                    }\n                    else if (translate >= s.slidesGrid[i] && translate < s.slidesGrid[i + 1]) {\n                        newActiveIndex = i + 1;\n                    }\n                }\n                else {\n                    if (translate >= s.slidesGrid[i]) {\n                        newActiveIndex = i;\n                    }\n                }\n            }\n            // Normalize slideIndex\n            if (newActiveIndex < 0 || typeof newActiveIndex === 'undefined') newActiveIndex = 0;\n            // for (i = 0; i < s.slidesGrid.length; i++) {\n                // if (- translate >= s.slidesGrid[i]) {\n                    // newActiveIndex = i;\n                // }\n            // }\n            snapIndex = Math.floor(newActiveIndex / s.params.slidesPerGroup);\n            if (snapIndex >= s.snapGrid.length) snapIndex = s.snapGrid.length - 1;\n        \n            if (newActiveIndex === s.activeIndex) {\n                return;\n            }\n            s.snapIndex = snapIndex;\n            s.previousIndex = s.activeIndex;\n            s.activeIndex = newActiveIndex;\n            s.updateClasses();\n        };\n        \n        /*=========================\n          Classes\n          ===========================*/\n        s.updateClasses = function () {\n            s.slides.removeClass(s.params.slideActiveClass + ' ' + s.params.slideNextClass + ' ' + s.params.slidePrevClass);\n            var activeSlide = s.slides.eq(s.activeIndex);\n            // Active classes\n            activeSlide.addClass(s.params.slideActiveClass);\n            activeSlide.next('.' + s.params.slideClass).addClass(s.params.slideNextClass);\n            activeSlide.prev('.' + s.params.slideClass).addClass(s.params.slidePrevClass);\n        \n            // Pagination\n            if (s.bullets && s.bullets.length > 0) {\n                s.bullets.removeClass(s.params.bulletActiveClass);\n                var bulletIndex;\n                if (s.params.loop) {\n                    bulletIndex = Math.ceil(s.activeIndex - s.loopedSlides)/s.params.slidesPerGroup;\n                    if (bulletIndex > s.slides.length - 1 - s.loopedSlides * 2) {\n                        bulletIndex = bulletIndex - (s.slides.length - s.loopedSlides * 2);\n                    }\n                    if (bulletIndex > s.bullets.length - 1) bulletIndex = bulletIndex - s.bullets.length;\n                }\n                else {\n                    if (typeof s.snapIndex !== 'undefined') {\n                        bulletIndex = s.snapIndex;\n                    }\n                    else {\n                        bulletIndex = s.activeIndex || 0;\n                    }\n                }\n                if (s.paginationContainer.length > 1) {\n                    s.bullets.each(function () {\n                        if ($(this).index() === bulletIndex) $(this).addClass(s.params.bulletActiveClass);\n                    });\n                }\n                else {\n                    s.bullets.eq(bulletIndex).addClass(s.params.bulletActiveClass);\n                }\n            }\n        \n            // Next/active buttons\n            if (!s.params.loop) {\n                if (s.params.prevButton) {\n                    if (s.isBeginning) {\n                        $(s.params.prevButton).addClass(s.params.buttonDisabledClass);\n                        if (s.params.a11y && s.a11y) s.a11y.disable($(s.params.prevButton));\n                    }\n                    else {\n                        $(s.params.prevButton).removeClass(s.params.buttonDisabledClass);\n                        if (s.params.a11y && s.a11y) s.a11y.enable($(s.params.prevButton));\n                    }\n                }\n                if (s.params.nextButton) {\n                    if (s.isEnd) {\n                        $(s.params.nextButton).addClass(s.params.buttonDisabledClass);\n                        if (s.params.a11y && s.a11y) s.a11y.disable($(s.params.nextButton));\n                    }\n                    else {\n                        $(s.params.nextButton).removeClass(s.params.buttonDisabledClass);\n                        if (s.params.a11y && s.a11y) s.a11y.enable($(s.params.nextButton));\n                    }\n                }\n            }\n        };\n        \n        /*=========================\n          Pagination\n          ===========================*/\n        s.updatePagination = function () {\n            if (!s.params.pagination) return;\n            if (s.paginationContainer && s.paginationContainer.length > 0) {\n                var bulletsHTML = '';\n                var numberOfBullets = s.params.loop ? Math.ceil((s.slides.length - s.loopedSlides * 2) / s.params.slidesPerGroup) : s.snapGrid.length;\n                for (var i = 0; i < numberOfBullets; i++) {\n                    if (s.params.paginationBulletRender) {\n                        bulletsHTML += s.params.paginationBulletRender(i, s.params.bulletClass);\n                    }\n                    else {\n                        bulletsHTML += '<span class=\"' + s.params.bulletClass + '\"></span>';\n                    }\n                }\n                s.paginationContainer.html(bulletsHTML);\n                s.bullets = s.paginationContainer.find('.' + s.params.bulletClass);\n            }\n        };\n        /*=========================\n          Common update method\n          ===========================*/\n        s.update = function (updateTranslate) {\n            s.updateContainerSize();\n            s.updateSlidesSize();\n            s.updateProgress();\n            s.updatePagination();\n            s.updateClasses();\n            if (s.params.scrollbar && s.scrollbar) {\n                s.scrollbar.set();\n            }\n            function forceSetTranslate() {\n                newTranslate = Math.min(Math.max(s.translate, s.maxTranslate()), s.minTranslate());\n                s.setWrapperTranslate(newTranslate);\n                s.updateActiveIndex();\n                s.updateClasses();\n            }\n            if (updateTranslate) {\n                var translated, newTranslate;\n                if (s.params.freeMode) {\n                    forceSetTranslate();\n                }\n                else {\n                    if (s.params.slidesPerView === 'auto' && s.isEnd && !s.params.centeredSlides) {\n                        translated = s.slideTo(s.slides.length - 1, 0, false, true);\n                    }\n                    else {\n                        translated = s.slideTo(s.activeIndex, 0, false, true);\n                    }\n                    if (!translated) {\n                        forceSetTranslate();\n                    }\n                }\n        \n            }\n        };\n        \n        /*=========================\n          Resize Handler\n          ===========================*/\n        s.onResize = function () {\n            s.updateContainerSize();\n            s.updateSlidesSize();\n            s.updateProgress();\n            if (s.params.slidesPerView === 'auto' || s.params.freeMode) s.updatePagination();\n            if (s.params.scrollbar && s.scrollbar) {\n                s.scrollbar.set();\n            }\n            if (s.params.freeMode) {\n                var newTranslate = Math.min(Math.max(s.translate, s.maxTranslate()), s.minTranslate());\n                s.setWrapperTranslate(newTranslate);\n                s.updateActiveIndex();\n                s.updateClasses();\n            }\n            else {\n                s.updateClasses();\n                if (s.params.slidesPerView === 'auto' && s.isEnd && !s.params.centeredSlides) {\n                    s.slideTo(s.slides.length - 1, 0, false, true);\n                }\n                else {\n                    s.slideTo(s.activeIndex, 0, false, true);\n                }\n            }\n        \n        };\n        \n        /*=========================\n          Events\n          ===========================*/\n        \n        //Define Touch Events\n        var desktopEvents = ['mousedown', 'mousemove', 'mouseup'];\n        if (window.navigator.pointerEnabled) desktopEvents = ['pointerdown', 'pointermove', 'pointerup'];\n        else if (window.navigator.msPointerEnabled) desktopEvents = ['MSPointerDown', 'MSPointerMove', 'MSPointerUp'];\n        s.touchEvents = {\n            start : s.support.touch || !s.params.simulateTouch  ? 'touchstart' : desktopEvents[0],\n            move : s.support.touch || !s.params.simulateTouch ? 'touchmove' : desktopEvents[1],\n            end : s.support.touch || !s.params.simulateTouch ? 'touchend' : desktopEvents[2]\n        };\n        \n        \n        // WP8 Touch Events Fix\n        if (window.navigator.pointerEnabled || window.navigator.msPointerEnabled) {\n            (s.params.touchEventsTarget === 'container' ? s.container : s.wrapper).addClass('swiper-wp8-' + s.params.direction);\n        }\n        \n        // Attach/detach events\n        s.initEvents = function (detach) {\n            var actionDom = detach ? 'off' : 'on';\n            var action = detach ? 'removeEventListener' : 'addEventListener';\n            var touchEventsTarget = s.params.touchEventsTarget === 'container' ? s.container[0] : s.wrapper[0];\n            var target = s.support.touch ? touchEventsTarget : document;\n        \n            var moveCapture = s.params.nested ? true : false;\n        \n            //Touch Events\n            if (s.browser.ie) {\n                touchEventsTarget[action](s.touchEvents.start, s.onTouchStart, false);\n                target[action](s.touchEvents.move, s.onTouchMove, moveCapture);\n                target[action](s.touchEvents.end, s.onTouchEnd, false);\n            }\n            else {\n                if (s.support.touch) {\n                    touchEventsTarget[action](s.touchEvents.start, s.onTouchStart, false);\n                    touchEventsTarget[action](s.touchEvents.move, s.onTouchMove, moveCapture);\n                    touchEventsTarget[action](s.touchEvents.end, s.onTouchEnd, false);\n                }\n                if (params.simulateTouch && !s.device.ios && !s.device.android) {\n                    touchEventsTarget[action]('mousedown', s.onTouchStart, false);\n                    target[action]('mousemove', s.onTouchMove, moveCapture);\n                    target[action]('mouseup', s.onTouchEnd, false);\n                }\n            }\n            window[action]('resize', s.onResize);\n        \n            // Next, Prev, Index\n            if (s.params.nextButton) {\n                $(s.params.nextButton)[actionDom]('click', s.onClickNext);\n                if (s.params.a11y && s.a11y) $(s.params.nextButton)[actionDom]('keydown', s.a11y.onEnterKey);\n            }\n            if (s.params.prevButton) {\n                $(s.params.prevButton)[actionDom]('click', s.onClickPrev);\n                if (s.params.a11y && s.a11y) $(s.params.prevButton)[actionDom]('keydown', s.a11y.onEnterKey);\n            }\n            if (s.params.pagination && s.params.paginationClickable) {\n                $(s.paginationContainer)[actionDom]('click', '.' + s.params.bulletClass, s.onClickIndex);\n            }\n        \n            // Prevent Links Clicks\n            if (s.params.preventClicks || s.params.preventClicksPropagation) touchEventsTarget[action]('click', s.preventClicks, true);\n        };\n        s.attachEvents = function (detach) {\n            s.initEvents();\n        };\n        s.detachEvents = function () {\n            s.initEvents(true);\n        };\n        \n        /*=========================\n          Handle Clicks\n          ===========================*/\n        // Prevent Clicks\n        s.allowClick = true;\n        s.preventClicks = function (e) {\n            if (!s.allowClick) {\n                if (s.params.preventClicks) e.preventDefault();\n                if (s.params.preventClicksPropagation) {\n                    e.stopPropagation();\n                    e.stopImmediatePropagation();\n                }\n            }\n        };\n        // Clicks\n        s.onClickNext = function (e) {\n            e.preventDefault();\n            s.slideNext();\n        };\n        s.onClickPrev = function (e) {\n            e.preventDefault();\n            s.slidePrev();\n        };\n        s.onClickIndex = function (e) {\n            e.preventDefault();\n            var index = $(this).index() * s.params.slidesPerGroup;\n            if (s.params.loop) index = index + s.loopedSlides;\n            s.slideTo(index);\n        };\n        \n        /*=========================\n          Handle Touches\n          ===========================*/\n        function findElementInEvent(e, selector) {\n            var el = $(e.target);\n            if (!el.is(selector)) {\n                if (typeof selector === 'string') {\n                    el = el.parents(selector);\n                }\n                else if (selector.nodeType) {\n                    var found;\n                    el.parents().each(function (index, _el) {\n                        if (_el === selector) found = selector;\n                    });\n                    if (!found) return undefined;\n                    else return selector;\n                }\n            }\n            if (el.length === 0) {\n                return undefined;\n            }\n            return el[0];\n        }\n        s.updateClickedSlide = function (e) {\n            var slide = findElementInEvent(e, '.' + s.params.slideClass);\n            if (slide) {\n                s.clickedSlide = slide;\n                s.clickedIndex = $(slide).index();\n            }\n            else {\n                s.clickedSlide = undefined;\n                s.clickedIndex = undefined;\n                return;\n            }\n            if (s.params.slideToClickedSlide && s.clickedIndex !== undefined && s.clickedIndex !== s.activeIndex) {\n                var slideToIndex = s.clickedIndex,\n                    realIndex;\n                if (s.params.loop) {\n                    realIndex = $(s.clickedSlide).attr('data-swiper-slide-index');\n                    if (slideToIndex > s.slides.length - s.params.slidesPerView) {\n                        s.fixLoop();\n                        slideToIndex = s.wrapper.children('.' + s.params.slideClass + '[data-swiper-slide-index=\"' + realIndex + '\"]').eq(0).index();\n                        setTimeout(function () {\n                            s.slideTo(slideToIndex);\n                        }, 0);\n                    }\n                    else if (slideToIndex < s.params.slidesPerView - 1) {\n                        s.fixLoop();\n                        var duplicatedSlides = s.wrapper.children('.' + s.params.slideClass + '[data-swiper-slide-index=\"' + realIndex + '\"]');\n                        slideToIndex = duplicatedSlides.eq(duplicatedSlides.length - 1).index();\n                        setTimeout(function () {\n                            s.slideTo(slideToIndex);\n                        }, 0);\n                    }\n                    else {\n                        s.slideTo(slideToIndex);\n                    }\n                }\n                else {\n                    s.slideTo(slideToIndex);\n                }\n            }\n        };\n        \n        var isTouched,\n            isMoved,\n            touchStartTime,\n            isScrolling,\n            currentTranslate,\n            startTranslate,\n            allowThresholdMove,\n            // Form elements to match\n            formElements = 'input, select, textarea, button',\n            // Last click time\n            lastClickTime = Date.now(), clickTimeout,\n            //Velocities\n            velocities = [],\n            allowMomentumBounce;\n        \n        // Animating Flag\n        s.animating = false;\n        \n        // Touches information\n        s.touches = {\n            startX: 0,\n            startY: 0,\n            currentX: 0,\n            currentY: 0,\n            diff: 0\n        };\n        \n        // Touch handlers\n        var isTouchEvent, startMoving;\n        s.onTouchStart = function (e) {\n            if (e.originalEvent) e = e.originalEvent;\n            isTouchEvent = e.type === 'touchstart';\n            if (!isTouchEvent && 'which' in e && e.which === 3) return;\n            if (s.params.noSwiping && findElementInEvent(e, '.' + s.params.noSwipingClass)) {\n                s.allowClick = true;\n                return;\n            }\n            if (s.params.swipeHandler) {\n                if (!findElementInEvent(e, s.params.swipeHandler)) return;\n            }\n            isTouched = true;\n            isMoved = false;\n            isScrolling = undefined;\n            startMoving = undefined;\n            s.touches.startX = s.touches.currentX = e.type === 'touchstart' ? e.targetTouches[0].pageX : e.pageX;\n            s.touches.startY = s.touches.currentY = e.type === 'touchstart' ? e.targetTouches[0].pageY : e.pageY;\n            touchStartTime = Date.now();\n            s.allowClick = true;\n            s.updateContainerSize();\n            s.swipeDirection = undefined;\n            if (s.params.threshold > 0) allowThresholdMove = false;\n            if (e.type !== 'touchstart') {\n                var preventDefault = true;\n                if ($(e.target).is(formElements)) preventDefault = false;\n                if (document.activeElement && $(document.activeElement).is(formElements)) {\n                    document.activeElement.blur();\n                }\n                if (preventDefault) {\n                    e.preventDefault();\n                }\n            }\n            s.emit('onTouchStart', s, e);\n        };\n        \n        s.onTouchMove = function (e) {\n            if (e.originalEvent) e = e.originalEvent;\n            if (isTouchEvent && e.type === 'mousemove') return;\n            if (e.preventedByNestedSwiper) return;\n            if (s.params.onlyExternal) {\n                isMoved = true;\n                s.allowClick = false;\n                return;\n            }\n            if (isTouchEvent && document.activeElement) {\n                if (e.target === document.activeElement && $(e.target).is(formElements)) {\n                    isMoved = true;\n                    s.allowClick = false;\n                    return;\n                }\n            }\n        \n            s.emit('onTouchMove', s, e);\n        \n            if (e.targetTouches && e.targetTouches.length > 1) return;\n        \n            s.touches.currentX = e.type === 'touchmove' ? e.targetTouches[0].pageX : e.pageX;\n            s.touches.currentY = e.type === 'touchmove' ? e.targetTouches[0].pageY : e.pageY;\n        \n            if (typeof isScrolling === 'undefined') {\n                var touchAngle = Math.atan2(Math.abs(s.touches.currentY - s.touches.startY), Math.abs(s.touches.currentX - s.touches.startX)) * 180 / Math.PI;\n                isScrolling = isH() ? touchAngle > s.params.touchAngle : (90 - touchAngle > s.params.touchAngle);\n            }\n            if (isScrolling) {\n                s.emit('onTouchMoveOpposite', s, e);\n            }\n            if (typeof startMoving === 'undefined' && s.browser.ieTouch) {\n                if (s.touches.currentX !== s.touches.startX || s.touches.currentY !== s.touches.startY) {\n                    startMoving = true;\n                }\n            }\n            if (!isTouched) return;\n            if (isScrolling)  {\n                isTouched = false;\n                return;\n            }\n            if (!startMoving && s.browser.ieTouch) {\n                return;\n            }\n            s.allowClick = false;\n            s.emit('onSliderMove', s, e);\n            e.preventDefault();\n            if (s.params.touchMoveStopPropagation && !s.params.nested) {\n                e.stopPropagation();\n            }\n        \n            if (!isMoved) {\n                if (params.loop) {\n                    s.fixLoop();\n                }\n                startTranslate = s.getWrapperTranslate();\n                s.setWrapperTransition(0);\n                if (s.animating) {\n                    s.wrapper.trigger('webkitTransitionEnd transitionend oTransitionEnd MSTransitionEnd msTransitionEnd');\n                }\n                if (s.params.autoplay && s.autoplaying) {\n                    if (s.params.autoplayDisableOnInteraction) {\n                        s.stopAutoplay();\n                    }\n                    else {\n                        s.pauseAutoplay();\n                    }\n                }\n                allowMomentumBounce = false;\n                //Grab Cursor\n                if (s.params.grabCursor) {\n                    s.container[0].style.cursor = 'move';\n                    s.container[0].style.cursor = '-webkit-grabbing';\n                    s.container[0].style.cursor = '-moz-grabbin';\n                    s.container[0].style.cursor = 'grabbing';\n                }\n            }\n            isMoved = true;\n        \n            var diff = s.touches.diff = isH() ? s.touches.currentX - s.touches.startX : s.touches.currentY - s.touches.startY;\n        \n            diff = diff * s.params.touchRatio;\n            if (s.rtl) diff = -diff;\n        \n            s.swipeDirection = diff > 0 ? 'prev' : 'next';\n            currentTranslate = diff + startTranslate;\n        \n            var disableParentSwiper = true;\n            if ((diff > 0 && currentTranslate > s.minTranslate())) {\n                disableParentSwiper = false;\n                if (s.params.resistance) currentTranslate = s.minTranslate() - 1 + Math.pow(-s.minTranslate() + startTranslate + diff, s.params.resistanceRatio);\n            }\n            else if (diff < 0 && currentTranslate < s.maxTranslate()) {\n                disableParentSwiper = false;\n                if (s.params.resistance) currentTranslate = s.maxTranslate() + 1 - Math.pow(s.maxTranslate() - startTranslate - diff, s.params.resistanceRatio);\n            }\n        \n            if (disableParentSwiper) {\n                e.preventedByNestedSwiper = true;\n            }\n        \n            // Directions locks\n            if (!s.params.allowSwipeToNext && s.swipeDirection === 'next' && currentTranslate < startTranslate) {\n                currentTranslate = startTranslate;\n            }\n            if (!s.params.allowSwipeToPrev && s.swipeDirection === 'prev' && currentTranslate > startTranslate) {\n                currentTranslate = startTranslate;\n            }\n        \n            if (!s.params.followFinger) return;\n        \n            // Threshold\n            if (s.params.threshold > 0) {\n                if (Math.abs(diff) > s.params.threshold || allowThresholdMove) {\n                    if (!allowThresholdMove) {\n                        allowThresholdMove = true;\n                        s.touches.startX = s.touches.currentX;\n                        s.touches.startY = s.touches.currentY;\n                        currentTranslate = startTranslate;\n                        s.touches.diff = isH() ? s.touches.currentX - s.touches.startX : s.touches.currentY - s.touches.startY;\n                        return;\n                    }\n                }\n                else {\n                    currentTranslate = startTranslate;\n                    return;\n                }\n            }\n            // Update active index in free mode\n            if (s.params.freeMode || s.params.watchSlidesProgress) {\n                s.updateActiveIndex();\n            }\n            if (s.params.freeMode) {\n                //Velocity\n                if (velocities.length === 0) {\n                    velocities.push({\n                        position: s.touches[isH() ? 'startX' : 'startY'],\n                        time: touchStartTime\n                    });\n                }\n                velocities.push({\n                    position: s.touches[isH() ? 'currentX' : 'currentY'],\n                    time: (new Date()).getTime()\n                });\n            }\n            // Update progress\n            s.updateProgress(currentTranslate);\n            // Update translate\n            s.setWrapperTranslate(currentTranslate);\n        };\n        s.onTouchEnd = function (e) {\n            if (e.originalEvent) e = e.originalEvent;\n            s.emit('onTouchEnd', s, e);\n            if (!isTouched) return;\n            //Return Grab Cursor\n            if (s.params.grabCursor && isMoved && isTouched) {\n                s.container[0].style.cursor = 'move';\n                s.container[0].style.cursor = '-webkit-grab';\n                s.container[0].style.cursor = '-moz-grab';\n                s.container[0].style.cursor = 'grab';\n            }\n        \n            // Time diff\n            var touchEndTime = Date.now();\n            var timeDiff = touchEndTime - touchStartTime;\n        \n            // Tap, doubleTap, Click\n            if (s.allowClick) {\n                s.updateClickedSlide(e);\n                s.emit('onTap', s, e);\n                if (timeDiff < 300 && (touchEndTime - lastClickTime) > 300) {\n                    if (clickTimeout) clearTimeout(clickTimeout);\n                    clickTimeout = setTimeout(function () {\n                        if (!s) return;\n                        if (s.params.paginationHide && s.paginationContainer.length > 0 && !$(e.target).hasClass(s.params.bulletClass)) {\n                            s.paginationContainer.toggleClass(s.params.paginationHiddenClass);\n                        }\n                        s.emit('onClick', s, e);\n                    }, 300);\n        \n                }\n                if (timeDiff < 300 && (touchEndTime - lastClickTime) < 300) {\n                    if (clickTimeout) clearTimeout(clickTimeout);\n                    s.emit('onDoubleTap', s, e);\n                }\n            }\n        \n            lastClickTime = Date.now();\n            setTimeout(function () {\n                if (s && s.allowClick) s.allowClick = true;\n            }, 0);\n        \n            if (!isTouched || !isMoved || !s.swipeDirection || s.touches.diff === 0 || currentTranslate === startTranslate) {\n                isTouched = isMoved = false;\n                return;\n            }\n            isTouched = isMoved = false;\n        \n            var currentPos;\n            if (s.params.followFinger) {\n                currentPos = s.rtl ? s.translate : -s.translate;\n            }\n            else {\n                currentPos = -currentTranslate;\n            }\n            if (s.params.freeMode) {\n                if (currentPos < -s.minTranslate()) {\n                    s.slideTo(s.activeIndex);\n                    return;\n                }\n                else if (currentPos > -s.maxTranslate()) {\n                    s.slideTo(s.slides.length - 1);\n                    return;\n                }\n        \n                if (s.params.freeModeMomentum) {\n                    if (velocities.length > 1) {\n                        var lastMoveEvent = velocities.pop(), velocityEvent = velocities.pop();\n        \n                        var distance = lastMoveEvent.position - velocityEvent.position;\n                        var time = lastMoveEvent.time - velocityEvent.time;\n                        s.velocity = distance / time;\n                        s.velocity = s.velocity / 2;\n                        if (Math.abs(s.velocity) < 0.02) {\n                            s.velocity = 0;\n                        }\n                        // this implies that the user stopped moving a finger then released.\n                        // There would be no events with distance zero, so the last event is stale.\n                        if (time > 150 || (new Date().getTime() - lastMoveEvent.time) > 300) {\n                            s.velocity = 0;\n                        }\n                    } else {\n                        s.velocity = 0;\n                    }\n        \n                    velocities.length = 0;\n                    var momentumDuration = 1000 * s.params.freeModeMomentumRatio;\n                    var momentumDistance = s.velocity * momentumDuration;\n        \n                    var newPosition = s.translate + momentumDistance;\n                    if (s.rtl) newPosition = - newPosition;\n                    var doBounce = false;\n                    var afterBouncePosition;\n                    var bounceAmount = Math.abs(s.velocity) * 20 * s.params.freeModeMomentumBounceRatio;\n                    if (newPosition < s.maxTranslate()) {\n                        if (s.params.freeModeMomentumBounce) {\n                            if (newPosition + s.maxTranslate() < -bounceAmount) {\n                                newPosition = s.maxTranslate() - bounceAmount;\n                            }\n                            afterBouncePosition = s.maxTranslate();\n                            doBounce = true;\n                            allowMomentumBounce = true;\n                        }\n                        else {\n                            newPosition = s.maxTranslate();\n                        }\n                    }\n                    if (newPosition > s.minTranslate()) {\n                        if (s.params.freeModeMomentumBounce) {\n                            if (newPosition - s.minTranslate() > bounceAmount) {\n                                newPosition = s.minTranslate() + bounceAmount;\n                            }\n                            afterBouncePosition = s.minTranslate();\n                            doBounce = true;\n                            allowMomentumBounce = true;\n                        }\n                        else {\n                            newPosition = s.minTranslate();\n                        }\n                    }\n                    //Fix duration\n                    if (s.velocity !== 0) {\n                        if (s.rtl) {\n                            momentumDuration = Math.abs((-newPosition - s.translate) / s.velocity);\n                        }\n                        else {\n                            momentumDuration = Math.abs((newPosition - s.translate) / s.velocity);\n                        }\n                    }\n        \n                    if (s.params.freeModeMomentumBounce && doBounce) {\n                        s.updateProgress(afterBouncePosition);\n                        s.setWrapperTransition(momentumDuration);\n                        s.setWrapperTranslate(newPosition);\n                        s.onTransitionStart();\n                        s.animating = true;\n                        s.wrapper.transitionEnd(function () {\n                            if (!allowMomentumBounce) return;\n                            s.emit('onMomentumBounce', s);\n        \n                            s.setWrapperTransition(s.params.speed);\n                            s.setWrapperTranslate(afterBouncePosition);\n                            s.wrapper.transitionEnd(function () {\n                                s.onTransitionEnd();\n                            });\n                        });\n                    } else if (s.velocity) {\n                        s.updateProgress(newPosition);\n                        s.setWrapperTransition(momentumDuration);\n                        s.setWrapperTranslate(newPosition);\n                        s.onTransitionStart();\n                        if (!s.animating) {\n                            s.animating = true;\n                            s.wrapper.transitionEnd(function () {\n                                s.onTransitionEnd();\n                            });\n                        }\n        \n                    } else {\n                        s.updateProgress(newPosition);\n                    }\n        \n                    s.updateActiveIndex();\n                }\n                if (!s.params.freeModeMomentum || timeDiff >= s.params.longSwipesMs) {\n                    s.updateProgress();\n                    s.updateActiveIndex();\n                }\n                return;\n            }\n        \n            // Find current slide\n            var i, stopIndex = 0, groupSize = s.slidesSizesGrid[0];\n            for (i = 0; i < s.slidesGrid.length; i += s.params.slidesPerGroup) {\n                if (typeof s.slidesGrid[i + s.params.slidesPerGroup] !== 'undefined') {\n                    if (currentPos >= s.slidesGrid[i] && currentPos < s.slidesGrid[i + s.params.slidesPerGroup]) {\n                        stopIndex = i;\n                        groupSize = s.slidesGrid[i + s.params.slidesPerGroup] - s.slidesGrid[i];\n                    }\n                }\n                else {\n                    if (currentPos >= s.slidesGrid[i]) {\n                        stopIndex = i;\n                        groupSize = s.slidesGrid[s.slidesGrid.length - 1] - s.slidesGrid[s.slidesGrid.length - 2];\n                    }\n                }\n            }\n        \n            // Find current slide size\n            var ratio = (currentPos - s.slidesGrid[stopIndex]) / groupSize;\n        \n            if (timeDiff > s.params.longSwipesMs) {\n                // Long touches\n                if (!s.params.longSwipes) {\n                    s.slideTo(s.activeIndex);\n                    return;\n                }\n                if (s.swipeDirection === 'next') {\n                    if (ratio >= s.params.longSwipesRatio) s.slideTo(stopIndex + s.params.slidesPerGroup);\n                    else s.slideTo(stopIndex);\n        \n                }\n                if (s.swipeDirection === 'prev') {\n                    if (ratio > (1 - s.params.longSwipesRatio)) s.slideTo(stopIndex + s.params.slidesPerGroup);\n                    else s.slideTo(stopIndex);\n                }\n            }\n            else {\n                // Short swipes\n                if (!s.params.shortSwipes) {\n                    s.slideTo(s.activeIndex);\n                    return;\n                }\n                if (s.swipeDirection === 'next') {\n                    s.slideTo(stopIndex + s.params.slidesPerGroup);\n        \n                }\n                if (s.swipeDirection === 'prev') {\n                    s.slideTo(stopIndex);\n                }\n            }\n        };\n        /*=========================\n          Transitions\n          ===========================*/\n        s._slideTo = function (slideIndex, speed) {\n            return s.slideTo(slideIndex, speed, true, true);\n        };\n        s.slideTo = function (slideIndex, speed, runCallbacks, internal) {\n            if (typeof runCallbacks === 'undefined') runCallbacks = true;\n            if (typeof slideIndex === 'undefined') slideIndex = 0;\n            if (slideIndex < 0) slideIndex = 0;\n            s.snapIndex = Math.floor(slideIndex / s.params.slidesPerGroup);\n            if (s.snapIndex >= s.snapGrid.length) s.snapIndex = s.snapGrid.length - 1;\n        \n            var translate = - s.snapGrid[s.snapIndex];\n        \n            // Stop autoplay\n        \n            if (s.params.autoplay && s.autoplaying) {\n                if (internal || !s.params.autoplayDisableOnInteraction) {\n                    s.pauseAutoplay(speed);\n                }\n                else {\n                    s.stopAutoplay();\n                }\n            }\n            // Update progress\n            s.updateProgress(translate);\n        \n            // Normalize slideIndex\n            for (var i = 0; i < s.slidesGrid.length; i++) {\n                if (- translate >= s.slidesGrid[i]) {\n                    slideIndex = i;\n                }\n            }\n        \n            if (typeof speed === 'undefined') speed = s.params.speed;\n            s.previousIndex = s.activeIndex || 0;\n            s.activeIndex = slideIndex;\n        \n            if (translate === s.translate) {\n                s.updateClasses();\n                return false;\n            }\n            s.onTransitionStart(runCallbacks);\n            var translateX = isH() ? translate : 0, translateY = isH() ? 0 : translate;\n            if (speed === 0) {\n                s.setWrapperTransition(0);\n                s.setWrapperTranslate(translate);\n                s.onTransitionEnd(runCallbacks);\n            }\n            else {\n                s.setWrapperTransition(speed);\n                s.setWrapperTranslate(translate);\n                if (!s.animating) {\n                    s.animating = true;\n                    s.wrapper.transitionEnd(function () {\n                        s.onTransitionEnd(runCallbacks);\n                    });\n                }\n        \n            }\n            s.updateClasses();\n            return true;\n        };\n        \n        s.onTransitionStart = function (runCallbacks) {\n            if (typeof runCallbacks === 'undefined') runCallbacks = true;\n            if (s.lazy) s.lazy.onTransitionStart();\n            if (runCallbacks) {\n                s.emit('onTransitionStart', s);\n                if (s.activeIndex !== s.previousIndex) {\n                    s.emit('onSlideChangeStart', s);\n                }\n            }\n        };\n        s.onTransitionEnd = function (runCallbacks) {\n            s.animating = false;\n            s.setWrapperTransition(0);\n            if (typeof runCallbacks === 'undefined') runCallbacks = true;\n            if (s.lazy) s.lazy.onTransitionEnd();\n            if (runCallbacks) {\n                s.emit('onTransitionEnd', s);\n                if (s.activeIndex !== s.previousIndex) {\n                    s.emit('onSlideChangeEnd', s);\n                }\n            }\n            if (s.params.hashnav && s.hashnav) {\n                s.hashnav.setHash();\n            }\n        \n        };\n        s.slideNext = function (runCallbacks, speed, internal) {\n            if (s.params.loop) {\n                if (s.animating) return false;\n                s.fixLoop();\n                var clientLeft = s.container[0].clientLeft;\n                return s.slideTo(s.activeIndex + s.params.slidesPerGroup, speed, runCallbacks, internal);\n            }\n            else return s.slideTo(s.activeIndex + s.params.slidesPerGroup, speed, runCallbacks, internal);\n        };\n        s._slideNext = function (speed) {\n            return s.slideNext(true, speed, true);\n        };\n        s.slidePrev = function (runCallbacks, speed, internal) {\n            if (s.params.loop) {\n                if (s.animating) return false;\n                s.fixLoop();\n                var clientLeft = s.container[0].clientLeft;\n                return s.slideTo(s.activeIndex - 1, speed, runCallbacks, internal);\n            }\n            else return s.slideTo(s.activeIndex - 1, speed, runCallbacks, internal);\n        };\n        s._slidePrev = function (speed) {\n            return s.slidePrev(true, speed, true);\n        };\n        s.slideReset = function (runCallbacks, speed, internal) {\n            return s.slideTo(s.activeIndex, speed, runCallbacks);\n        };\n        \n        /*=========================\n          Translate/transition helpers\n          ===========================*/\n        s.setWrapperTransition = function (duration, byController) {\n            s.wrapper.transition(duration);\n            if (s.params.effect !== 'slide' && s.effects[s.params.effect]) {\n                s.effects[s.params.effect].setTransition(duration);\n            }\n            if (s.params.parallax && s.parallax) {\n                s.parallax.setTransition(duration);\n            }\n            if (s.params.scrollbar && s.scrollbar) {\n                s.scrollbar.setTransition(duration);\n            }\n            if (s.params.control && s.controller) {\n                s.controller.setTransition(duration, byController);\n            }\n            s.emit('onSetTransition', s, duration);\n        };\n        s.setWrapperTranslate = function (translate, updateActiveIndex, byController) {\n            var x = 0, y = 0, z = 0;\n            if (isH()) {\n                x = s.rtl ? -translate : translate;\n            }\n            else {\n                y = translate;\n            }\n            if (!s.params.virtualTranslate) {\n                if (s.support.transforms3d) s.wrapper.transform('translate3d(' + x + 'px, ' + y + 'px, ' + z + 'px)');\n                else s.wrapper.transform('translate(' + x + 'px, ' + y + 'px)');\n            }\n        \n            s.translate = isH() ? x : y;\n        \n            if (updateActiveIndex) s.updateActiveIndex();\n            if (s.params.effect !== 'slide' && s.effects[s.params.effect]) {\n                s.effects[s.params.effect].setTranslate(s.translate);\n            }\n            if (s.params.parallax && s.parallax) {\n                s.parallax.setTranslate(s.translate);\n            }\n            if (s.params.scrollbar && s.scrollbar) {\n                s.scrollbar.setTranslate(s.translate);\n            }\n            if (s.params.control && s.controller) {\n                s.controller.setTranslate(s.translate, byController);\n            }\n            s.emit('onSetTranslate', s, s.translate);\n        };\n        \n        s.getTranslate = function (el, axis) {\n            var matrix, curTransform, curStyle, transformMatrix;\n        \n            // automatic axis detection\n            if (typeof axis === 'undefined') {\n                axis = 'x';\n            }\n        \n            if (s.params.virtualTranslate) {\n                return s.rtl ? -s.translate : s.translate;\n            }\n        \n            curStyle = window.getComputedStyle(el, null);\n            if (window.WebKitCSSMatrix) {\n                // Some old versions of Webkit choke when 'none' is passed; pass\n                // empty string instead in this case\n                transformMatrix = new WebKitCSSMatrix(curStyle.webkitTransform === 'none' ? '' : curStyle.webkitTransform);\n            }\n            else {\n                transformMatrix = curStyle.MozTransform || curStyle.OTransform || curStyle.MsTransform || curStyle.msTransform  || curStyle.transform || curStyle.getPropertyValue('transform').replace('translate(', 'matrix(1, 0, 0, 1,');\n                matrix = transformMatrix.toString().split(',');\n            }\n        \n            if (axis === 'x') {\n                //Latest Chrome and webkits Fix\n                if (window.WebKitCSSMatrix)\n                    curTransform = transformMatrix.m41;\n                //Crazy IE10 Matrix\n                else if (matrix.length === 16)\n                    curTransform = parseFloat(matrix[12]);\n                //Normal Browsers\n                else\n                    curTransform = parseFloat(matrix[4]);\n            }\n            if (axis === 'y') {\n                //Latest Chrome and webkits Fix\n                if (window.WebKitCSSMatrix)\n                    curTransform = transformMatrix.m42;\n                //Crazy IE10 Matrix\n                else if (matrix.length === 16)\n                    curTransform = parseFloat(matrix[13]);\n                //Normal Browsers\n                else\n                    curTransform = parseFloat(matrix[5]);\n            }\n            if (s.rtl && curTransform) curTransform = -curTransform;\n            return curTransform || 0;\n        };\n        s.getWrapperTranslate = function (axis) {\n            if (typeof axis === 'undefined') {\n                axis = isH() ? 'x' : 'y';\n            }\n            return s.getTranslate(s.wrapper[0], axis);\n        };\n        \n        /*=========================\n          Observer\n          ===========================*/\n        s.observers = [];\n        function initObserver(target, options) {\n            options = options || {};\n            // create an observer instance\n            var ObserverFunc = window.MutationObserver || window.WebkitMutationObserver;\n            var observer = new ObserverFunc(function (mutations) {\n                mutations.forEach(function (mutation) {\n                    s.onResize();\n                    s.emit('onObserverUpdate', s, mutation);\n                });\n            });\n        \n            observer.observe(target, {\n                attributes: typeof options.attributes === 'undefined' ? true : options.attributes,\n                childList: typeof options.childList === 'undefined' ? true : options.childList,\n                characterData: typeof options.characterData === 'undefined' ? true : options.characterData\n            });\n        \n            s.observers.push(observer);\n        }\n        s.initObservers = function () {\n            if (s.params.observeParents) {\n                var containerParents = s.container.parents();\n                for (var i = 0; i < containerParents.length; i++) {\n                    initObserver(containerParents[i]);\n                }\n            }\n        \n            // Observe container\n            initObserver(s.container[0], {childList: false});\n        \n            // Observe wrapper\n            initObserver(s.wrapper[0], {attributes: false});\n        };\n        s.disconnectObservers = function () {\n            for (var i = 0; i < s.observers.length; i++) {\n                s.observers[i].disconnect();\n            }\n            s.observers = [];\n        };\n        /*=========================\n          Loop\n          ===========================*/\n        // Create looped slides\n        s.createLoop = function () {\n            // Remove duplicated slides\n            s.wrapper.children('.' + s.params.slideClass + '.' + s.params.slideDuplicateClass).remove();\n        \n            var slides = s.wrapper.children('.' + s.params.slideClass);\n            s.loopedSlides = parseInt(s.params.loopedSlides || s.params.slidesPerView, 10);\n            s.loopedSlides = s.loopedSlides + s.params.loopAdditionalSlides;\n            if (s.loopedSlides > slides.length) {\n                s.loopedSlides = slides.length;\n            }\n        \n            var prependSlides = [], appendSlides = [], i;\n            slides.each(function (index, el) {\n                var slide = $(this);\n                if (index < s.loopedSlides) appendSlides.push(el);\n                if (index < slides.length && index >= slides.length - s.loopedSlides) prependSlides.push(el);\n                slide.attr('data-swiper-slide-index', index);\n            });\n            for (i = 0; i < appendSlides.length; i++) {\n                s.wrapper.append($(appendSlides[i].cloneNode(true)).addClass(s.params.slideDuplicateClass));\n            }\n            for (i = prependSlides.length - 1; i >= 0; i--) {\n                s.wrapper.prepend($(prependSlides[i].cloneNode(true)).addClass(s.params.slideDuplicateClass));\n            }\n        };\n        s.destroyLoop = function () {\n            s.wrapper.children('.' + s.params.slideClass + '.' + s.params.slideDuplicateClass).remove();\n            s.slides.removeAttr('data-swiper-slide-index');\n        };\n        s.fixLoop = function () {\n            var newIndex;\n            //Fix For Negative Oversliding\n            if (s.activeIndex < s.loopedSlides) {\n                newIndex = s.slides.length - s.loopedSlides * 3 + s.activeIndex;\n                newIndex = newIndex + s.loopedSlides;\n                s.slideTo(newIndex, 0, false, true);\n            }\n            //Fix For Positive Oversliding\n            else if ((s.params.slidesPerView === 'auto' && s.activeIndex >= s.loopedSlides * 2) || (s.activeIndex > s.slides.length - s.params.slidesPerView * 2)) {\n                newIndex = -s.slides.length + s.activeIndex + s.loopedSlides;\n                newIndex = newIndex + s.loopedSlides;\n                s.slideTo(newIndex, 0, false, true);\n            }\n        };\n        /*=========================\n          Append/Prepend/Remove Slides\n          ===========================*/\n        s.appendSlide = function (slides) {\n            if (s.params.loop) {\n                s.destroyLoop();\n            }\n            if (typeof slides === 'object' && slides.length) {\n                for (var i = 0; i < slides.length; i++) {\n                    if (slides[i]) s.wrapper.append(slides[i]);\n                }\n            }\n            else {\n                s.wrapper.append(slides);\n            }\n            if (s.params.loop) {\n                s.createLoop();\n            }\n            if (!(s.params.observer && s.support.observer)) {\n                s.update(true);\n            }\n        };\n        s.prependSlide = function (slides) {\n            if (s.params.loop) {\n                s.destroyLoop();\n            }\n            var newActiveIndex = s.activeIndex + 1;\n            if (typeof slides === 'object' && slides.length) {\n                for (var i = 0; i < slides.length; i++) {\n                    if (slides[i]) s.wrapper.prepend(slides[i]);\n                }\n                newActiveIndex = s.activeIndex + slides.length;\n            }\n            else {\n                s.wrapper.prepend(slides);\n            }\n            if (s.params.loop) {\n                s.createLoop();\n            }\n            if (!(s.params.observer && s.support.observer)) {\n                s.update(true);\n            }\n            s.slideTo(newActiveIndex, 0, false);\n        };\n        s.removeSlide = function (slidesIndexes) {\n            if (s.params.loop) {\n                s.destroyLoop();\n            }\n            var newActiveIndex = s.activeIndex,\n                indexToRemove;\n            if (typeof slidesIndexes === 'object' && slidesIndexes.length) {\n                for (var i = 0; i < slidesIndexes.length; i++) {\n                    indexToRemove = slidesIndexes[i];\n                    if (s.slides[indexToRemove]) s.slides.eq(indexToRemove).remove();\n                    if (indexToRemove < newActiveIndex) newActiveIndex--;\n                }\n                newActiveIndex = Math.max(newActiveIndex, 0);\n            }\n            else {\n                indexToRemove = slidesIndexes;\n                if (s.slides[indexToRemove]) s.slides.eq(indexToRemove).remove();\n                if (indexToRemove < newActiveIndex) newActiveIndex--;\n                newActiveIndex = Math.max(newActiveIndex, 0);\n            }\n        \n            if (!(s.params.observer && s.support.observer)) {\n                s.update(true);\n            }\n            s.slideTo(newActiveIndex, 0, false);\n        };\n        s.removeAllSlides = function () {\n            var slidesIndexes = [];\n            for (var i = 0; i < s.slides.length; i++) {\n                slidesIndexes.push(i);\n            }\n            s.removeSlide(slidesIndexes);\n        };\n        \n\n        /*=========================\n          Effects\n          ===========================*/\n        s.effects = {\n            fade: {\n                fadeIndex: null,\n                setTranslate: function () {\n                    for (var i = 0; i < s.slides.length; i++) {\n                        var slide = s.slides.eq(i);\n                        var offset = slide[0].swiperSlideOffset;\n                        var tx = -offset;\n                        if (!s.params.virtualTranslate) tx = tx - s.translate;\n                        var ty = 0;\n                        if (!isH()) {\n                            ty = tx;\n                            tx = 0;\n                        }\n                        var slideOpacity = s.params.fade.crossFade ?\n                                Math.max(1 - Math.abs(slide[0].progress), 0) :\n                                1 + Math.min(Math.max(slide[0].progress, -1), 0);\n                        if (slideOpacity > 0 && slideOpacity < 1) {\n                            s.effects.fade.fadeIndex = i;\n                        }\n                        slide\n                            .css({\n                                opacity: slideOpacity\n                            })\n                            .transform('translate3d(' + tx + 'px, ' + ty + 'px, 0px)');\n        \n                    }\n                },\n                setTransition: function (duration) {\n                    s.slides.transition(duration);\n                    if (s.params.virtualTranslate && duration !== 0) {\n                        var fadeIndex = s.effects.fade.fadeIndex !== null ? s.effects.fade.fadeIndex : s.activeIndex;\n                        s.slides.eq(fadeIndex).transitionEnd(function () {\n                            var triggerEvents = ['webkitTransitionEnd', 'transitionend', 'oTransitionEnd', 'MSTransitionEnd', 'msTransitionEnd'];\n                            for (var i = 0; i < triggerEvents.length; i++) {\n                                s.wrapper.trigger(triggerEvents[i]);\n                            }\n                        });\n                    }\n                }\n            },\n            cube: {\n                setTranslate: function () {\n                    var wrapperRotate = 0, cubeShadow;\n                    if (s.params.cube.shadow) {\n                        if (isH()) {\n                            cubeShadow = s.wrapper.find('.swiper-cube-shadow');\n                            if (cubeShadow.length === 0) {\n                                cubeShadow = $('<div class=\"swiper-cube-shadow\"></div>');\n                                s.wrapper.append(cubeShadow);\n                            }\n                            cubeShadow.css({height: s.width + 'px'});\n                        }\n                        else {\n                            cubeShadow = s.container.find('.swiper-cube-shadow');\n                            if (cubeShadow.length === 0) {\n                                cubeShadow = $('<div class=\"swiper-cube-shadow\"></div>');\n                                s.container.append(cubeShadow);\n                            }\n                        }\n                    }\n                    for (var i = 0; i < s.slides.length; i++) {\n                        var slide = s.slides.eq(i);\n                        var slideAngle = i * 90;\n                        var round = Math.floor(slideAngle / 360);\n                        if (s.rtl) {\n                            slideAngle = -slideAngle;\n                            round = Math.floor(-slideAngle / 360);\n                        }\n                        var progress = Math.max(Math.min(slide[0].progress, 1), -1);\n                        var tx = 0, ty = 0, tz = 0;\n                        if (i % 4 === 0) {\n                            tx = - round * 4 * s.size;\n                            tz = 0;\n                        }\n                        else if ((i - 1) % 4 === 0) {\n                            tx = 0;\n                            tz = - round * 4 * s.size;\n                        }\n                        else if ((i - 2) % 4 === 0) {\n                            tx = s.size + round * 4 * s.size;\n                            tz = s.size;\n                        }\n                        else if ((i - 3) % 4 === 0) {\n                            tx = - s.size;\n                            tz = 3 * s.size + s.size * 4 * round;\n                        }\n                        if (s.rtl) {\n                            tx = -tx;\n                        }\n                        \n                        if (!isH()) {\n                            ty = tx;\n                            tx = 0;\n                        }\n                        \n                        var transform = 'rotateX(' + (isH() ? 0 : -slideAngle) + 'deg) rotateY(' + (isH() ? slideAngle : 0) + 'deg) translate3d(' + tx + 'px, ' + ty + 'px, ' + tz + 'px)';\n                        if (progress <= 1 && progress > -1) {\n                            wrapperRotate = i * 90 + progress * 90;\n                            if (s.rtl) wrapperRotate = -i * 90 - progress * 90;\n                        }\n                        slide.transform(transform);\n                        if (s.params.cube.slideShadows) {\n                            //Set shadows\n                            var shadowBefore = isH() ? slide.find('.swiper-slide-shadow-left') : slide.find('.swiper-slide-shadow-top');\n                            var shadowAfter = isH() ? slide.find('.swiper-slide-shadow-right') : slide.find('.swiper-slide-shadow-bottom');\n                            if (shadowBefore.length === 0) {\n                                shadowBefore = $('<div class=\"swiper-slide-shadow-' + (isH() ? 'left' : 'top') + '\"></div>');\n                                slide.append(shadowBefore);\n                            }\n                            if (shadowAfter.length === 0) {\n                                shadowAfter = $('<div class=\"swiper-slide-shadow-' + (isH() ? 'right' : 'bottom') + '\"></div>');\n                                slide.append(shadowAfter);\n                            }\n                            var shadowOpacity = slide[0].progress;\n                            if (shadowBefore.length) shadowBefore[0].style.opacity = -slide[0].progress;\n                            if (shadowAfter.length) shadowAfter[0].style.opacity = slide[0].progress;\n                        }\n                    }\n                    s.wrapper.css({\n                        '-webkit-transform-origin': '50% 50% -' + (s.size / 2) + 'px',\n                        '-moz-transform-origin': '50% 50% -' + (s.size / 2) + 'px',\n                        '-ms-transform-origin': '50% 50% -' + (s.size / 2) + 'px',\n                        'transform-origin': '50% 50% -' + (s.size / 2) + 'px'\n                    });\n                        \n                    if (s.params.cube.shadow) {\n                        if (isH()) {\n                            cubeShadow.transform('translate3d(0px, ' + (s.width / 2 + s.params.cube.shadowOffset) + 'px, ' + (-s.width / 2) + 'px) rotateX(90deg) rotateZ(0deg) scale(' + (s.params.cube.shadowScale) + ')');\n                        }\n                        else {\n                            var shadowAngle = Math.abs(wrapperRotate) - Math.floor(Math.abs(wrapperRotate) / 90) * 90;\n                            var multiplier = 1.5 - (Math.sin(shadowAngle * 2 * Math.PI / 360) / 2 + Math.cos(shadowAngle * 2 * Math.PI / 360) / 2);\n                            var scale1 = s.params.cube.shadowScale,\n                                scale2 = s.params.cube.shadowScale / multiplier,\n                                offset = s.params.cube.shadowOffset;\n                            cubeShadow.transform('scale3d(' + scale1 + ', 1, ' + scale2 + ') translate3d(0px, ' + (s.height / 2 + offset) + 'px, ' + (-s.height / 2 / scale2) + 'px) rotateX(-90deg)');\n                        }\n                    }\n                    var zFactor = (s.isSafari || s.isUiWebView) ? (-s.size / 2) : 0;\n                    s.wrapper.transform('translate3d(0px,0,' + zFactor + 'px) rotateX(' + (isH() ? 0 : wrapperRotate) + 'deg) rotateY(' + (isH() ? -wrapperRotate : 0) + 'deg)');\n                },\n                setTransition: function (duration) {\n                    s.slides.transition(duration).find('.swiper-slide-shadow-top, .swiper-slide-shadow-right, .swiper-slide-shadow-bottom, .swiper-slide-shadow-left').transition(duration);\n                    if (s.params.cube.shadow && !isH()) {\n                        s.container.find('.swiper-cube-shadow').transition(duration);\n                    }\n                }\n            },\n            coverflow: {\n                setTranslate: function () {\n                    var transform = s.translate;\n                    var center = isH() ? -transform + s.width / 2 : -transform + s.height / 2;\n                    var rotate = isH() ? s.params.coverflow.rotate: -s.params.coverflow.rotate;\n                    var translate = s.params.coverflow.depth;\n                    //Each slide offset from center\n                    for (var i = 0, length = s.slides.length; i < length; i++) {\n                        var slide = s.slides.eq(i);\n                        var slideSize = s.slidesSizesGrid[i];\n                        var slideOffset = slide[0].swiperSlideOffset;\n                        var offsetMultiplier = (center - slideOffset - slideSize / 2) / slideSize * s.params.coverflow.modifier;\n        \n                        var rotateY = isH() ? rotate * offsetMultiplier : 0;\n                        var rotateX = isH() ? 0 : rotate * offsetMultiplier;\n                        // var rotateZ = 0\n                        var translateZ = -translate * Math.abs(offsetMultiplier);\n        \n                        var translateY = isH() ? 0 : s.params.coverflow.stretch * (offsetMultiplier);\n                        var translateX = isH() ? s.params.coverflow.stretch * (offsetMultiplier) : 0;\n        \n                        //Fix for ultra small values\n                        if (Math.abs(translateX) < 0.001) translateX = 0;\n                        if (Math.abs(translateY) < 0.001) translateY = 0;\n                        if (Math.abs(translateZ) < 0.001) translateZ = 0;\n                        if (Math.abs(rotateY) < 0.001) rotateY = 0;\n                        if (Math.abs(rotateX) < 0.001) rotateX = 0;\n        \n                        var slideTransform = 'translate3d(' + translateX + 'px,' + translateY + 'px,' + translateZ + 'px)  rotateX(' + rotateX + 'deg) rotateY(' + rotateY + 'deg)';\n        \n                        slide.transform(slideTransform);\n                        slide[0].style.zIndex = -Math.abs(Math.round(offsetMultiplier)) + 1;\n                        if (s.params.coverflow.slideShadows) {\n                            //Set shadows\n                            var shadowBefore = isH() ? slide.find('.swiper-slide-shadow-left') : slide.find('.swiper-slide-shadow-top');\n                            var shadowAfter = isH() ? slide.find('.swiper-slide-shadow-right') : slide.find('.swiper-slide-shadow-bottom');\n                            if (shadowBefore.length === 0) {\n                                shadowBefore = $('<div class=\"swiper-slide-shadow-' + (isH() ? 'left' : 'top') + '\"></div>');\n                                slide.append(shadowBefore);\n                            }\n                            if (shadowAfter.length === 0) {\n                                shadowAfter = $('<div class=\"swiper-slide-shadow-' + (isH() ? 'right' : 'bottom') + '\"></div>');\n                                slide.append(shadowAfter);\n                            }\n                            if (shadowBefore.length) shadowBefore[0].style.opacity = offsetMultiplier > 0 ? offsetMultiplier : 0;\n                            if (shadowAfter.length) shadowAfter[0].style.opacity = (-offsetMultiplier) > 0 ? -offsetMultiplier : 0;\n                        }\n                    }\n        \n                    //Set correct perspective for IE10\n                    if (s.browser.ie) {\n                        var ws = s.wrapper[0].style;\n                        ws.perspectiveOrigin = center + 'px 50%';\n                    }\n                },\n                setTransition: function (duration) {\n                    s.slides.transition(duration).find('.swiper-slide-shadow-top, .swiper-slide-shadow-right, .swiper-slide-shadow-bottom, .swiper-slide-shadow-left').transition(duration);\n                }\n            }\n        };\n\n        /*=========================\n          Images Lazy Loading\n          ===========================*/\n        s.lazy = {\n            initialImageLoaded: false,\n            loadImageInSlide: function (index) {\n                if (typeof index === 'undefined') return;\n                if (s.slides.length === 0) return;\n                \n                var slide = s.slides.eq(index);\n                var img = slide.find('img.swiper-lazy:not(.swiper-lazy-loaded):not(.swiper-lazy-loading)');\n                if (img.length === 0) return;\n        \n                img.each(function () {\n                    var _img = $(this);\n                    _img.addClass('swiper-lazy-loading');\n        \n                    var src = _img.attr('data-src');\n        \n                    s.loadImage(_img[0], src, false, function () {\n                        _img.attr('src', src);\n                        _img.removeAttr('data-src');\n                        _img.addClass('swiper-lazy-loaded').removeClass('swiper-lazy-loading');\n                        slide.find('.swiper-lazy-preloader, .preloader').remove();\n        \n                        s.emit('onLazyImageReady', s, slide[0], _img[0]);\n                    });\n                    \n                    s.emit('onLazyImageLoad', s, slide[0], _img[0]);\n                });\n                    \n            },\n            load: function () {\n                if (s.params.watchSlidesVisibility) {\n                    s.wrapper.children('.' + s.params.slideVisibleClass).each(function () {\n                        s.lazy.loadImageInSlide($(this).index());\n                    });\n                }\n                else {\n                    if (s.params.slidesPerView > 1) {\n                        for (var i = s.activeIndex; i < s.activeIndex + s.params.slidesPerView ; i++) {\n                            if (s.slides[i]) s.lazy.loadImageInSlide(i);\n                        }\n                    }\n                    else {\n                        s.lazy.loadImageInSlide(s.activeIndex);    \n                    }\n                }\n                if (s.params.lazyLoadingInPrevNext) {\n                    var nextSlide = s.wrapper.children('.' + s.params.slideNextClass);\n                    if (nextSlide.length > 0) s.lazy.loadImageInSlide(nextSlide.index());\n        \n                    var prevSlide = s.wrapper.children('.' + s.params.slidePrevClass);\n                    if (prevSlide.length > 0) s.lazy.loadImageInSlide(prevSlide.index());\n                }\n            },\n            onTransitionStart: function () {\n                if (s.params.lazyLoading) {\n                    if (s.params.lazyLoadingOnTransitionStart || (!s.params.lazyLoadingOnTransitionStart && !s.lazy.initialImageLoaded)) {\n                        s.lazy.initialImageLoaded = true;\n                        s.lazy.load();\n                    }\n                }\n            },\n            onTransitionEnd: function () {\n                if (s.params.lazyLoading && !s.params.lazyLoadingOnTransitionStart) {\n                    s.lazy.load();\n                }\n            }\n        };\n        \n\n        /*=========================\n          Scrollbar\n          ===========================*/\n        s.scrollbar = {\n            set: function () {\n                if (!s.params.scrollbar) return;\n                var sb = s.scrollbar;\n                sb.track = $(s.params.scrollbar);\n                sb.drag = sb.track.find('.swiper-scrollbar-drag');\n                if (sb.drag.length === 0) {\n                    sb.drag = $('<div class=\"swiper-scrollbar-drag\"></div>');\n                    sb.track.append(sb.drag);\n                }\n                sb.drag[0].style.width = '';\n                sb.drag[0].style.height = '';\n                sb.trackSize = isH() ? sb.track[0].offsetWidth : sb.track[0].offsetHeight;\n                \n                sb.divider = s.size / s.virtualSize;\n                sb.moveDivider = sb.divider * (sb.trackSize / s.size);\n                sb.dragSize = sb.trackSize * sb.divider;\n        \n                if (isH()) {\n                    sb.drag[0].style.width = sb.dragSize + 'px';\n                }\n                else {\n                    sb.drag[0].style.height = sb.dragSize + 'px';\n                }\n        \n                if (sb.divider >= 1) {\n                    sb.track[0].style.display = 'none';\n                }\n                else {\n                    sb.track[0].style.display = '';\n                }\n                if (s.params.scrollbarHide) {\n                    sb.track[0].style.opacity = 0;\n                }\n            },\n            setTranslate: function () {\n                if (!s.params.scrollbar) return;\n                var diff;\n                var sb = s.scrollbar;\n                var translate = s.translate || 0;\n                var newPos;\n                \n                var newSize = sb.dragSize;\n                newPos = (sb.trackSize - sb.dragSize) * s.progress;\n                if (s.rtl && isH()) {\n                    newPos = -newPos;\n                    if (newPos > 0) {\n                        newSize = sb.dragSize - newPos;\n                        newPos = 0;\n                    }\n                    else if (-newPos + sb.dragSize > sb.trackSize) {\n                        newSize = sb.trackSize + newPos;\n                    }\n                }\n                else {\n                    if (newPos < 0) {\n                        newSize = sb.dragSize + newPos;\n                        newPos = 0;\n                    }\n                    else if (newPos + sb.dragSize > sb.trackSize) {\n                        newSize = sb.trackSize - newPos;\n                    }\n                }\n                if (isH()) {\n                    if (s.support.transforms3d) {\n                        sb.drag.transform('translate3d(' + (newPos) + 'px, 0, 0)');\n                    }\n                    else {\n                        sb.drag.transform('translateX(' + (newPos) + 'px)');   \n                    }\n                    sb.drag[0].style.width = newSize + 'px';\n                }\n                else {\n                    if (s.support.transforms3d) {\n                        sb.drag.transform('translate3d(0px, ' + (newPos) + 'px, 0)');\n                    }\n                    else {\n                        sb.drag.transform('translateY(' + (newPos) + 'px)');   \n                    }\n                    sb.drag[0].style.height = newSize + 'px';\n                }\n                if (s.params.scrollbarHide) {\n                    clearTimeout(sb.timeout);\n                    sb.track[0].style.opacity = 1;\n                    sb.timeout = setTimeout(function () {\n                        sb.track[0].style.opacity = 0;\n                        sb.track.transition(400);\n                    }, 1000);\n                }\n            },\n            setTransition: function (duration) {\n                if (!s.params.scrollbar) return;\n                s.scrollbar.drag.transition(duration);\n            }\n        };\n\n        /*=========================\n          Controller\n          ===========================*/\n        s.controller = {\n            setTranslate: function (translate, byController) {\n                var controlled = s.params.control;\n                var multiplier, controlledTranslate;\n                if (s.isArray(controlled)) {\n                    for (var i = 0; i < controlled.length; i++) {\n                        if (controlled[i] !== byController && controlled[i] instanceof Swiper) {\n                            translate = controlled[i].rtl && controlled[i].params.direction === 'horizontal' ? -s.translate : s.translate;\n                            multiplier = (controlled[i].maxTranslate() - controlled[i].minTranslate()) / (s.maxTranslate() - s.minTranslate());\n                            controlledTranslate = (translate - s.minTranslate()) * multiplier + controlled[i].minTranslate();\n                            if (s.params.controlInverse) {\n                                controlledTranslate = controlled[i].maxTranslate() - controlledTranslate;\n                            }\n                            controlled[i].updateProgress(controlledTranslate);\n                            controlled[i].setWrapperTranslate(controlledTranslate, false, s);\n                            controlled[i].updateActiveIndex();\n                        }\n                    }\n                }\n                else if (controlled instanceof Swiper && byController !== controlled) {\n                    translate = controlled.rtl && controlled.params.direction === 'horizontal' ? -s.translate : s.translate;\n                    multiplier = (controlled.maxTranslate() - controlled.minTranslate()) / (s.maxTranslate() - s.minTranslate());\n                    controlledTranslate = (translate - s.minTranslate()) * multiplier + controlled.minTranslate();\n                    if (s.params.controlInverse) {\n                        controlledTranslate = controlled.maxTranslate() - controlledTranslate;\n                    }\n                    controlled.updateProgress(controlledTranslate);\n                    controlled.setWrapperTranslate(controlledTranslate, false, s);\n                    controlled.updateActiveIndex();\n                }\n            },\n            setTransition: function (duration, byController) {\n                var controlled = s.params.control;\n                if (s.isArray(controlled)) {\n                    for (var i = 0; i < controlled.length; i++) {\n                        if (controlled[i] !== byController && controlled[i] instanceof Swiper) {\n                            controlled[i].setWrapperTransition(duration, s);\n                        }\n                    }\n                }\n                else if (controlled instanceof Swiper && byController !== controlled) {\n                    controlled.setWrapperTransition(duration, s);\n                }\n            }\n        };\n\n        /*=========================\n          Hash Navigation\n          ===========================*/\n        s.hashnav = {\n            init: function () {\n                if (!s.params.hashnav) return;\n                s.hashnav.initialized = true;\n                var hash = document.location.hash.replace('#', '');\n                if (!hash) return;\n                var speed = 0;\n                for (var i = 0, length = s.slides.length; i < length; i++) {\n                    var slide = s.slides.eq(i);\n                    var slideHash = slide.attr('data-hash');\n                    if (slideHash === hash && !slide.hasClass(s.params.slideDuplicateClass)) {\n                        var index = slide.index();\n                        s.slideTo(index, speed, s.params.runCallbacksOnInit, true);\n                    }\n                }\n            },\n            setHash: function () {\n                if (!s.hashnav.initialized || !s.params.hashnav) return;\n                document.location.hash = s.slides.eq(s.activeIndex).attr('data-hash') || '';\n            }\n        };\n\n        /*=========================\n          Keyboard Control\n          ===========================*/\n        function handleKeyboard(e) {\n            if (e.originalEvent) e = e.originalEvent; //jquery fix\n            var kc = e.keyCode || e.charCode;\n            if (e.shiftKey || e.altKey || e.ctrlKey || e.metaKey) {\n                return;\n            }\n            if (document.activeElement && document.activeElement.nodeName && (document.activeElement.nodeName.toLowerCase() === 'input' || document.activeElement.nodeName.toLowerCase() === 'textarea')) {\n                return;\n            }\n            if (kc === 37 || kc === 39 || kc === 38 || kc === 40) {\n                var inView = false;\n                //Check that swiper should be inside of visible area of window\n                if (s.container.parents('.swiper-slide').length > 0 && s.container.parents('.swiper-slide-active').length === 0) {\n                    return;\n                }\n                var windowScroll = {\n                    left: window.pageXOffset,\n                    top: window.pageYOffset\n                };\n                var windowWidth = window.innerWidth;\n                var windowHeight = window.innerHeight;\n                var swiperOffset = s.container.offset();\n                \n                var swiperCoord = [\n                    [swiperOffset.left, swiperOffset.top],\n                    [swiperOffset.left + s.width, swiperOffset.top],\n                    [swiperOffset.left, swiperOffset.top + s.height],\n                    [swiperOffset.left + s.width, swiperOffset.top + s.height]\n                ];\n                for (var i = 0; i < swiperCoord.length; i++) {\n                    var point = swiperCoord[i];\n                    if (\n                        point[0] >= windowScroll.left && point[0] <= windowScroll.left + windowWidth &&\n                        point[1] >= windowScroll.top && point[1] <= windowScroll.top + windowHeight\n                    ) {\n                        inView = true;\n                    }\n        \n                }\n                if (!inView) return;\n            }\n            if (isH()) {\n                if (kc === 37 || kc === 39) {\n                    if (e.preventDefault) e.preventDefault();\n                    else e.returnValue = false;\n                }\n                if (kc === 39) s.slideNext();\n                if (kc === 37) s.slidePrev();\n            }\n            else {\n                if (kc === 38 || kc === 40) {\n                    if (e.preventDefault) e.preventDefault();\n                    else e.returnValue = false;\n                }\n                if (kc === 40) s.slideNext();\n                if (kc === 38) s.slidePrev();\n            }\n        }\n        s.disableKeyboardControl = function () {\n            $(document).off('keydown', handleKeyboard);\n        };\n        s.enableKeyboardControl = function () {\n            $(document).on('keydown', handleKeyboard);\n        };\n        \n\n        /*=========================\n          Mousewheel Control\n          ===========================*/\n        s._wheelEvent = false;\n        s._lastWheelScrollTime = (new Date()).getTime();\n        if (s.params.mousewheelControl) {\n            if (document.onmousewheel !== undefined) {\n                s._wheelEvent = 'mousewheel';\n            }\n            if (!s._wheelEvent) {\n                try {\n                    new WheelEvent('wheel');\n                    s._wheelEvent = 'wheel';\n                } catch (e) {}\n            }\n            if (!s._wheelEvent) {\n                s._wheelEvent = 'DOMMouseScroll';\n            }\n        }\n        function handleMousewheel(e) {\n            if (e.originalEvent) e = e.originalEvent; //jquery fix\n            var we = s._wheelEvent;\n            var delta = 0;\n            //Opera & IE\n            if (e.detail) delta = -e.detail;\n            //WebKits\n            else if (we === 'mousewheel') {\n                if (s.params.mousewheelForceToAxis) {\n                    if (isH()) {\n                        if (Math.abs(e.wheelDeltaX) > Math.abs(e.wheelDeltaY)) delta = e.wheelDeltaX;\n                        else return;\n                    }\n                    else {\n                        if (Math.abs(e.wheelDeltaY) > Math.abs(e.wheelDeltaX)) delta = e.wheelDeltaY;\n                        else return;\n                    }\n                }\n                else {\n                    delta = e.wheelDelta;\n                }\n            }\n            //Old FireFox\n            else if (we === 'DOMMouseScroll') delta = -e.detail;\n            //New FireFox\n            else if (we === 'wheel') {\n                if (s.params.mousewheelForceToAxis) {\n                    if (isH()) {\n                        if (Math.abs(e.deltaX) > Math.abs(e.deltaY)) delta = -e.deltaX;\n                        else return;\n                    }\n                    else {\n                        if (Math.abs(e.deltaY) > Math.abs(e.deltaX)) delta = -e.deltaY;\n                        else return;\n                    }\n                }\n                else {\n                    delta = Math.abs(e.deltaX) > Math.abs(e.deltaY) ? - e.deltaX : - e.deltaY;\n                }\n            }\n        \n            if (!s.params.freeMode) {\n                if ((new Date()).getTime() - s._lastWheelScrollTime > 60) {\n                    if (delta < 0) s.slideNext();\n                    else s.slidePrev();\n                }\n                s._lastWheelScrollTime = (new Date()).getTime();\n        \n            }\n            else {\n                //Freemode or scrollContainer:\n                var position = s.getWrapperTranslate() + delta;\n        \n                if (position > 0) position = 0;\n                if (position < s.maxTranslate()) position = s.maxTranslate();\n        \n                s.setWrapperTransition(0);\n                s.setWrapperTranslate(position);\n                s.updateProgress();\n                s.updateActiveIndex();\n        \n                // Return page scroll on edge positions\n                if (position === 0 || position === s.maxTranslate()) return;\n            }\n            if (s.params.autoplay) s.stopAutoplay();\n        \n            if (e.preventDefault) e.preventDefault();\n            else e.returnValue = false;\n            return false;\n        }\n        s.disableMousewheelControl = function () {\n            if (!s._wheelEvent) return false;\n            s.container.off(s._wheelEvent, handleMousewheel);\n            return true;\n        };\n        \n        s.enableMousewheelControl = function () {\n            if (!s._wheelEvent) return false;\n            s.container.on(s._wheelEvent, handleMousewheel);\n            return true;\n        };\n\n        /*=========================\n          Parallax\n          ===========================*/\n        function setParallaxTransform(el, progress) {\n            el = $(el);\n            var p, pX, pY;\n            \n            p = el.attr('data-swiper-parallax') || '0';\n            pX = el.attr('data-swiper-parallax-x');\n            pY = el.attr('data-swiper-parallax-y');\n            if (pX || pY) {\n                pX = pX || '0';\n                pY = pY || '0';\n            }\n            else {\n                if (isH()) {\n                    pX = p;\n                    pY = '0';\n                }\n                else {\n                    pY = p;\n                    pX = '0';\n                }\n            }\n            if ((pX).indexOf('%') >= 0) {\n                pX = parseInt(pX, 10) * progress + '%';\n            }\n            else {\n                pX = pX * progress + 'px' ;\n            }\n            if ((pY).indexOf('%') >= 0) {\n                pY = parseInt(pY, 10) * progress + '%';\n            }\n            else {\n                pY = pY * progress + 'px' ;\n            }\n            el.transform('translate3d(' + pX + ', ' + pY + ',0px)');\n        }   \n        s.parallax = {\n            setTranslate: function () {\n                s.container.children('[data-swiper-parallax], [data-swiper-parallax-x], [data-swiper-parallax-y]').each(function(){\n                    setParallaxTransform(this, s.progress);\n                    \n                });\n                s.slides.each(function () {\n                    var slide = $(this);\n                    slide.find('[data-swiper-parallax], [data-swiper-parallax-x], [data-swiper-parallax-y]').each(function () {\n                        var progress = Math.min(Math.max(slide[0].progress, -1), 1);\n                        setParallaxTransform(this, progress);\n                    });\n                });\n            },\n            setTransition: function (duration) {\n                if (typeof duration === 'undefined') duration = s.params.speed;\n                s.container.find('[data-swiper-parallax], [data-swiper-parallax-x], [data-swiper-parallax-y]').each(function(){\n                    var el = $(this);\n                    var parallaxDuration = parseInt(el.attr('data-swiper-parallax-duration'), 10) || duration;\n                    if (duration === 0) parallaxDuration = 0;\n                    el.transition(parallaxDuration);\n                });\n            }\n        };\n        \n\n        /*=========================\n          Plugins API. Collect all and init all plugins\n          ===========================*/\n        s._plugins = [];\n        for (var plugin in s.plugins) {\n            var p = s.plugins[plugin](s, s.params[plugin]);\n            if (p) s._plugins.push(p);\n        }\n        // Method to call all plugins event/method\n        s.callPlugins = function (eventName) {\n            for (var i = 0; i < s._plugins.length; i++) {\n                if (eventName in s._plugins[i]) {\n                    s._plugins[i][eventName](arguments[1], arguments[2], arguments[3], arguments[4], arguments[5]);\n                }\n            }\n        };\n\n        /*=========================\n          Events/Callbacks/Plugins Emitter\n          ===========================*/\n        function normalizeEventName (eventName) {\n            if (eventName.indexOf('on') !== 0) {\n                if (eventName[0] !== eventName[0].toUpperCase()) {\n                    eventName = 'on' + eventName[0].toUpperCase() + eventName.substring(1);\n                }\n                else {\n                    eventName = 'on' + eventName;\n                }\n            }\n            return eventName;\n        }\n        s.emitterEventListeners = {\n        \n        };\n        s.emit = function (eventName) {\n            // Trigger callbacks\n            if (s.params[eventName]) {\n                s.params[eventName](arguments[1], arguments[2], arguments[3], arguments[4], arguments[5]);\n            }\n            var i;\n            // Trigger events\n            if (s.emitterEventListeners[eventName]) {\n                for (i = 0; i < s.emitterEventListeners[eventName].length; i++) {\n                    s.emitterEventListeners[eventName][i](arguments[1], arguments[2], arguments[3], arguments[4], arguments[5]);\n                }\n            }\n            // Trigger plugins\n            if (s.callPlugins) s.callPlugins(eventName, arguments[1], arguments[2], arguments[3], arguments[4], arguments[5]);\n        };\n        s.on = function (eventName, handler) {\n            eventName = normalizeEventName(eventName);\n            if (!s.emitterEventListeners[eventName]) s.emitterEventListeners[eventName] = [];\n            s.emitterEventListeners[eventName].push(handler);\n            return s;\n        };\n        s.off = function (eventName, handler) {\n            var i;\n            eventName = normalizeEventName(eventName);\n            if (typeof handler === 'undefined') {\n                // Remove all handlers for such event\n                s.emitterEventListeners[eventName] = [];\n                return s;\n            }\n            if (!s.emitterEventListeners[eventName] || s.emitterEventListeners[eventName].length === 0) return;\n            for (i = 0; i < s.emitterEventListeners[eventName].length; i++) {\n                if(s.emitterEventListeners[eventName][i] === handler) s.emitterEventListeners[eventName].splice(i, 1);\n            }\n            return s;\n        };\n        s.once = function (eventName, handler) {\n            eventName = normalizeEventName(eventName);\n            var _handler = function () {\n                handler(arguments[0], arguments[1], arguments[2], arguments[3], arguments[4]);\n                s.off(eventName, _handler);\n            };\n            s.on(eventName, _handler);\n            return s;\n        };\n\n        // Accessibility tools\n        s.a11y = {\n            makeFocusable: function ($el) {\n                $el[0].tabIndex = '0';\n                return $el;\n            },\n            addRole: function ($el, role) {\n                $el.attr('role', role);\n                return $el;\n            },\n        \n            addLabel: function ($el, label) {\n                $el.attr('aria-label', label);\n                return $el;\n            },\n        \n            disable: function ($el) {\n                $el.attr('aria-disabled', true);\n                return $el;\n            },\n        \n            enable: function ($el) {\n                $el.attr('aria-disabled', false);\n                return $el;\n            },\n        \n            onEnterKey: function (event) {\n                if (event.keyCode !== 13) return;\n                if ($(event.target).is(s.params.nextButton)) {\n                    s.onClickNext(event);\n                    if (s.isEnd) {\n                        s.a11y.notify(s.params.lastSlideMsg);\n                    }\n                    else {\n                        s.a11y.notify(s.params.nextSlideMsg);\n                    }\n                }\n                else if ($(event.target).is(s.params.prevButton)) {\n                    s.onClickPrev(event);\n                    if (s.isBeginning) {\n                        s.a11y.notify(s.params.firstSlideMsg);\n                    }\n                    else {\n                        s.a11y.notify(s.params.prevSlideMsg);\n                    }\n                }\n            },\n        \n            liveRegion: $('<span class=\"swiper-notification\" aria-live=\"assertive\" aria-atomic=\"true\"></span>'),\n        \n            notify: function (message) {\n                var notification = s.a11y.liveRegion;\n                if (notification.length === 0) return;\n                notification.html('');\n                notification.html(message);\n            },\n            init: function () {\n                // Setup accessibility\n                if (s.params.nextButton) {\n                    var nextButton = $(s.params.nextButton);\n                    s.a11y.makeFocusable(nextButton);\n                    s.a11y.addRole(nextButton, 'button');\n                    s.a11y.addLabel(nextButton, s.params.nextSlideMsg);\n                }\n                if (s.params.prevButton) {\n                    var prevButton = $(s.params.prevButton);\n                    s.a11y.makeFocusable(prevButton);\n                    s.a11y.addRole(prevButton, 'button');\n                    s.a11y.addLabel(prevButton, s.params.prevSlideMsg);\n                }\n        \n                $(s.container).append(s.a11y.liveRegion);\n            },\n            destroy: function () {\n                if (s.a11y.liveRegion && s.a11y.liveRegion.length > 0) s.a11y.liveRegion.remove();\n            }\n        };\n        \n\n        /*=========================\n          Init/Destroy\n          ===========================*/\n        s.init = function () {\n            if (s.params.loop) s.createLoop();\n            s.updateContainerSize();\n            s.updateSlidesSize();\n            s.updatePagination();\n            if (s.params.scrollbar && s.scrollbar) {\n                s.scrollbar.set();\n            }\n            if (s.params.effect !== 'slide' && s.effects[s.params.effect]) {\n                if (!s.params.loop) s.updateProgress();\n                s.effects[s.params.effect].setTranslate();\n            }\n            if (s.params.loop) {\n                s.slideTo(s.params.initialSlide + s.loopedSlides, 0, s.params.runCallbacksOnInit);\n            }\n            else {\n                s.slideTo(s.params.initialSlide, 0, s.params.runCallbacksOnInit);\n                if (s.params.initialSlide === 0) {\n                    if (s.parallax && s.params.parallax) s.parallax.setTranslate();\n                    if (s.lazy && s.params.lazyLoading) s.lazy.load();\n                }\n            }\n            s.attachEvents();\n            if (s.params.observer && s.support.observer) {\n                s.initObservers();\n            }\n            if (s.params.preloadImages && !s.params.lazyLoading) {\n                s.preloadImages();\n            }\n            if (s.params.autoplay) {\n                s.startAutoplay();\n            }\n            if (s.params.keyboardControl) {\n                if (s.enableKeyboardControl) s.enableKeyboardControl();\n            }\n            if (s.params.mousewheelControl) {\n                if (s.enableMousewheelControl) s.enableMousewheelControl();\n            }\n            if (s.params.hashnav) {\n                if (s.hashnav) s.hashnav.init();\n            }\n            if (s.params.a11y && s.a11y) s.a11y.init();\n            s.emit('onInit', s);\n        };\n        \n        // Cleanup dynamic styles\n        s.cleanupStyles = function () {\n            // Container\n            s.container.removeClass(s.classNames.join(' ')).removeAttr('style');\n        \n            // Wrapper\n            s.wrapper.removeAttr('style');\n        \n            // Slides\n            if (s.slides && s.slides.length) {\n                s.slides\n                    .removeClass([\n                      s.params.slideVisibleClass,\n                      s.params.slideActiveClass,\n                      s.params.slideNextClass,\n                      s.params.slidePrevClass\n                    ].join(' '))\n                    .removeAttr('style')\n                    .removeAttr('data-swiper-column')\n                    .removeAttr('data-swiper-row');\n            }\n        \n            // Pagination/Bullets\n            if (s.paginationContainer && s.paginationContainer.length) {\n                s.paginationContainer.removeClass(s.params.paginationHiddenClass);\n            }\n            if (s.bullets && s.bullets.length) {\n                s.bullets.removeClass(s.params.bulletActiveClass);\n            }\n        \n            // Buttons\n            if (s.params.prevButton) $(s.params.prevButton).removeClass(s.params.buttonDisabledClass);\n            if (s.params.nextButton) $(s.params.nextButton).removeClass(s.params.buttonDisabledClass);\n        \n            // Scrollbar\n            if (s.params.scrollbar && s.scrollbar) {\n                if (s.scrollbar.track && s.scrollbar.track.length) s.scrollbar.track.removeAttr('style');\n                if (s.scrollbar.drag && s.scrollbar.drag.length) s.scrollbar.drag.removeAttr('style');\n            }\n        };\n        \n        // Destroy\n        s.destroy = function (deleteInstance, cleanupStyles) {\n            // Detach evebts\n            s.detachEvents();\n            // Stop autoplay\n            s.stopAutoplay();\n            // Destroy loop\n            if (s.params.loop) {\n                s.destroyLoop();\n            }\n            // Cleanup styles\n            if (cleanupStyles) {\n                s.cleanupStyles();\n            }\n            // Disconnect observer\n            s.disconnectObservers();\n            // Disable keyboard/mousewheel\n            if (s.params.keyboardControl) {\n                if (s.disableKeyboardControl) s.disableKeyboardControl();\n            }\n            if (s.params.mousewheelControl) {\n                if (s.disableMousewheelControl) s.disableMousewheelControl();\n            }\n            // Disable a11y\n            if (s.params.a11y && s.a11y) s.a11y.destroy();\n            // Destroy callback\n            s.emit('onDestroy');\n            // Delete instance\n            if (deleteInstance !== false) s = null;\n        };\n        \n        s.init();\n        \n\n        \n        // Return swiper instance\n        return s;\n    };\n    \n\n    /*==================================================\n        Prototype\n    ====================================================*/\n    Swiper.prototype = {\n        isSafari: (function () {\n            var ua = navigator.userAgent.toLowerCase();\n            return (ua.indexOf('safari') >= 0 && ua.indexOf('chrome') < 0 && ua.indexOf('android') < 0);\n        })(),\n        isUiWebView: /(iPhone|iPod|iPad).*AppleWebKit(?!.*Safari)/i.test(navigator.userAgent),\n        isArray: function (arr) {\n            return Object.prototype.toString.apply(arr) === '[object Array]';\n        },\n        /*==================================================\n        Browser\n        ====================================================*/\n        browser: {\n            ie: window.navigator.pointerEnabled || window.navigator.msPointerEnabled,\n            ieTouch: (window.navigator.msPointerEnabled && window.navigator.msMaxTouchPoints > 1) || (window.navigator.pointerEnabled && window.navigator.maxTouchPoints > 1),\n        },\n        /*==================================================\n        Devices\n        ====================================================*/\n        device: (function () {\n            var ua = navigator.userAgent;\n            var android = ua.match(/(Android);?[\\s\\/]+([\\d.]+)?/);\n            var ipad = ua.match(/(iPad).*OS\\s([\\d_]+)/);\n            var ipod = ua.match(/(iPod)(.*OS\\s([\\d_]+))?/);\n            var iphone = !ipad && ua.match(/(iPhone\\sOS)\\s([\\d_]+)/);\n            return {\n                ios: ipad || iphone || ipad,\n                android: android\n            };\n        })(),\n        /*==================================================\n        Feature Detection\n        ====================================================*/\n        support: {\n            touch : (window.Modernizr && Modernizr.touch === true) || (function () {\n                return !!(('ontouchstart' in window) || window.DocumentTouch && document instanceof DocumentTouch);\n            })(),\n    \n            transforms3d : (window.Modernizr && Modernizr.csstransforms3d === true) || (function () {\n                var div = document.createElement('div').style;\n                return ('webkitPerspective' in div || 'MozPerspective' in div || 'OPerspective' in div || 'MsPerspective' in div || 'perspective' in div);\n            })(),\n    \n            flexbox: (function () {\n                var div = document.createElement('div').style;\n                var styles = ('alignItems webkitAlignItems webkitBoxAlign msFlexAlign mozBoxAlign webkitFlexDirection msFlexDirection mozBoxDirection mozBoxOrient webkitBoxDirection webkitBoxOrient').split(' ');\n                for (var i = 0; i < styles.length; i++) {\n                    if (styles[i] in div) return true;\n                }\n            })(),\n    \n            observer: (function () {\n                return ('MutationObserver' in window || 'WebkitMutationObserver' in window);\n            })()\n        },\n        /*==================================================\n        Plugins\n        ====================================================*/\n        plugins: {}\n    };\n    \n\n    /*===========================\n    Dom7 Library\n    ===========================*/\n    var Dom7 = (function () {\n        var Dom7 = function (arr) {\n            var _this = this, i = 0;\n            // Create array-like object\n            for (i = 0; i < arr.length; i++) {\n                _this[i] = arr[i];\n            }\n            _this.length = arr.length;\n            // Return collection with methods\n            return this;\n        };\n        var $ = function (selector, context) {\n            var arr = [], i = 0;\n            if (selector && !context) {\n                if (selector instanceof Dom7) {\n                    return selector;\n                }\n            }\n            if (selector) {\n                // String\n                if (typeof selector === 'string') {\n                    var els, tempParent, html = selector.trim();\n                    if (html.indexOf('<') >= 0 && html.indexOf('>') >= 0) {\n                        var toCreate = 'div';\n                        if (html.indexOf('<li') === 0) toCreate = 'ul';\n                        if (html.indexOf('<tr') === 0) toCreate = 'tbody';\n                        if (html.indexOf('<td') === 0 || html.indexOf('<th') === 0) toCreate = 'tr';\n                        if (html.indexOf('<tbody') === 0) toCreate = 'table';\n                        if (html.indexOf('<option') === 0) toCreate = 'select';\n                        tempParent = document.createElement(toCreate);\n                        tempParent.innerHTML = selector;\n                        for (i = 0; i < tempParent.childNodes.length; i++) {\n                            arr.push(tempParent.childNodes[i]);\n                        }\n                    }\n                    else {\n                        if (!context && selector[0] === '#' && !selector.match(/[ .<>:~]/)) {\n                            // Pure ID selector\n                            els = [document.getElementById(selector.split('#')[1])];\n                        }\n                        else {\n                            // Other selectors\n                            els = (context || document).querySelectorAll(selector);\n                        }\n                        for (i = 0; i < els.length; i++) {\n                            if (els[i]) arr.push(els[i]);\n                        }\n                    }\n                }\n                // Node/element\n                else if (selector.nodeType || selector === window || selector === document) {\n                    arr.push(selector);\n                }\n                //Array of elements or instance of Dom\n                else if (selector.length > 0 && selector[0].nodeType) {\n                    for (i = 0; i < selector.length; i++) {\n                        arr.push(selector[i]);\n                    }\n                }\n            }\n            return new Dom7(arr);\n        };\n        Dom7.prototype = {\n            // Classes and attriutes\n            addClass: function (className) {\n                if (typeof className === 'undefined') {\n                    return this;\n                }\n                var classes = className.split(' ');\n                for (var i = 0; i < classes.length; i++) {\n                    for (var j = 0; j < this.length; j++) {\n                        this[j].classList.add(classes[i]);\n                    }\n                }\n                return this;\n            },\n            removeClass: function (className) {\n                var classes = className.split(' ');\n                for (var i = 0; i < classes.length; i++) {\n                    for (var j = 0; j < this.length; j++) {\n                        this[j].classList.remove(classes[i]);\n                    }\n                }\n                return this;\n            },\n            hasClass: function (className) {\n                if (!this[0]) return false;\n                else return this[0].classList.contains(className);\n            },\n            toggleClass: function (className) {\n                var classes = className.split(' ');\n                for (var i = 0; i < classes.length; i++) {\n                    for (var j = 0; j < this.length; j++) {\n                        this[j].classList.toggle(classes[i]);\n                    }\n                }\n                return this;\n            },\n            attr: function (attrs, value) {\n                if (arguments.length === 1 && typeof attrs === 'string') {\n                    // Get attr\n                    if (this[0]) return this[0].getAttribute(attrs);\n                    else return undefined;\n                }\n                else {\n                    // Set attrs\n                    for (var i = 0; i < this.length; i++) {\n                        if (arguments.length === 2) {\n                            // String\n                            this[i].setAttribute(attrs, value);\n                        }\n                        else {\n                            // Object\n                            for (var attrName in attrs) {\n                                this[i][attrName] = attrs[attrName];\n                                this[i].setAttribute(attrName, attrs[attrName]);\n                            }\n                        }\n                    }\n                    return this;\n                }\n            },\n            removeAttr: function (attr) {\n                for (var i = 0; i < this.length; i++) {\n                    this[i].removeAttribute(attr);\n                }\n                return this;\n            },\n            data: function (key, value) {\n                if (typeof value === 'undefined') {\n                    // Get value\n                    if (this[0]) {\n                        var dataKey = this[0].getAttribute('data-' + key);\n                        if (dataKey) return dataKey;\n                        else if (this[0].dom7ElementDataStorage && (key in this[0].dom7ElementDataStorage)) return this[0].dom7ElementDataStorage[key];\n                        else return undefined;\n                    }\n                    else return undefined;\n                }\n                else {\n                    // Set value\n                    for (var i = 0; i < this.length; i++) {\n                        var el = this[i];\n                        if (!el.dom7ElementDataStorage) el.dom7ElementDataStorage = {};\n                        el.dom7ElementDataStorage[key] = value;\n                    }\n                    return this;\n                }\n            },\n            // Transforms\n            transform : function (transform) {\n                for (var i = 0; i < this.length; i++) {\n                    var elStyle = this[i].style;\n                    elStyle.webkitTransform = elStyle.MsTransform = elStyle.msTransform = elStyle.MozTransform = elStyle.OTransform = elStyle.transform = transform;\n                }\n                return this;\n            },\n            transition: function (duration) {\n                if (typeof duration !== 'string') {\n                    duration = duration + 'ms';\n                }\n                for (var i = 0; i < this.length; i++) {\n                    var elStyle = this[i].style;\n                    elStyle.webkitTransitionDuration = elStyle.MsTransitionDuration = elStyle.msTransitionDuration = elStyle.MozTransitionDuration = elStyle.OTransitionDuration = elStyle.transitionDuration = duration;\n                }\n                return this;\n            },\n            //Events\n            on: function (eventName, targetSelector, listener, capture) {\n                function handleLiveEvent(e) {\n                    var target = e.target;\n                    if ($(target).is(targetSelector)) listener.call(target, e);\n                    else {\n                        var parents = $(target).parents();\n                        for (var k = 0; k < parents.length; k++) {\n                            if ($(parents[k]).is(targetSelector)) listener.call(parents[k], e);\n                        }\n                    }\n                }\n                var events = eventName.split(' ');\n                var i, j;\n                for (i = 0; i < this.length; i++) {\n                    if (typeof targetSelector === 'function' || targetSelector === false) {\n                        // Usual events\n                        if (typeof targetSelector === 'function') {\n                            listener = arguments[1];\n                            capture = arguments[2] || false;\n                        }\n                        for (j = 0; j < events.length; j++) {\n                            this[i].addEventListener(events[j], listener, capture);\n                        }\n                    }\n                    else {\n                        //Live events\n                        for (j = 0; j < events.length; j++) {\n                            if (!this[i].dom7LiveListeners) this[i].dom7LiveListeners = [];\n                            this[i].dom7LiveListeners.push({listener: listener, liveListener: handleLiveEvent});\n                            this[i].addEventListener(events[j], handleLiveEvent, capture);\n                        }\n                    }\n                }\n    \n                return this;\n            },\n            off: function (eventName, targetSelector, listener, capture) {\n                var events = eventName.split(' ');\n                for (var i = 0; i < events.length; i++) {\n                    for (var j = 0; j < this.length; j++) {\n                        if (typeof targetSelector === 'function' || targetSelector === false) {\n                            // Usual events\n                            if (typeof targetSelector === 'function') {\n                                listener = arguments[1];\n                                capture = arguments[2] || false;\n                            }\n                            this[j].removeEventListener(events[i], listener, capture);\n                        }\n                        else {\n                            // Live event\n                            if (this[j].dom7LiveListeners) {\n                                for (var k = 0; k < this[j].dom7LiveListeners.length; k++) {\n                                    if (this[j].dom7LiveListeners[k].listener === listener) {\n                                        this[j].removeEventListener(events[i], this[j].dom7LiveListeners[k].liveListener, capture);\n                                    }\n                                }\n                            }\n                        }\n                    }\n                }\n                return this;\n            },\n            once: function (eventName, targetSelector, listener, capture) {\n                var dom = this;\n                if (typeof targetSelector === 'function') {\n                    targetSelector = false;\n                    listener = arguments[1];\n                    capture = arguments[2];\n                }\n                function proxy(e) {\n                    listener(e);\n                    dom.off(eventName, targetSelector, proxy, capture);\n                }\n                dom.on(eventName, targetSelector, proxy, capture);\n            },\n            trigger: function (eventName, eventData) {\n                for (var i = 0; i < this.length; i++) {\n                    var evt;\n                    try {\n                        evt = new CustomEvent(eventName, {detail: eventData, bubbles: true, cancelable: true});\n                    }\n                    catch (e) {\n                        evt = document.createEvent('Event');\n                        evt.initEvent(eventName, true, true);\n                        evt.detail = eventData;\n                    }\n                    this[i].dispatchEvent(evt);\n                }\n                return this;\n            },\n            transitionEnd: function (callback) {\n                var events = ['webkitTransitionEnd', 'transitionend', 'oTransitionEnd', 'MSTransitionEnd', 'msTransitionEnd'],\n                    i, j, dom = this;\n                function fireCallBack(e) {\n                    /*jshint validthis:true */\n                    if (e.target !== this) return;\n                    callback.call(this, e);\n                    for (i = 0; i < events.length; i++) {\n                        dom.off(events[i], fireCallBack);\n                    }\n                }\n                if (callback) {\n                    for (i = 0; i < events.length; i++) {\n                        dom.on(events[i], fireCallBack);\n                    }\n                }\n                return this;\n            },\n            // Sizing/Styles\n            width: function () {\n                if (this[0] === window) {\n                    return window.innerWidth;\n                }\n                else {\n                    if (this.length > 0) {\n                        return parseFloat(this.css('width'));\n                    }\n                    else {\n                        return null;\n                    }\n                }\n            },\n            outerWidth: function (includeMargins) {\n                if (this.length > 0) {\n                    if (includeMargins)\n                        return this[0].offsetWidth + parseFloat(this.css('margin-right')) + parseFloat(this.css('margin-left'));\n                    else\n                        return this[0].offsetWidth;\n                }\n                else return null;\n            },\n            height: function () {\n                if (this[0] === window) {\n                    return window.innerHeight;\n                }\n                else {\n                    if (this.length > 0) {\n                        return parseFloat(this.css('height'));\n                    }\n                    else {\n                        return null;\n                    }\n                }\n            },\n            outerHeight: function (includeMargins) {\n                if (this.length > 0) {\n                    if (includeMargins)\n                        return this[0].offsetHeight + parseFloat(this.css('margin-top')) + parseFloat(this.css('margin-bottom'));\n                    else\n                        return this[0].offsetHeight;\n                }\n                else return null;\n            },\n            offset: function () {\n                if (this.length > 0) {\n                    var el = this[0];\n                    var box = el.getBoundingClientRect();\n                    var body = document.body;\n                    var clientTop  = el.clientTop  || body.clientTop  || 0;\n                    var clientLeft = el.clientLeft || body.clientLeft || 0;\n                    var scrollTop  = window.pageYOffset || el.scrollTop;\n                    var scrollLeft = window.pageXOffset || el.scrollLeft;\n                    return {\n                        top: box.top  + scrollTop  - clientTop,\n                        left: box.left + scrollLeft - clientLeft\n                    };\n                }\n                else {\n                    return null;\n                }\n            },\n            css: function (props, value) {\n                var i;\n                if (arguments.length === 1) {\n                    if (typeof props === 'string') {\n                        if (this[0]) return window.getComputedStyle(this[0], null).getPropertyValue(props);\n                    }\n                    else {\n                        for (i = 0; i < this.length; i++) {\n                            for (var prop in props) {\n                                this[i].style[prop] = props[prop];\n                            }\n                        }\n                        return this;\n                    }\n                }\n                if (arguments.length === 2 && typeof props === 'string') {\n                    for (i = 0; i < this.length; i++) {\n                        this[i].style[props] = value;\n                    }\n                    return this;\n                }\n                return this;\n            },\n            \n            //Dom manipulation\n            each: function (callback) {\n                for (var i = 0; i < this.length; i++) {\n                    callback.call(this[i], i, this[i]);\n                }\n                return this;\n            },\n            html: function (html) {\n                if (typeof html === 'undefined') {\n                    return this[0] ? this[0].innerHTML : undefined;\n                }\n                else {\n                    for (var i = 0; i < this.length; i++) {\n                        this[i].innerHTML = html;\n                    }\n                    return this;\n                }\n            },\n            is: function (selector) {\n                if (!this[0]) return false;\n                var compareWith, i;\n                if (typeof selector === 'string') {\n                    var el = this[0];\n                    if (el === document) return selector === document;\n                    if (el === window) return selector === window;\n    \n                    if (el.matches) return el.matches(selector);\n                    else if (el.webkitMatchesSelector) return el.webkitMatchesSelector(selector);\n                    else if (el.mozMatchesSelector) return el.mozMatchesSelector(selector);\n                    else if (el.msMatchesSelector) return el.msMatchesSelector(selector);\n                    else {\n                        compareWith = $(selector);\n                        for (i = 0; i < compareWith.length; i++) {\n                            if (compareWith[i] === this[0]) return true;\n                        }\n                        return false;\n                    }\n                }\n                else if (selector === document) return this[0] === document;\n                else if (selector === window) return this[0] === window;\n                else {\n                    if (selector.nodeType || selector instanceof Dom7) {\n                        compareWith = selector.nodeType ? [selector] : selector;\n                        for (i = 0; i < compareWith.length; i++) {\n                            if (compareWith[i] === this[0]) return true;\n                        }\n                        return false;\n                    }\n                    return false;\n                }\n                \n            },\n            index: function () {\n                if (this[0]) {\n                    var child = this[0];\n                    var i = 0;\n                    while ((child = child.previousSibling) !== null) {\n                        if (child.nodeType === 1) i++;\n                    }\n                    return i;\n                }\n                else return undefined;\n            },\n            eq: function (index) {\n                if (typeof index === 'undefined') return this;\n                var length = this.length;\n                var returnIndex;\n                if (index > length - 1) {\n                    return new Dom7([]);\n                }\n                if (index < 0) {\n                    returnIndex = length + index;\n                    if (returnIndex < 0) return new Dom7([]);\n                    else return new Dom7([this[returnIndex]]);\n                }\n                return new Dom7([this[index]]);\n            },\n            append: function (newChild) {\n                var i, j;\n                for (i = 0; i < this.length; i++) {\n                    if (typeof newChild === 'string') {\n                        var tempDiv = document.createElement('div');\n                        tempDiv.innerHTML = newChild;\n                        while (tempDiv.firstChild) {\n                            this[i].appendChild(tempDiv.firstChild);\n                        }\n                    }\n                    else if (newChild instanceof Dom7) {\n                        for (j = 0; j < newChild.length; j++) {\n                            this[i].appendChild(newChild[j]);\n                        }\n                    }\n                    else {\n                        this[i].appendChild(newChild);\n                    }\n                }\n                return this;\n            },\n            prepend: function (newChild) {\n                var i, j;\n                for (i = 0; i < this.length; i++) {\n                    if (typeof newChild === 'string') {\n                        var tempDiv = document.createElement('div');\n                        tempDiv.innerHTML = newChild;\n                        for (j = tempDiv.childNodes.length - 1; j >= 0; j--) {\n                            this[i].insertBefore(tempDiv.childNodes[j], this[i].childNodes[0]);\n                        }\n                        // this[i].insertAdjacentHTML('afterbegin', newChild);\n                    }\n                    else if (newChild instanceof Dom7) {\n                        for (j = 0; j < newChild.length; j++) {\n                            this[i].insertBefore(newChild[j], this[i].childNodes[0]);\n                        }\n                    }\n                    else {\n                        this[i].insertBefore(newChild, this[i].childNodes[0]);\n                    }\n                }\n                return this;\n            },\n            insertBefore: function (selector) {\n                var before = $(selector);\n                for (var i = 0; i < this.length; i++) {\n                    if (before.length === 1) {\n                        before[0].parentNode.insertBefore(this[i], before[0]);\n                    }\n                    else if (before.length > 1) {\n                        for (var j = 0; j < before.length; j++) {\n                            before[j].parentNode.insertBefore(this[i].cloneNode(true), before[j]);\n                        }\n                    }\n                }\n            },\n            insertAfter: function (selector) {\n                var after = $(selector);\n                for (var i = 0; i < this.length; i++) {\n                    if (after.length === 1) {\n                        after[0].parentNode.insertBefore(this[i], after[0].nextSibling);\n                    }\n                    else if (after.length > 1) {\n                        for (var j = 0; j < after.length; j++) {\n                            after[j].parentNode.insertBefore(this[i].cloneNode(true), after[j].nextSibling);\n                        }\n                    }\n                }\n            },\n            next: function (selector) {\n                if (this.length > 0) {\n                    if (selector) {\n                        if (this[0].nextElementSibling && $(this[0].nextElementSibling).is(selector)) return new Dom7([this[0].nextElementSibling]);\n                        else return new Dom7([]);\n                    }\n                    else {\n                        if (this[0].nextElementSibling) return new Dom7([this[0].nextElementSibling]);\n                        else return new Dom7([]);\n                    }\n                }\n                else return new Dom7([]);\n            },\n            nextAll: function (selector) {\n                var nextEls = [];\n                var el = this[0];\n                if (!el) return new Dom7([]);\n                while (el.nextElementSibling) {\n                    var next = el.nextElementSibling;\n                    if (selector) {\n                        if($(next).is(selector)) nextEls.push(next);\n                    }\n                    else nextEls.push(next);\n                    el = next;\n                }\n                return new Dom7(nextEls);\n            },\n            prev: function (selector) {\n                if (this.length > 0) {\n                    if (selector) {\n                        if (this[0].previousElementSibling && $(this[0].previousElementSibling).is(selector)) return new Dom7([this[0].previousElementSibling]);\n                        else return new Dom7([]);\n                    }\n                    else {\n                        if (this[0].previousElementSibling) return new Dom7([this[0].previousElementSibling]);\n                        else return new Dom7([]);\n                    }\n                }\n                else return new Dom7([]);\n            },\n            prevAll: function (selector) {\n                var prevEls = [];\n                var el = this[0];\n                if (!el) return new Dom7([]);\n                while (el.previousElementSibling) {\n                    var prev = el.previousElementSibling;\n                    if (selector) {\n                        if($(prev).is(selector)) prevEls.push(prev);\n                    }\n                    else prevEls.push(prev);\n                    el = prev;\n                }\n                return new Dom7(prevEls);\n            },\n            parent: function (selector) {\n                var parents = [];\n                for (var i = 0; i < this.length; i++) {\n                    if (selector) {\n                        if ($(this[i].parentNode).is(selector)) parents.push(this[i].parentNode);\n                    }\n                    else {\n                        parents.push(this[i].parentNode);\n                    }\n                }\n                return $($.unique(parents));\n            },\n            parents: function (selector) {\n                var parents = [];\n                for (var i = 0; i < this.length; i++) {\n                    var parent = this[i].parentNode;\n                    while (parent) {\n                        if (selector) {\n                            if ($(parent).is(selector)) parents.push(parent);\n                        }\n                        else {\n                            parents.push(parent);\n                        }\n                        parent = parent.parentNode;\n                    }\n                }\n                return $($.unique(parents));\n            },\n            find : function (selector) {\n                var foundElements = [];\n                for (var i = 0; i < this.length; i++) {\n                    var found = this[i].querySelectorAll(selector);\n                    for (var j = 0; j < found.length; j++) {\n                        foundElements.push(found[j]);\n                    }\n                }\n                return new Dom7(foundElements);\n            },\n            children: function (selector) {\n                var children = [];\n                for (var i = 0; i < this.length; i++) {\n                    var childNodes = this[i].childNodes;\n    \n                    for (var j = 0; j < childNodes.length; j++) {\n                        if (!selector) {\n                            if (childNodes[j].nodeType === 1) children.push(childNodes[j]);\n                        }\n                        else {\n                            if (childNodes[j].nodeType === 1 && $(childNodes[j]).is(selector)) children.push(childNodes[j]);\n                        }\n                    }\n                }\n                return new Dom7($.unique(children));\n            },\n            remove: function () {\n                for (var i = 0; i < this.length; i++) {\n                    if (this[i].parentNode) this[i].parentNode.removeChild(this[i]);\n                }\n                return this;\n            },\n            add: function () {\n                var dom = this;\n                var i, j;\n                for (i = 0; i < arguments.length; i++) {\n                    var toAdd = $(arguments[i]);\n                    for (j = 0; j < toAdd.length; j++) {\n                        dom[dom.length] = toAdd[j];\n                        dom.length++;\n                    }\n                }\n                return dom;\n            }\n        };\n        $.fn = Dom7.prototype;\n        $.unique = function (arr) {\n            var unique = [];\n            for (var i = 0; i < arr.length; i++) {\n                if (unique.indexOf(arr[i]) === -1) unique.push(arr[i]);\n            }\n            return unique;\n        };\n    \n        return $;\n    })();\n    \n\n    /*===========================\n    Add .swiper plugin from Dom libraries\n    ===========================*/\n    var swiperDomPlugins = ['jQuery', 'Zepto', 'Dom7'];\n    function addLibraryPlugin(lib) {\n        lib.fn.swiper = function (params) {\n            var firstInstance;\n            lib(this).each(function () {\n                var s = new Swiper(this, params);\n                if (!firstInstance) firstInstance = s;\n            });\n            return firstInstance;\n        };\n    }\n    for (var i = 0; i < swiperDomPlugins.length; i++) {\n        if (window[swiperDomPlugins[i]]) {\n            addLibraryPlugin(window[swiperDomPlugins[i]]);\n        }\n    }\n    // Required DOM Plugins\n    var domLib;\n    if (typeof Dom7 === 'undefined') {\n        domLib = window.Dom7 || window.Zepto || window.jQuery;\n    }\n    else {\n        domLib = Dom7;\n    }\n    if (domLib) {\n        if (!('transitionEnd' in domLib.fn)) {\n            domLib.fn.transitionEnd = function (callback) {\n                var events = ['webkitTransitionEnd', 'transitionend', 'oTransitionEnd', 'MSTransitionEnd', 'msTransitionEnd'],\n                    i, j, dom = this;\n                function fireCallBack(e) {\n                    /*jshint validthis:true */\n                    if (e.target !== this) return;\n                    callback.call(this, e);\n                    for (i = 0; i < events.length; i++) {\n                        dom.off(events[i], fireCallBack);\n                    }\n                }\n                if (callback) {\n                    for (i = 0; i < events.length; i++) {\n                        dom.on(events[i], fireCallBack);\n                    }\n                }\n                return this;\n            };\n        }\n        if (!('transform' in domLib.fn)) {\n            domLib.fn.transform = function (transform) {\n                for (var i = 0; i < this.length; i++) {\n                    var elStyle = this[i].style;\n                    elStyle.webkitTransform = elStyle.MsTransform = elStyle.msTransform = elStyle.MozTransform = elStyle.OTransform = elStyle.transform = transform;\n                }\n                return this;\n            };\n        }\n        if (!('transition' in domLib.fn)) {\n            domLib.fn.transition = function (duration) {\n                if (typeof duration !== 'string') {\n                    duration = duration + 'ms';\n                }\n                for (var i = 0; i < this.length; i++) {\n                    var elStyle = this[i].style;\n                    elStyle.webkitTransitionDuration = elStyle.MsTransitionDuration = elStyle.msTransitionDuration = elStyle.MozTransitionDuration = elStyle.OTransitionDuration = elStyle.transitionDuration = duration;\n                }\n                return this;\n            };\n        }\n    }\n        \n    \n\n})();\n/*===========================\nSwiper AMD Export\n===========================*/\nif (typeof(module) !== 'undefined')\n{\n    module.exports = Swiper;\n}\nelse if (typeof define === 'function' && define.amd) {\n    define([], function () {\n        'use strict';\n        return Swiper;\n    });\n}"], "sourceRoot": "/source/"}