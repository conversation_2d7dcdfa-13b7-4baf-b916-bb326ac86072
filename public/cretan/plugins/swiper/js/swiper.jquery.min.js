/**
 * Swiper 3.0.6
 * Most modern mobile touch slider and framework with hardware accelerated transitions
 * 
 * http://www.idangero.us/swiper/
 * 
 * Copyright 2015, <PERSON>
 * The iDangero.us
 * http://www.idangero.us/
 * 
 * Licensed under MIT
 * 
 * Released on: March 27, 2015
 */
!function(){"use strict";function e(e){e.fn.swiper=function(a){var t;return e(this).each(function(){var e=new Swiper(this,a);t||(t=e)}),t}}window.Swiper=function(e,a){function t(){return"horizontal"===f.params.direction}function s(){f.autoplayTimeoutId=setTimeout(function(){f.params.loop?(f.fixLoop(),f._slideNext()):f.isEnd?a.autoplayStopOnLast?f.stopAutoplay():f._slideTo(0):f._slideNext()},f.params.autoplay)}function r(e,a){var t=h(e.target);if(!t.is(a))if("string"==typeof a)t=t.parents(a);else if(a.nodeType){var s;return t.parents().each(function(e,t){t===a&&(s=a)}),s?a:void 0}return 0===t.length?void 0:t[0]}function i(e,a){a=a||{};var t=window.MutationObserver||window.WebkitMutationObserver,s=new t(function(e){e.forEach(function(e){f.onResize(),f.emit("onObserverUpdate",f,e)})});s.observe(e,{attributes:"undefined"==typeof a.attributes?!0:a.attributes,childList:"undefined"==typeof a.childList?!0:a.childList,characterData:"undefined"==typeof a.characterData?!0:a.characterData}),f.observers.push(s)}function n(e){e.originalEvent&&(e=e.originalEvent);var a=e.keyCode||e.charCode;if(!(e.shiftKey||e.altKey||e.ctrlKey||e.metaKey||document.activeElement&&document.activeElement.nodeName&&("input"===document.activeElement.nodeName.toLowerCase()||"textarea"===document.activeElement.nodeName.toLowerCase()))){if(37===a||39===a||38===a||40===a){var s=!1;if(f.container.parents(".swiper-slide").length>0&&0===f.container.parents(".swiper-slide-active").length)return;for(var r={left:window.pageXOffset,top:window.pageYOffset},i=window.innerWidth,n=window.innerHeight,o=f.container.offset(),l=[[o.left,o.top],[o.left+f.width,o.top],[o.left,o.top+f.height],[o.left+f.width,o.top+f.height]],p=0;p<l.length;p++){var d=l[p];d[0]>=r.left&&d[0]<=r.left+i&&d[1]>=r.top&&d[1]<=r.top+n&&(s=!0)}if(!s)return}t()?((37===a||39===a)&&(e.preventDefault?e.preventDefault():e.returnValue=!1),39===a&&f.slideNext(),37===a&&f.slidePrev()):((38===a||40===a)&&(e.preventDefault?e.preventDefault():e.returnValue=!1),40===a&&f.slideNext(),38===a&&f.slidePrev())}}function o(e){e.originalEvent&&(e=e.originalEvent);var a=f._wheelEvent,s=0;if(e.detail)s=-e.detail;else if("mousewheel"===a)if(f.params.mousewheelForceToAxis)if(t()){if(!(Math.abs(e.wheelDeltaX)>Math.abs(e.wheelDeltaY)))return;s=e.wheelDeltaX}else{if(!(Math.abs(e.wheelDeltaY)>Math.abs(e.wheelDeltaX)))return;s=e.wheelDeltaY}else s=e.wheelDelta;else if("DOMMouseScroll"===a)s=-e.detail;else if("wheel"===a)if(f.params.mousewheelForceToAxis)if(t()){if(!(Math.abs(e.deltaX)>Math.abs(e.deltaY)))return;s=-e.deltaX}else{if(!(Math.abs(e.deltaY)>Math.abs(e.deltaX)))return;s=-e.deltaY}else s=Math.abs(e.deltaX)>Math.abs(e.deltaY)?-e.deltaX:-e.deltaY;if(f.params.freeMode){var r=f.getWrapperTranslate()+s;if(r>0&&(r=0),r<f.maxTranslate()&&(r=f.maxTranslate()),f.setWrapperTransition(0),f.setWrapperTranslate(r),f.updateProgress(),f.updateActiveIndex(),0===r||r===f.maxTranslate())return}else(new Date).getTime()-f._lastWheelScrollTime>60&&(0>s?f.slideNext():f.slidePrev()),f._lastWheelScrollTime=(new Date).getTime();return f.params.autoplay&&f.stopAutoplay(),e.preventDefault?e.preventDefault():e.returnValue=!1,!1}function l(e,a){e=h(e);var s,r,i;s=e.attr("data-swiper-parallax")||"0",r=e.attr("data-swiper-parallax-x"),i=e.attr("data-swiper-parallax-y"),r||i?(r=r||"0",i=i||"0"):t()?(r=s,i="0"):(i=s,r="0"),r=r.indexOf("%")>=0?parseInt(r,10)*a+"%":r*a+"px",i=i.indexOf("%")>=0?parseInt(i,10)*a+"%":i*a+"px",e.transform("translate3d("+r+", "+i+",0px)")}function p(e){return 0!==e.indexOf("on")&&(e=e[0]!==e[0].toUpperCase()?"on"+e[0].toUpperCase()+e.substring(1):"on"+e),e}if(!(this instanceof Swiper))return new Swiper(e,a);var d={direction:"horizontal",touchEventsTarget:"container",initialSlide:0,speed:300,autoplay:!1,autoplayDisableOnInteraction:!0,freeMode:!1,freeModeMomentum:!0,freeModeMomentumRatio:1,freeModeMomentumBounce:!0,freeModeMomentumBounceRatio:1,setWrapperSize:!1,virtualTranslate:!1,effect:"slide",coverflow:{rotate:50,stretch:0,depth:100,modifier:1,slideShadows:!0},cube:{slideShadows:!0,shadow:!0,shadowOffset:20,shadowScale:.94},fade:{crossFade:!1},parallax:!1,scrollbar:null,scrollbarHide:!0,keyboardControl:!1,mousewheelControl:!1,mousewheelForceToAxis:!1,hashnav:!1,spaceBetween:0,slidesPerView:1,slidesPerColumn:1,slidesPerColumnFill:"column",slidesPerGroup:1,centeredSlides:!1,touchRatio:1,touchAngle:45,simulateTouch:!0,shortSwipes:!0,longSwipes:!0,longSwipesRatio:.5,longSwipesMs:300,followFinger:!0,onlyExternal:!1,threshold:0,touchMoveStopPropagation:!0,pagination:null,paginationClickable:!1,paginationHide:!1,paginationBulletRender:null,resistance:!0,resistanceRatio:.85,nextButton:null,prevButton:null,watchSlidesProgress:!1,watchSlidesVisibility:!1,grabCursor:!1,preventClicks:!0,preventClicksPropagation:!0,slideToClickedSlide:!1,lazyLoading:!1,lazyLoadingInPrevNext:!1,lazyLoadingOnTransitionStart:!1,preloadImages:!0,updateOnImagesReady:!0,loop:!1,loopAdditionalSlides:0,loopedSlides:null,control:void 0,controlInverse:!1,allowSwipeToPrev:!0,allowSwipeToNext:!0,swipeHandler:null,noSwiping:!0,noSwipingClass:"swiper-no-swiping",slideClass:"swiper-slide",slideActiveClass:"swiper-slide-active",slideVisibleClass:"swiper-slide-visible",slideDuplicateClass:"swiper-slide-duplicate",slideNextClass:"swiper-slide-next",slidePrevClass:"swiper-slide-prev",wrapperClass:"swiper-wrapper",bulletClass:"swiper-pagination-bullet",bulletActiveClass:"swiper-pagination-bullet-active",buttonDisabledClass:"swiper-button-disabled",paginationHiddenClass:"swiper-pagination-hidden",observer:!1,observeParents:!1,a11y:!1,prevSlideMessage:"Previous slide",nextSlideMessage:"Next slide",firstSlideMessage:"This is the first slide",lastSlideMessage:"This is the last slide",runCallbacksOnInit:!0},u=a&&a.virtualTranslate;a=a||{};for(var c in d)if("undefined"==typeof a[c])a[c]=d[c];else if("object"==typeof a[c])for(var m in d[c])"undefined"==typeof a[c][m]&&(a[c][m]=d[c][m]);var f=this;f.params=a,f.classNames=[];var h;if(h="undefined"==typeof Dom7?window.Dom7||window.Zepto||window.jQuery:Dom7,h&&(f.$=h,f.container=h(e),0!==f.container.length)){if(f.container.length>1)return void f.container.each(function(){new Swiper(this,a)});f.container[0].swiper=f,f.container.data("swiper",f),f.classNames.push("swiper-container-"+f.params.direction),f.params.freeMode&&f.classNames.push("swiper-container-free-mode"),f.support.flexbox||(f.classNames.push("swiper-container-no-flexbox"),f.params.slidesPerColumn=1),(f.params.parallax||f.params.watchSlidesVisibility)&&(f.params.watchSlidesProgress=!0),["cube","coverflow"].indexOf(f.params.effect)>=0&&(f.support.transforms3d?(f.params.watchSlidesProgress=!0,f.classNames.push("swiper-container-3d")):f.params.effect="slide"),"slide"!==f.params.effect&&f.classNames.push("swiper-container-"+f.params.effect),"cube"===f.params.effect&&(f.params.resistanceRatio=0,f.params.slidesPerView=1,f.params.slidesPerColumn=1,f.params.slidesPerGroup=1,f.params.centeredSlides=!1,f.params.spaceBetween=0,f.params.virtualTranslate=!0,f.params.setWrapperSize=!1),"fade"===f.params.effect&&(f.params.slidesPerView=1,f.params.slidesPerColumn=1,f.params.slidesPerGroup=1,f.params.watchSlidesProgress=!0,f.params.spaceBetween=0,"undefined"==typeof u&&(f.params.virtualTranslate=!0)),f.params.grabCursor&&f.support.touch&&(f.params.grabCursor=!1),f.wrapper=f.container.children("."+f.params.wrapperClass),f.params.pagination&&(f.paginationContainer=h(f.params.pagination),f.params.paginationClickable&&f.paginationContainer.addClass("swiper-pagination-clickable")),f.rtl=t()&&("rtl"===f.container[0].dir.toLowerCase()||"rtl"===f.container.css("direction")),f.rtl&&f.classNames.push("swiper-container-rtl"),f.rtl&&(f.wrongRTL="-webkit-box"===f.wrapper.css("display")),f.params.slidesPerColumn>1&&f.classNames.push("swiper-container-multirow"),f.device.android&&f.classNames.push("swiper-container-android"),f.container.addClass(f.classNames.join(" ")),f.translate=0,f.progress=0,f.velocity=0,f.lockSwipeToNext=function(){f.params.allowSwipeToNext=!1},f.lockSwipeToPrev=function(){f.params.allowSwipeToPrev=!1},f.lockSwipes=function(){f.params.allowSwipeToNext=f.params.allowSwipeToPrev=!1},f.unlockSwipeToNext=function(){f.params.allowSwipeToNext=!0},f.unlockSwipeToPrev=function(){f.params.allowSwipeToPrev=!0},f.unlockSwipes=function(){f.params.allowSwipeToNext=f.params.allowSwipeToPrev=!0},f.params.grabCursor&&(f.container[0].style.cursor="move",f.container[0].style.cursor="-webkit-grab",f.container[0].style.cursor="-moz-grab",f.container[0].style.cursor="grab"),f.imagesToLoad=[],f.imagesLoaded=0,f.loadImage=function(e,a,t,s){function r(){s&&s()}var i;e.complete&&t?r():a?(i=new Image,i.onload=r,i.onerror=r,i.src=a):r()},f.preloadImages=function(){function e(){"undefined"!=typeof f&&null!==f&&(void 0!==f.imagesLoaded&&f.imagesLoaded++,f.imagesLoaded===f.imagesToLoad.length&&(f.params.updateOnImagesReady&&f.update(),f.emit("onImagesReady",f)))}f.imagesToLoad=f.container.find("img");for(var a=0;a<f.imagesToLoad.length;a++)f.loadImage(f.imagesToLoad[a],f.imagesToLoad[a].currentSrc||f.imagesToLoad[a].getAttribute("src"),!0,e)},f.autoplayTimeoutId=void 0,f.autoplaying=!1,f.autoplayPaused=!1,f.startAutoplay=function(){return"undefined"!=typeof f.autoplayTimeoutId?!1:f.params.autoplay?f.autoplaying?!1:(f.autoplaying=!0,f.emit("onAutoplayStart",f),void s()):!1},f.stopAutoplay=function(){f.autoplayTimeoutId&&(f.autoplayTimeoutId&&clearTimeout(f.autoplayTimeoutId),f.autoplaying=!1,f.autoplayTimeoutId=void 0,f.emit("onAutoplayStop",f))},f.pauseAutoplay=function(e){f.autoplayPaused||(f.autoplayTimeoutId&&clearTimeout(f.autoplayTimeoutId),f.autoplayPaused=!0,0===e?(f.autoplayPaused=!1,s()):f.wrapper.transitionEnd(function(){f.autoplayPaused=!1,f.autoplaying?s():f.stopAutoplay()}))},f.minTranslate=function(){return-f.snapGrid[0]},f.maxTranslate=function(){return-f.snapGrid[f.snapGrid.length-1]},f.updateContainerSize=function(){f.width=f.container[0].clientWidth,f.height=f.container[0].clientHeight,f.size=t()?f.width:f.height},f.updateSlidesSize=function(){f.slides=f.wrapper.children("."+f.params.slideClass),f.snapGrid=[],f.slidesGrid=[],f.slidesSizesGrid=[];var e,a=f.params.spaceBetween,s=0,r=0,i=0;"string"==typeof a&&a.indexOf("%")>=0&&(a=parseFloat(a.replace("%",""))/100*f.size),f.virtualSize=-a,f.slides.css(f.rtl?{marginLeft:"",marginTop:""}:{marginRight:"",marginBottom:""});var n;f.params.slidesPerColumn>1&&(n=Math.floor(f.slides.length/f.params.slidesPerColumn)===f.slides.length/f.params.slidesPerColumn?f.slides.length:Math.ceil(f.slides.length/f.params.slidesPerColumn)*f.params.slidesPerColumn);var o;for(e=0;e<f.slides.length;e++){o=0;var l=f.slides.eq(e);if(f.params.slidesPerColumn>1){var p,d,u,c,m=f.params.slidesPerColumn;"column"===f.params.slidesPerColumnFill?(d=Math.floor(e/m),u=e-d*m,p=d+u*n/m,l.css({"-webkit-box-ordinal-group":p,"-moz-box-ordinal-group":p,"-ms-flex-order":p,"-webkit-order":p,order:p})):(c=n/m,u=Math.floor(e/c),d=e-u*c),l.css({"margin-top":0!==u&&f.params.spaceBetween&&f.params.spaceBetween+"px"}).attr("data-swiper-column",d).attr("data-swiper-row",u)}"none"!==l.css("display")&&("auto"===f.params.slidesPerView?o=t()?l.outerWidth(!0):l.outerHeight(!0):(o=(f.size-(f.params.slidesPerView-1)*a)/f.params.slidesPerView,t()?f.slides[e].style.width=o+"px":f.slides[e].style.height=o+"px"),f.slides[e].swiperSlideSize=o,f.slidesSizesGrid.push(o),f.params.centeredSlides?(s=s+o/2+r/2+a,0===e&&(s=s-f.size/2-a),Math.abs(s)<.001&&(s=0),i%f.params.slidesPerGroup===0&&f.snapGrid.push(s),f.slidesGrid.push(s)):(i%f.params.slidesPerGroup===0&&f.snapGrid.push(s),f.slidesGrid.push(s),s=s+o+a),f.virtualSize+=o+a,r=o,i++)}f.virtualSize=Math.max(f.virtualSize,f.size);var h;if(f.rtl&&f.wrongRTL&&("slide"===f.params.effect||"coverflow"===f.params.effect)&&f.wrapper.css({width:f.virtualSize+f.params.spaceBetween+"px"}),(!f.support.flexbox||f.params.setWrapperSize)&&f.wrapper.css(t()?{width:f.virtualSize+f.params.spaceBetween+"px"}:{height:f.virtualSize+f.params.spaceBetween+"px"}),f.params.slidesPerColumn>1&&(f.virtualSize=(o+f.params.spaceBetween)*n,f.virtualSize=Math.ceil(f.virtualSize/f.params.slidesPerColumn)-f.params.spaceBetween,f.wrapper.css({width:f.virtualSize+f.params.spaceBetween+"px"}),f.params.centeredSlides)){for(h=[],e=0;e<f.snapGrid.length;e++)f.snapGrid[e]<f.virtualSize+f.snapGrid[0]&&h.push(f.snapGrid[e]);f.snapGrid=h}if(!f.params.centeredSlides){for(h=[],e=0;e<f.snapGrid.length;e++)f.snapGrid[e]<=f.virtualSize-f.size&&h.push(f.snapGrid[e]);f.snapGrid=h,Math.floor(f.virtualSize-f.size)>Math.floor(f.snapGrid[f.snapGrid.length-1])&&f.snapGrid.push(f.virtualSize-f.size)}0===f.snapGrid.length&&(f.snapGrid=[0]),0!==f.params.spaceBetween&&f.slides.css(t()?f.rtl?{marginLeft:a+"px"}:{marginRight:a+"px"}:{marginBottom:a+"px"}),f.params.watchSlidesProgress&&f.updateSlidesOffset()},f.updateSlidesOffset=function(){for(var e=0;e<f.slides.length;e++)f.slides[e].swiperSlideOffset=t()?f.slides[e].offsetLeft:f.slides[e].offsetTop},f.updateSlidesProgress=function(e){if("undefined"==typeof e&&(e=f.translate||0),0!==f.slides.length){"undefined"==typeof f.slides[0].swiperSlideOffset&&f.updateSlidesOffset();var a=f.params.centeredSlides?-e+f.size/2:-e;f.rtl&&(a=f.params.centeredSlides?e-f.size/2:e);{f.container[0].getBoundingClientRect(),t()?"left":"top",t()?"right":"bottom"}f.slides.removeClass(f.params.slideVisibleClass);for(var s=0;s<f.slides.length;s++){var r=f.slides[s],i=f.params.centeredSlides===!0?r.swiperSlideSize/2:0,n=(a-r.swiperSlideOffset-i)/(r.swiperSlideSize+f.params.spaceBetween);if(f.params.watchSlidesVisibility){var o=-(a-r.swiperSlideOffset-i),l=o+f.slidesSizesGrid[s],p=o>=0&&o<f.size||l>0&&l<=f.size||0>=o&&l>=f.size;p&&f.slides.eq(s).addClass(f.params.slideVisibleClass)}r.progress=f.rtl?-n:n}}},f.updateProgress=function(e){"undefined"==typeof e&&(e=f.translate||0);var a=f.maxTranslate()-f.minTranslate();0===a?(f.progress=0,f.isBeginning=f.isEnd=!0):(f.progress=(e-f.minTranslate())/a,f.isBeginning=f.progress<=0,f.isEnd=f.progress>=1),f.isBeginning&&f.emit("onReachBeginning",f),f.isEnd&&f.emit("onReachEnd",f),f.params.watchSlidesProgress&&f.updateSlidesProgress(e),f.emit("onProgress",f,f.progress)},f.updateActiveIndex=function(){var e,a,t,s=f.rtl?f.translate:-f.translate;for(a=0;a<f.slidesGrid.length;a++)"undefined"!=typeof f.slidesGrid[a+1]?s>=f.slidesGrid[a]&&s<f.slidesGrid[a+1]-(f.slidesGrid[a+1]-f.slidesGrid[a])/2?e=a:s>=f.slidesGrid[a]&&s<f.slidesGrid[a+1]&&(e=a+1):s>=f.slidesGrid[a]&&(e=a);(0>e||"undefined"==typeof e)&&(e=0),t=Math.floor(e/f.params.slidesPerGroup),t>=f.snapGrid.length&&(t=f.snapGrid.length-1),e!==f.activeIndex&&(f.snapIndex=t,f.previousIndex=f.activeIndex,f.activeIndex=e,f.updateClasses())},f.updateClasses=function(){f.slides.removeClass(f.params.slideActiveClass+" "+f.params.slideNextClass+" "+f.params.slidePrevClass);var e=f.slides.eq(f.activeIndex);if(e.addClass(f.params.slideActiveClass),e.next("."+f.params.slideClass).addClass(f.params.slideNextClass),e.prev("."+f.params.slideClass).addClass(f.params.slidePrevClass),f.bullets&&f.bullets.length>0){f.bullets.removeClass(f.params.bulletActiveClass);var a;f.params.loop?(a=Math.ceil(f.activeIndex-f.loopedSlides)/f.params.slidesPerGroup,a>f.slides.length-1-2*f.loopedSlides&&(a-=f.slides.length-2*f.loopedSlides),a>f.bullets.length-1&&(a-=f.bullets.length)):a="undefined"!=typeof f.snapIndex?f.snapIndex:f.activeIndex||0,f.paginationContainer.length>1?f.bullets.each(function(){h(this).index()===a&&h(this).addClass(f.params.bulletActiveClass)}):f.bullets.eq(a).addClass(f.params.bulletActiveClass)}f.params.loop||(f.params.prevButton&&(f.isBeginning?(h(f.params.prevButton).addClass(f.params.buttonDisabledClass),f.params.a11y&&f.a11y&&f.a11y.disable(h(f.params.prevButton))):(h(f.params.prevButton).removeClass(f.params.buttonDisabledClass),f.params.a11y&&f.a11y&&f.a11y.enable(h(f.params.prevButton)))),f.params.nextButton&&(f.isEnd?(h(f.params.nextButton).addClass(f.params.buttonDisabledClass),f.params.a11y&&f.a11y&&f.a11y.disable(h(f.params.nextButton))):(h(f.params.nextButton).removeClass(f.params.buttonDisabledClass),f.params.a11y&&f.a11y&&f.a11y.enable(h(f.params.nextButton)))))},f.updatePagination=function(){if(f.params.pagination&&f.paginationContainer&&f.paginationContainer.length>0){for(var e="",a=f.params.loop?Math.ceil((f.slides.length-2*f.loopedSlides)/f.params.slidesPerGroup):f.snapGrid.length,t=0;a>t;t++)e+=f.params.paginationBulletRender?f.params.paginationBulletRender(t,f.params.bulletClass):'<span class="'+f.params.bulletClass+'"></span>';f.paginationContainer.html(e),f.bullets=f.paginationContainer.find("."+f.params.bulletClass)}},f.update=function(e){function a(){s=Math.min(Math.max(f.translate,f.maxTranslate()),f.minTranslate()),f.setWrapperTranslate(s),f.updateActiveIndex(),f.updateClasses()}if(f.updateContainerSize(),f.updateSlidesSize(),f.updateProgress(),f.updatePagination(),f.updateClasses(),f.params.scrollbar&&f.scrollbar&&f.scrollbar.set(),e){var t,s;f.params.freeMode?a():(t="auto"===f.params.slidesPerView&&f.isEnd&&!f.params.centeredSlides?f.slideTo(f.slides.length-1,0,!1,!0):f.slideTo(f.activeIndex,0,!1,!0),t||a())}},f.onResize=function(){if(f.updateContainerSize(),f.updateSlidesSize(),f.updateProgress(),("auto"===f.params.slidesPerView||f.params.freeMode)&&f.updatePagination(),f.params.scrollbar&&f.scrollbar&&f.scrollbar.set(),f.params.freeMode){var e=Math.min(Math.max(f.translate,f.maxTranslate()),f.minTranslate());f.setWrapperTranslate(e),f.updateActiveIndex(),f.updateClasses()}else f.updateClasses(),"auto"===f.params.slidesPerView&&f.isEnd&&!f.params.centeredSlides?f.slideTo(f.slides.length-1,0,!1,!0):f.slideTo(f.activeIndex,0,!1,!0)};var v=["mousedown","mousemove","mouseup"];window.navigator.pointerEnabled?v=["pointerdown","pointermove","pointerup"]:window.navigator.msPointerEnabled&&(v=["MSPointerDown","MSPointerMove","MSPointerUp"]),f.touchEvents={start:f.support.touch||!f.params.simulateTouch?"touchstart":v[0],move:f.support.touch||!f.params.simulateTouch?"touchmove":v[1],end:f.support.touch||!f.params.simulateTouch?"touchend":v[2]},(window.navigator.pointerEnabled||window.navigator.msPointerEnabled)&&("container"===f.params.touchEventsTarget?f.container:f.wrapper).addClass("swiper-wp8-"+f.params.direction),f.initEvents=function(e){var t=e?"off":"on",s=e?"removeEventListener":"addEventListener",r="container"===f.params.touchEventsTarget?f.container[0]:f.wrapper[0],i=f.support.touch?r:document,n=f.params.nested?!0:!1;f.browser.ie?(r[s](f.touchEvents.start,f.onTouchStart,!1),i[s](f.touchEvents.move,f.onTouchMove,n),i[s](f.touchEvents.end,f.onTouchEnd,!1)):(f.support.touch&&(r[s](f.touchEvents.start,f.onTouchStart,!1),r[s](f.touchEvents.move,f.onTouchMove,n),r[s](f.touchEvents.end,f.onTouchEnd,!1)),!a.simulateTouch||f.device.ios||f.device.android||(r[s]("mousedown",f.onTouchStart,!1),i[s]("mousemove",f.onTouchMove,n),i[s]("mouseup",f.onTouchEnd,!1))),window[s]("resize",f.onResize),f.params.nextButton&&(h(f.params.nextButton)[t]("click",f.onClickNext),f.params.a11y&&f.a11y&&h(f.params.nextButton)[t]("keydown",f.a11y.onEnterKey)),f.params.prevButton&&(h(f.params.prevButton)[t]("click",f.onClickPrev),f.params.a11y&&f.a11y&&h(f.params.prevButton)[t]("keydown",f.a11y.onEnterKey)),f.params.pagination&&f.params.paginationClickable&&h(f.paginationContainer)[t]("click","."+f.params.bulletClass,f.onClickIndex),(f.params.preventClicks||f.params.preventClicksPropagation)&&r[s]("click",f.preventClicks,!0)},f.attachEvents=function(){f.initEvents()},f.detachEvents=function(){f.initEvents(!0)},f.allowClick=!0,f.preventClicks=function(e){f.allowClick||(f.params.preventClicks&&e.preventDefault(),f.params.preventClicksPropagation&&(e.stopPropagation(),e.stopImmediatePropagation()))},f.onClickNext=function(e){e.preventDefault(),f.slideNext()},f.onClickPrev=function(e){e.preventDefault(),f.slidePrev()},f.onClickIndex=function(e){e.preventDefault();var a=h(this).index()*f.params.slidesPerGroup;f.params.loop&&(a+=f.loopedSlides),f.slideTo(a)},f.updateClickedSlide=function(e){var a=r(e,"."+f.params.slideClass);if(!a)return f.clickedSlide=void 0,void(f.clickedIndex=void 0);if(f.clickedSlide=a,f.clickedIndex=h(a).index(),f.params.slideToClickedSlide&&void 0!==f.clickedIndex&&f.clickedIndex!==f.activeIndex){var t,s=f.clickedIndex;if(f.params.loop)if(t=h(f.clickedSlide).attr("data-swiper-slide-index"),s>f.slides.length-f.params.slidesPerView)f.fixLoop(),s=f.wrapper.children("."+f.params.slideClass+'[data-swiper-slide-index="'+t+'"]').eq(0).index(),setTimeout(function(){f.slideTo(s)},0);else if(s<f.params.slidesPerView-1){f.fixLoop();var i=f.wrapper.children("."+f.params.slideClass+'[data-swiper-slide-index="'+t+'"]');s=i.eq(i.length-1).index(),setTimeout(function(){f.slideTo(s)},0)}else f.slideTo(s);else f.slideTo(s)}};var g,w,b,y,x,T,S,C,M,P="input, select, textarea, button",z=Date.now(),I=[];f.animating=!1,f.touches={startX:0,startY:0,currentX:0,currentY:0,diff:0};var E,k;if(f.onTouchStart=function(e){if(e.originalEvent&&(e=e.originalEvent),E="touchstart"===e.type,E||!("which"in e)||3!==e.which){if(f.params.noSwiping&&r(e,"."+f.params.noSwipingClass))return void(f.allowClick=!0);if(!f.params.swipeHandler||r(e,f.params.swipeHandler)){if(g=!0,w=!1,y=void 0,k=void 0,f.touches.startX=f.touches.currentX="touchstart"===e.type?e.targetTouches[0].pageX:e.pageX,f.touches.startY=f.touches.currentY="touchstart"===e.type?e.targetTouches[0].pageY:e.pageY,b=Date.now(),f.allowClick=!0,f.updateContainerSize(),f.swipeDirection=void 0,f.params.threshold>0&&(S=!1),"touchstart"!==e.type){var a=!0;h(e.target).is(P)&&(a=!1),document.activeElement&&h(document.activeElement).is(P)&&document.activeElement.blur(),a&&e.preventDefault()}f.emit("onTouchStart",f,e)}}},f.onTouchMove=function(e){if(e.originalEvent&&(e=e.originalEvent),!(E&&"mousemove"===e.type||e.preventedByNestedSwiper)){if(f.params.onlyExternal)return w=!0,void(f.allowClick=!1);if(E&&document.activeElement&&e.target===document.activeElement&&h(e.target).is(P))return w=!0,void(f.allowClick=!1);if(f.emit("onTouchMove",f,e),!(e.targetTouches&&e.targetTouches.length>1)){if(f.touches.currentX="touchmove"===e.type?e.targetTouches[0].pageX:e.pageX,f.touches.currentY="touchmove"===e.type?e.targetTouches[0].pageY:e.pageY,"undefined"==typeof y){var s=180*Math.atan2(Math.abs(f.touches.currentY-f.touches.startY),Math.abs(f.touches.currentX-f.touches.startX))/Math.PI;y=t()?s>f.params.touchAngle:90-s>f.params.touchAngle}if(y&&f.emit("onTouchMoveOpposite",f,e),"undefined"==typeof k&&f.browser.ieTouch&&(f.touches.currentX!==f.touches.startX||f.touches.currentY!==f.touches.startY)&&(k=!0),g){if(y)return void(g=!1);if(k||!f.browser.ieTouch){f.allowClick=!1,f.emit("onSliderMove",f,e),e.preventDefault(),f.params.touchMoveStopPropagation&&!f.params.nested&&e.stopPropagation(),w||(a.loop&&f.fixLoop(),T=f.getWrapperTranslate(),f.setWrapperTransition(0),f.animating&&f.wrapper.trigger("webkitTransitionEnd transitionend oTransitionEnd MSTransitionEnd msTransitionEnd"),f.params.autoplay&&f.autoplaying&&(f.params.autoplayDisableOnInteraction?f.stopAutoplay():f.pauseAutoplay()),M=!1,f.params.grabCursor&&(f.container[0].style.cursor="move",f.container[0].style.cursor="-webkit-grabbing",f.container[0].style.cursor="-moz-grabbin",f.container[0].style.cursor="grabbing")),w=!0;var r=f.touches.diff=t()?f.touches.currentX-f.touches.startX:f.touches.currentY-f.touches.startY;r*=f.params.touchRatio,f.rtl&&(r=-r),f.swipeDirection=r>0?"prev":"next",x=r+T;var i=!0;if(r>0&&x>f.minTranslate()?(i=!1,f.params.resistance&&(x=f.minTranslate()-1+Math.pow(-f.minTranslate()+T+r,f.params.resistanceRatio))):0>r&&x<f.maxTranslate()&&(i=!1,f.params.resistance&&(x=f.maxTranslate()+1-Math.pow(f.maxTranslate()-T-r,f.params.resistanceRatio))),i&&(e.preventedByNestedSwiper=!0),!f.params.allowSwipeToNext&&"next"===f.swipeDirection&&T>x&&(x=T),!f.params.allowSwipeToPrev&&"prev"===f.swipeDirection&&x>T&&(x=T),f.params.followFinger){if(f.params.threshold>0){if(!(Math.abs(r)>f.params.threshold||S))return void(x=T);if(!S)return S=!0,f.touches.startX=f.touches.currentX,f.touches.startY=f.touches.currentY,x=T,void(f.touches.diff=t()?f.touches.currentX-f.touches.startX:f.touches.currentY-f.touches.startY)}(f.params.freeMode||f.params.watchSlidesProgress)&&f.updateActiveIndex(),f.params.freeMode&&(0===I.length&&I.push({position:f.touches[t()?"startX":"startY"],time:b}),I.push({position:f.touches[t()?"currentX":"currentY"],time:(new Date).getTime()})),f.updateProgress(x),f.setWrapperTranslate(x)}}}}}},f.onTouchEnd=function(e){if(e.originalEvent&&(e=e.originalEvent),f.emit("onTouchEnd",f,e),g){f.params.grabCursor&&w&&g&&(f.container[0].style.cursor="move",f.container[0].style.cursor="-webkit-grab",f.container[0].style.cursor="-moz-grab",f.container[0].style.cursor="grab");var a=Date.now(),t=a-b;if(f.allowClick&&(f.updateClickedSlide(e),f.emit("onTap",f,e),300>t&&a-z>300&&(C&&clearTimeout(C),C=setTimeout(function(){f&&(f.params.paginationHide&&f.paginationContainer.length>0&&!h(e.target).hasClass(f.params.bulletClass)&&f.paginationContainer.toggleClass(f.params.paginationHiddenClass),f.emit("onClick",f,e))},300)),300>t&&300>a-z&&(C&&clearTimeout(C),f.emit("onDoubleTap",f,e))),z=Date.now(),setTimeout(function(){f&&f.allowClick&&(f.allowClick=!0)},0),!g||!w||!f.swipeDirection||0===f.touches.diff||x===T)return void(g=w=!1);g=w=!1;var s;if(s=f.params.followFinger?f.rtl?f.translate:-f.translate:-x,f.params.freeMode){if(s<-f.minTranslate())return void f.slideTo(f.activeIndex);if(s>-f.maxTranslate())return void f.slideTo(f.slides.length-1);if(f.params.freeModeMomentum){if(I.length>1){var r=I.pop(),i=I.pop(),n=r.position-i.position,o=r.time-i.time;f.velocity=n/o,f.velocity=f.velocity/2,Math.abs(f.velocity)<.02&&(f.velocity=0),(o>150||(new Date).getTime()-r.time>300)&&(f.velocity=0)}else f.velocity=0;I.length=0;var l=1e3*f.params.freeModeMomentumRatio,p=f.velocity*l,d=f.translate+p;f.rtl&&(d=-d);var u,c=!1,m=20*Math.abs(f.velocity)*f.params.freeModeMomentumBounceRatio;d<f.maxTranslate()&&(f.params.freeModeMomentumBounce?(d+f.maxTranslate()<-m&&(d=f.maxTranslate()-m),u=f.maxTranslate(),c=!0,M=!0):d=f.maxTranslate()),d>f.minTranslate()&&(f.params.freeModeMomentumBounce?(d-f.minTranslate()>m&&(d=f.minTranslate()+m),u=f.minTranslate(),c=!0,M=!0):d=f.minTranslate()),0!==f.velocity&&(l=Math.abs(f.rtl?(-d-f.translate)/f.velocity:(d-f.translate)/f.velocity)),f.params.freeModeMomentumBounce&&c?(f.updateProgress(u),f.setWrapperTransition(l),f.setWrapperTranslate(d),f.onTransitionStart(),f.animating=!0,f.wrapper.transitionEnd(function(){M&&(f.emit("onMomentumBounce",f),f.setWrapperTransition(f.params.speed),f.setWrapperTranslate(u),f.wrapper.transitionEnd(function(){f.onTransitionEnd()}))})):f.velocity?(f.updateProgress(d),f.setWrapperTransition(l),f.setWrapperTranslate(d),f.onTransitionStart(),f.animating||(f.animating=!0,f.wrapper.transitionEnd(function(){f.onTransitionEnd()}))):f.updateProgress(d),f.updateActiveIndex()}return void((!f.params.freeModeMomentum||t>=f.params.longSwipesMs)&&(f.updateProgress(),f.updateActiveIndex()))}var v,y=0,S=f.slidesSizesGrid[0];for(v=0;v<f.slidesGrid.length;v+=f.params.slidesPerGroup)"undefined"!=typeof f.slidesGrid[v+f.params.slidesPerGroup]?s>=f.slidesGrid[v]&&s<f.slidesGrid[v+f.params.slidesPerGroup]&&(y=v,S=f.slidesGrid[v+f.params.slidesPerGroup]-f.slidesGrid[v]):s>=f.slidesGrid[v]&&(y=v,S=f.slidesGrid[f.slidesGrid.length-1]-f.slidesGrid[f.slidesGrid.length-2]);var P=(s-f.slidesGrid[y])/S;if(t>f.params.longSwipesMs){if(!f.params.longSwipes)return void f.slideTo(f.activeIndex);"next"===f.swipeDirection&&f.slideTo(P>=f.params.longSwipesRatio?y+f.params.slidesPerGroup:y),"prev"===f.swipeDirection&&f.slideTo(P>1-f.params.longSwipesRatio?y+f.params.slidesPerGroup:y)}else{if(!f.params.shortSwipes)return void f.slideTo(f.activeIndex);"next"===f.swipeDirection&&f.slideTo(y+f.params.slidesPerGroup),"prev"===f.swipeDirection&&f.slideTo(y)}}},f._slideTo=function(e,a){return f.slideTo(e,a,!0,!0)},f.slideTo=function(e,a,s,r){"undefined"==typeof s&&(s=!0),"undefined"==typeof e&&(e=0),0>e&&(e=0),f.snapIndex=Math.floor(e/f.params.slidesPerGroup),f.snapIndex>=f.snapGrid.length&&(f.snapIndex=f.snapGrid.length-1);var i=-f.snapGrid[f.snapIndex];f.params.autoplay&&f.autoplaying&&(r||!f.params.autoplayDisableOnInteraction?f.pauseAutoplay(a):f.stopAutoplay()),f.updateProgress(i);for(var n=0;n<f.slidesGrid.length;n++)-i>=f.slidesGrid[n]&&(e=n);if("undefined"==typeof a&&(a=f.params.speed),f.previousIndex=f.activeIndex||0,f.activeIndex=e,i===f.translate)return f.updateClasses(),!1;f.onTransitionStart(s);t()?i:0,t()?0:i;return 0===a?(f.setWrapperTransition(0),f.setWrapperTranslate(i),f.onTransitionEnd(s)):(f.setWrapperTransition(a),f.setWrapperTranslate(i),f.animating||(f.animating=!0,f.wrapper.transitionEnd(function(){f.onTransitionEnd(s)}))),f.updateClasses(),!0},f.onTransitionStart=function(e){"undefined"==typeof e&&(e=!0),f.lazy&&f.lazy.onTransitionStart(),e&&(f.emit("onTransitionStart",f),f.activeIndex!==f.previousIndex&&f.emit("onSlideChangeStart",f))},f.onTransitionEnd=function(e){f.animating=!1,f.setWrapperTransition(0),"undefined"==typeof e&&(e=!0),f.lazy&&f.lazy.onTransitionEnd(),e&&(f.emit("onTransitionEnd",f),f.activeIndex!==f.previousIndex&&f.emit("onSlideChangeEnd",f)),f.params.hashnav&&f.hashnav&&f.hashnav.setHash()},f.slideNext=function(e,a,t){if(f.params.loop){if(f.animating)return!1;f.fixLoop();{f.container[0].clientLeft}return f.slideTo(f.activeIndex+f.params.slidesPerGroup,a,e,t)}return f.slideTo(f.activeIndex+f.params.slidesPerGroup,a,e,t)},f._slideNext=function(e){return f.slideNext(!0,e,!0)},f.slidePrev=function(e,a,t){if(f.params.loop){if(f.animating)return!1;f.fixLoop();{f.container[0].clientLeft}return f.slideTo(f.activeIndex-1,a,e,t)}return f.slideTo(f.activeIndex-1,a,e,t)},f._slidePrev=function(e){return f.slidePrev(!0,e,!0)},f.slideReset=function(e,a){return f.slideTo(f.activeIndex,a,e)},f.setWrapperTransition=function(e,a){f.wrapper.transition(e),"slide"!==f.params.effect&&f.effects[f.params.effect]&&f.effects[f.params.effect].setTransition(e),f.params.parallax&&f.parallax&&f.parallax.setTransition(e),f.params.scrollbar&&f.scrollbar&&f.scrollbar.setTransition(e),f.params.control&&f.controller&&f.controller.setTransition(e,a),f.emit("onSetTransition",f,e)},f.setWrapperTranslate=function(e,a,s){var r=0,i=0,n=0;t()?r=f.rtl?-e:e:i=e,f.params.virtualTranslate||f.wrapper.transform(f.support.transforms3d?"translate3d("+r+"px, "+i+"px, "+n+"px)":"translate("+r+"px, "+i+"px)"),f.translate=t()?r:i,a&&f.updateActiveIndex(),"slide"!==f.params.effect&&f.effects[f.params.effect]&&f.effects[f.params.effect].setTranslate(f.translate),f.params.parallax&&f.parallax&&f.parallax.setTranslate(f.translate),f.params.scrollbar&&f.scrollbar&&f.scrollbar.setTranslate(f.translate),f.params.control&&f.controller&&f.controller.setTranslate(f.translate,s),f.emit("onSetTranslate",f,f.translate)},f.getTranslate=function(e,a){var t,s,r,i;return"undefined"==typeof a&&(a="x"),f.params.virtualTranslate?f.rtl?-f.translate:f.translate:(r=window.getComputedStyle(e,null),window.WebKitCSSMatrix?i=new WebKitCSSMatrix("none"===r.webkitTransform?"":r.webkitTransform):(i=r.MozTransform||r.OTransform||r.MsTransform||r.msTransform||r.transform||r.getPropertyValue("transform").replace("translate(","matrix(1, 0, 0, 1,"),t=i.toString().split(",")),"x"===a&&(s=window.WebKitCSSMatrix?i.m41:parseFloat(16===t.length?t[12]:t[4])),"y"===a&&(s=window.WebKitCSSMatrix?i.m42:parseFloat(16===t.length?t[13]:t[5])),f.rtl&&s&&(s=-s),s||0)},f.getWrapperTranslate=function(e){return"undefined"==typeof e&&(e=t()?"x":"y"),f.getTranslate(f.wrapper[0],e)},f.observers=[],f.initObservers=function(){if(f.params.observeParents)for(var e=f.container.parents(),a=0;a<e.length;a++)i(e[a]);i(f.container[0],{childList:!1}),i(f.wrapper[0],{attributes:!1})},f.disconnectObservers=function(){for(var e=0;e<f.observers.length;e++)f.observers[e].disconnect();f.observers=[]},f.createLoop=function(){f.wrapper.children("."+f.params.slideClass+"."+f.params.slideDuplicateClass).remove();
var e=f.wrapper.children("."+f.params.slideClass);f.loopedSlides=parseInt(f.params.loopedSlides||f.params.slidesPerView,10),f.loopedSlides=f.loopedSlides+f.params.loopAdditionalSlides,f.loopedSlides>e.length&&(f.loopedSlides=e.length);var a,t=[],s=[];for(e.each(function(a,r){var i=h(this);a<f.loopedSlides&&s.push(r),a<e.length&&a>=e.length-f.loopedSlides&&t.push(r),i.attr("data-swiper-slide-index",a)}),a=0;a<s.length;a++)f.wrapper.append(h(s[a].cloneNode(!0)).addClass(f.params.slideDuplicateClass));for(a=t.length-1;a>=0;a--)f.wrapper.prepend(h(t[a].cloneNode(!0)).addClass(f.params.slideDuplicateClass))},f.destroyLoop=function(){f.wrapper.children("."+f.params.slideClass+"."+f.params.slideDuplicateClass).remove(),f.slides.removeAttr("data-swiper-slide-index")},f.fixLoop=function(){var e;f.activeIndex<f.loopedSlides?(e=f.slides.length-3*f.loopedSlides+f.activeIndex,e+=f.loopedSlides,f.slideTo(e,0,!1,!0)):("auto"===f.params.slidesPerView&&f.activeIndex>=2*f.loopedSlides||f.activeIndex>f.slides.length-2*f.params.slidesPerView)&&(e=-f.slides.length+f.activeIndex+f.loopedSlides,e+=f.loopedSlides,f.slideTo(e,0,!1,!0))},f.appendSlide=function(e){if(f.params.loop&&f.destroyLoop(),"object"==typeof e&&e.length)for(var a=0;a<e.length;a++)e[a]&&f.wrapper.append(e[a]);else f.wrapper.append(e);f.params.loop&&f.createLoop(),f.params.observer&&f.support.observer||f.update(!0)},f.prependSlide=function(e){f.params.loop&&f.destroyLoop();var a=f.activeIndex+1;if("object"==typeof e&&e.length){for(var t=0;t<e.length;t++)e[t]&&f.wrapper.prepend(e[t]);a=f.activeIndex+e.length}else f.wrapper.prepend(e);f.params.loop&&f.createLoop(),f.params.observer&&f.support.observer||f.update(!0),f.slideTo(a,0,!1)},f.removeSlide=function(e){f.params.loop&&f.destroyLoop();var a,t=f.activeIndex;if("object"==typeof e&&e.length){for(var s=0;s<e.length;s++)a=e[s],f.slides[a]&&f.slides.eq(a).remove(),t>a&&t--;t=Math.max(t,0)}else a=e,f.slides[a]&&f.slides.eq(a).remove(),t>a&&t--,t=Math.max(t,0);f.params.observer&&f.support.observer||f.update(!0),f.slideTo(t,0,!1)},f.removeAllSlides=function(){for(var e=[],a=0;a<f.slides.length;a++)e.push(a);f.removeSlide(e)},f.effects={fade:{fadeIndex:null,setTranslate:function(){for(var e=0;e<f.slides.length;e++){var a=f.slides.eq(e),s=a[0].swiperSlideOffset,r=-s;f.params.virtualTranslate||(r-=f.translate);var i=0;t()||(i=r,r=0);var n=f.params.fade.crossFade?Math.max(1-Math.abs(a[0].progress),0):1+Math.min(Math.max(a[0].progress,-1),0);n>0&&1>n&&(f.effects.fade.fadeIndex=e),a.css({opacity:n}).transform("translate3d("+r+"px, "+i+"px, 0px)")}},setTransition:function(e){if(f.slides.transition(e),f.params.virtualTranslate&&0!==e){var a=null!==f.effects.fade.fadeIndex?f.effects.fade.fadeIndex:f.activeIndex;f.slides.eq(a).transitionEnd(function(){for(var e=["webkitTransitionEnd","transitionend","oTransitionEnd","MSTransitionEnd","msTransitionEnd"],a=0;a<e.length;a++)f.wrapper.trigger(e[a])})}}},cube:{setTranslate:function(){var e,a=0;f.params.cube.shadow&&(t()?(e=f.wrapper.find(".swiper-cube-shadow"),0===e.length&&(e=h('<div class="swiper-cube-shadow"></div>'),f.wrapper.append(e)),e.css({height:f.width+"px"})):(e=f.container.find(".swiper-cube-shadow"),0===e.length&&(e=h('<div class="swiper-cube-shadow"></div>'),f.container.append(e))));for(var s=0;s<f.slides.length;s++){var r=f.slides.eq(s),i=90*s,n=Math.floor(i/360);f.rtl&&(i=-i,n=Math.floor(-i/360));var o=Math.max(Math.min(r[0].progress,1),-1),l=0,p=0,d=0;s%4===0?(l=4*-n*f.size,d=0):(s-1)%4===0?(l=0,d=4*-n*f.size):(s-2)%4===0?(l=f.size+4*n*f.size,d=f.size):(s-3)%4===0&&(l=-f.size,d=3*f.size+4*f.size*n),f.rtl&&(l=-l),t()||(p=l,l=0);var u="rotateX("+(t()?0:-i)+"deg) rotateY("+(t()?i:0)+"deg) translate3d("+l+"px, "+p+"px, "+d+"px)";if(1>=o&&o>-1&&(a=90*s+90*o,f.rtl&&(a=90*-s-90*o)),r.transform(u),f.params.cube.slideShadows){var c=r.find(t()?".swiper-slide-shadow-left":".swiper-slide-shadow-top"),m=r.find(t()?".swiper-slide-shadow-right":".swiper-slide-shadow-bottom");0===c.length&&(c=h('<div class="swiper-slide-shadow-'+(t()?"left":"top")+'"></div>'),r.append(c)),0===m.length&&(m=h('<div class="swiper-slide-shadow-'+(t()?"right":"bottom")+'"></div>'),r.append(m));{r[0].progress}c.length&&(c[0].style.opacity=-r[0].progress),m.length&&(m[0].style.opacity=r[0].progress)}}if(f.wrapper.css({"-webkit-transform-origin":"50% 50% -"+f.size/2+"px","-moz-transform-origin":"50% 50% -"+f.size/2+"px","-ms-transform-origin":"50% 50% -"+f.size/2+"px","transform-origin":"50% 50% -"+f.size/2+"px"}),f.params.cube.shadow)if(t())e.transform("translate3d(0px, "+(f.width/2+f.params.cube.shadowOffset)+"px, "+-f.width/2+"px) rotateX(90deg) rotateZ(0deg) scale("+f.params.cube.shadowScale+")");else{var v=Math.abs(a)-90*Math.floor(Math.abs(a)/90),g=1.5-(Math.sin(2*v*Math.PI/360)/2+Math.cos(2*v*Math.PI/360)/2),w=f.params.cube.shadowScale,b=f.params.cube.shadowScale/g,y=f.params.cube.shadowOffset;e.transform("scale3d("+w+", 1, "+b+") translate3d(0px, "+(f.height/2+y)+"px, "+-f.height/2/b+"px) rotateX(-90deg)")}var x=f.isSafari||f.isUiWebView?-f.size/2:0;f.wrapper.transform("translate3d(0px,0,"+x+"px) rotateX("+(t()?0:a)+"deg) rotateY("+(t()?-a:0)+"deg)")},setTransition:function(e){f.slides.transition(e).find(".swiper-slide-shadow-top, .swiper-slide-shadow-right, .swiper-slide-shadow-bottom, .swiper-slide-shadow-left").transition(e),f.params.cube.shadow&&!t()&&f.container.find(".swiper-cube-shadow").transition(e)}},coverflow:{setTranslate:function(){for(var e=f.translate,a=t()?-e+f.width/2:-e+f.height/2,s=t()?f.params.coverflow.rotate:-f.params.coverflow.rotate,r=f.params.coverflow.depth,i=0,n=f.slides.length;n>i;i++){var o=f.slides.eq(i),l=f.slidesSizesGrid[i],p=o[0].swiperSlideOffset,d=(a-p-l/2)/l*f.params.coverflow.modifier,u=t()?s*d:0,c=t()?0:s*d,m=-r*Math.abs(d),v=t()?0:f.params.coverflow.stretch*d,g=t()?f.params.coverflow.stretch*d:0;Math.abs(g)<.001&&(g=0),Math.abs(v)<.001&&(v=0),Math.abs(m)<.001&&(m=0),Math.abs(u)<.001&&(u=0),Math.abs(c)<.001&&(c=0);var w="translate3d("+g+"px,"+v+"px,"+m+"px)  rotateX("+c+"deg) rotateY("+u+"deg)";if(o.transform(w),o[0].style.zIndex=-Math.abs(Math.round(d))+1,f.params.coverflow.slideShadows){var b=o.find(t()?".swiper-slide-shadow-left":".swiper-slide-shadow-top"),y=o.find(t()?".swiper-slide-shadow-right":".swiper-slide-shadow-bottom");0===b.length&&(b=h('<div class="swiper-slide-shadow-'+(t()?"left":"top")+'"></div>'),o.append(b)),0===y.length&&(y=h('<div class="swiper-slide-shadow-'+(t()?"right":"bottom")+'"></div>'),o.append(y)),b.length&&(b[0].style.opacity=d>0?d:0),y.length&&(y[0].style.opacity=-d>0?-d:0)}}if(f.browser.ie){var x=f.wrapper[0].style;x.perspectiveOrigin=a+"px 50%"}},setTransition:function(e){f.slides.transition(e).find(".swiper-slide-shadow-top, .swiper-slide-shadow-right, .swiper-slide-shadow-bottom, .swiper-slide-shadow-left").transition(e)}}},f.lazy={initialImageLoaded:!1,loadImageInSlide:function(e){if("undefined"!=typeof e&&0!==f.slides.length){var a=f.slides.eq(e),t=a.find("img.swiper-lazy:not(.swiper-lazy-loaded):not(.swiper-lazy-loading)");0!==t.length&&t.each(function(){var e=h(this);e.addClass("swiper-lazy-loading");var t=e.attr("data-src");f.loadImage(e[0],t,!1,function(){e.attr("src",t),e.removeAttr("data-src"),e.addClass("swiper-lazy-loaded").removeClass("swiper-lazy-loading"),a.find(".swiper-lazy-preloader, .preloader").remove(),f.emit("onLazyImageReady",f,a[0],e[0])}),f.emit("onLazyImageLoad",f,a[0],e[0])})}},load:function(){if(f.params.watchSlidesVisibility)f.wrapper.children("."+f.params.slideVisibleClass).each(function(){f.lazy.loadImageInSlide(h(this).index())});else if(f.params.slidesPerView>1)for(var e=f.activeIndex;e<f.activeIndex+f.params.slidesPerView;e++)f.slides[e]&&f.lazy.loadImageInSlide(e);else f.lazy.loadImageInSlide(f.activeIndex);if(f.params.lazyLoadingInPrevNext){var a=f.wrapper.children("."+f.params.slideNextClass);a.length>0&&f.lazy.loadImageInSlide(a.index());var t=f.wrapper.children("."+f.params.slidePrevClass);t.length>0&&f.lazy.loadImageInSlide(t.index())}},onTransitionStart:function(){f.params.lazyLoading&&(f.params.lazyLoadingOnTransitionStart||!f.params.lazyLoadingOnTransitionStart&&!f.lazy.initialImageLoaded)&&(f.lazy.initialImageLoaded=!0,f.lazy.load())},onTransitionEnd:function(){f.params.lazyLoading&&!f.params.lazyLoadingOnTransitionStart&&f.lazy.load()}},f.scrollbar={set:function(){if(f.params.scrollbar){var e=f.scrollbar;e.track=h(f.params.scrollbar),e.drag=e.track.find(".swiper-scrollbar-drag"),0===e.drag.length&&(e.drag=h('<div class="swiper-scrollbar-drag"></div>'),e.track.append(e.drag)),e.drag[0].style.width="",e.drag[0].style.height="",e.trackSize=t()?e.track[0].offsetWidth:e.track[0].offsetHeight,e.divider=f.size/f.virtualSize,e.moveDivider=e.divider*(e.trackSize/f.size),e.dragSize=e.trackSize*e.divider,t()?e.drag[0].style.width=e.dragSize+"px":e.drag[0].style.height=e.dragSize+"px",e.track[0].style.display=e.divider>=1?"none":"",f.params.scrollbarHide&&(e.track[0].style.opacity=0)}},setTranslate:function(){if(f.params.scrollbar){var e,a=f.scrollbar,s=(f.translate||0,a.dragSize);e=(a.trackSize-a.dragSize)*f.progress,f.rtl&&t()?(e=-e,e>0?(s=a.dragSize-e,e=0):-e+a.dragSize>a.trackSize&&(s=a.trackSize+e)):0>e?(s=a.dragSize+e,e=0):e+a.dragSize>a.trackSize&&(s=a.trackSize-e),t()?(a.drag.transform(f.support.transforms3d?"translate3d("+e+"px, 0, 0)":"translateX("+e+"px)"),a.drag[0].style.width=s+"px"):(a.drag.transform(f.support.transforms3d?"translate3d(0px, "+e+"px, 0)":"translateY("+e+"px)"),a.drag[0].style.height=s+"px"),f.params.scrollbarHide&&(clearTimeout(a.timeout),a.track[0].style.opacity=1,a.timeout=setTimeout(function(){a.track[0].style.opacity=0,a.track.transition(400)},1e3))}},setTransition:function(e){f.params.scrollbar&&f.scrollbar.drag.transition(e)}},f.controller={setTranslate:function(e,a){var t,s,r=f.params.control;if(f.isArray(r))for(var i=0;i<r.length;i++)r[i]!==a&&r[i]instanceof Swiper&&(e=r[i].rtl&&"horizontal"===r[i].params.direction?-f.translate:f.translate,t=(r[i].maxTranslate()-r[i].minTranslate())/(f.maxTranslate()-f.minTranslate()),s=(e-f.minTranslate())*t+r[i].minTranslate(),f.params.controlInverse&&(s=r[i].maxTranslate()-s),r[i].updateProgress(s),r[i].setWrapperTranslate(s,!1,f),r[i].updateActiveIndex());else r instanceof Swiper&&a!==r&&(e=r.rtl&&"horizontal"===r.params.direction?-f.translate:f.translate,t=(r.maxTranslate()-r.minTranslate())/(f.maxTranslate()-f.minTranslate()),s=(e-f.minTranslate())*t+r.minTranslate(),f.params.controlInverse&&(s=r.maxTranslate()-s),r.updateProgress(s),r.setWrapperTranslate(s,!1,f),r.updateActiveIndex())},setTransition:function(e,a){var t=f.params.control;if(f.isArray(t))for(var s=0;s<t.length;s++)t[s]!==a&&t[s]instanceof Swiper&&t[s].setWrapperTransition(e,f);else t instanceof Swiper&&a!==t&&t.setWrapperTransition(e,f)}},f.hashnav={init:function(){if(f.params.hashnav){f.hashnav.initialized=!0;var e=document.location.hash.replace("#","");if(e)for(var a=0,t=0,s=f.slides.length;s>t;t++){var r=f.slides.eq(t),i=r.attr("data-hash");if(i===e&&!r.hasClass(f.params.slideDuplicateClass)){var n=r.index();f.slideTo(n,a,f.params.runCallbacksOnInit,!0)}}}},setHash:function(){f.hashnav.initialized&&f.params.hashnav&&(document.location.hash=f.slides.eq(f.activeIndex).attr("data-hash")||"")}},f.disableKeyboardControl=function(){h(document).off("keydown",n)},f.enableKeyboardControl=function(){h(document).on("keydown",n)},f._wheelEvent=!1,f._lastWheelScrollTime=(new Date).getTime(),f.params.mousewheelControl){if(void 0!==document.onmousewheel&&(f._wheelEvent="mousewheel"),!f._wheelEvent)try{new WheelEvent("wheel"),f._wheelEvent="wheel"}catch(D){}f._wheelEvent||(f._wheelEvent="DOMMouseScroll")}f.disableMousewheelControl=function(){return f._wheelEvent?(f.container.off(f._wheelEvent,o),!0):!1},f.enableMousewheelControl=function(){return f._wheelEvent?(f.container.on(f._wheelEvent,o),!0):!1},f.parallax={setTranslate:function(){f.container.children("[data-swiper-parallax], [data-swiper-parallax-x], [data-swiper-parallax-y]").each(function(){l(this,f.progress)}),f.slides.each(function(){var e=h(this);e.find("[data-swiper-parallax], [data-swiper-parallax-x], [data-swiper-parallax-y]").each(function(){var a=Math.min(Math.max(e[0].progress,-1),1);l(this,a)})})},setTransition:function(e){"undefined"==typeof e&&(e=f.params.speed),f.container.find("[data-swiper-parallax], [data-swiper-parallax-x], [data-swiper-parallax-y]").each(function(){var a=h(this),t=parseInt(a.attr("data-swiper-parallax-duration"),10)||e;0===e&&(t=0),a.transition(t)})}},f._plugins=[];for(var L in f.plugins){var G=f.plugins[L](f,f.params[L]);G&&f._plugins.push(G)}return f.callPlugins=function(e){for(var a=0;a<f._plugins.length;a++)e in f._plugins[a]&&f._plugins[a][e](arguments[1],arguments[2],arguments[3],arguments[4],arguments[5])},f.emitterEventListeners={},f.emit=function(e){f.params[e]&&f.params[e](arguments[1],arguments[2],arguments[3],arguments[4],arguments[5]);var a;if(f.emitterEventListeners[e])for(a=0;a<f.emitterEventListeners[e].length;a++)f.emitterEventListeners[e][a](arguments[1],arguments[2],arguments[3],arguments[4],arguments[5]);f.callPlugins&&f.callPlugins(e,arguments[1],arguments[2],arguments[3],arguments[4],arguments[5])},f.on=function(e,a){return e=p(e),f.emitterEventListeners[e]||(f.emitterEventListeners[e]=[]),f.emitterEventListeners[e].push(a),f},f.off=function(e,a){var t;if(e=p(e),"undefined"==typeof a)return f.emitterEventListeners[e]=[],f;if(f.emitterEventListeners[e]&&0!==f.emitterEventListeners[e].length){for(t=0;t<f.emitterEventListeners[e].length;t++)f.emitterEventListeners[e][t]===a&&f.emitterEventListeners[e].splice(t,1);return f}},f.once=function(e,a){e=p(e);var t=function(){a(arguments[0],arguments[1],arguments[2],arguments[3],arguments[4]),f.off(e,t)};return f.on(e,t),f},f.a11y={makeFocusable:function(e){return e[0].tabIndex="0",e},addRole:function(e,a){return e.attr("role",a),e},addLabel:function(e,a){return e.attr("aria-label",a),e},disable:function(e){return e.attr("aria-disabled",!0),e},enable:function(e){return e.attr("aria-disabled",!1),e},onEnterKey:function(e){13===e.keyCode&&(h(e.target).is(f.params.nextButton)?(f.onClickNext(e),f.a11y.notify(f.isEnd?f.params.lastSlideMsg:f.params.nextSlideMsg)):h(e.target).is(f.params.prevButton)&&(f.onClickPrev(e),f.a11y.notify(f.isBeginning?f.params.firstSlideMsg:f.params.prevSlideMsg)))},liveRegion:h('<span class="swiper-notification" aria-live="assertive" aria-atomic="true"></span>'),notify:function(e){var a=f.a11y.liveRegion;0!==a.length&&(a.html(""),a.html(e))},init:function(){if(f.params.nextButton){var e=h(f.params.nextButton);f.a11y.makeFocusable(e),f.a11y.addRole(e,"button"),f.a11y.addLabel(e,f.params.nextSlideMsg)}if(f.params.prevButton){var a=h(f.params.prevButton);f.a11y.makeFocusable(a),f.a11y.addRole(a,"button"),f.a11y.addLabel(a,f.params.prevSlideMsg)}h(f.container).append(f.a11y.liveRegion)},destroy:function(){f.a11y.liveRegion&&f.a11y.liveRegion.length>0&&f.a11y.liveRegion.remove()}},f.init=function(){f.params.loop&&f.createLoop(),f.updateContainerSize(),f.updateSlidesSize(),f.updatePagination(),f.params.scrollbar&&f.scrollbar&&f.scrollbar.set(),"slide"!==f.params.effect&&f.effects[f.params.effect]&&(f.params.loop||f.updateProgress(),f.effects[f.params.effect].setTranslate()),f.params.loop?f.slideTo(f.params.initialSlide+f.loopedSlides,0,f.params.runCallbacksOnInit):(f.slideTo(f.params.initialSlide,0,f.params.runCallbacksOnInit),0===f.params.initialSlide&&(f.parallax&&f.params.parallax&&f.parallax.setTranslate(),f.lazy&&f.params.lazyLoading&&f.lazy.load())),f.attachEvents(),f.params.observer&&f.support.observer&&f.initObservers(),f.params.preloadImages&&!f.params.lazyLoading&&f.preloadImages(),f.params.autoplay&&f.startAutoplay(),f.params.keyboardControl&&f.enableKeyboardControl&&f.enableKeyboardControl(),f.params.mousewheelControl&&f.enableMousewheelControl&&f.enableMousewheelControl(),f.params.hashnav&&f.hashnav&&f.hashnav.init(),f.params.a11y&&f.a11y&&f.a11y.init(),f.emit("onInit",f)},f.cleanupStyles=function(){f.container.removeClass(f.classNames.join(" ")).removeAttr("style"),f.wrapper.removeAttr("style"),f.slides&&f.slides.length&&f.slides.removeClass([f.params.slideVisibleClass,f.params.slideActiveClass,f.params.slideNextClass,f.params.slidePrevClass].join(" ")).removeAttr("style").removeAttr("data-swiper-column").removeAttr("data-swiper-row"),f.paginationContainer&&f.paginationContainer.length&&f.paginationContainer.removeClass(f.params.paginationHiddenClass),f.bullets&&f.bullets.length&&f.bullets.removeClass(f.params.bulletActiveClass),f.params.prevButton&&h(f.params.prevButton).removeClass(f.params.buttonDisabledClass),f.params.nextButton&&h(f.params.nextButton).removeClass(f.params.buttonDisabledClass),f.params.scrollbar&&f.scrollbar&&(f.scrollbar.track&&f.scrollbar.track.length&&f.scrollbar.track.removeAttr("style"),f.scrollbar.drag&&f.scrollbar.drag.length&&f.scrollbar.drag.removeAttr("style"))},f.destroy=function(e,a){f.detachEvents(),f.stopAutoplay(),f.params.loop&&f.destroyLoop(),a&&f.cleanupStyles(),f.disconnectObservers(),f.params.keyboardControl&&f.disableKeyboardControl&&f.disableKeyboardControl(),f.params.mousewheelControl&&f.disableMousewheelControl&&f.disableMousewheelControl(),f.params.a11y&&f.a11y&&f.a11y.destroy(),f.emit("onDestroy"),e!==!1&&(f=null)},f.init(),f}},Swiper.prototype={isSafari:function(){var e=navigator.userAgent.toLowerCase();return e.indexOf("safari")>=0&&e.indexOf("chrome")<0&&e.indexOf("android")<0}(),isUiWebView:/(iPhone|iPod|iPad).*AppleWebKit(?!.*Safari)/i.test(navigator.userAgent),isArray:function(e){return"[object Array]"===Object.prototype.toString.apply(e)},browser:{ie:window.navigator.pointerEnabled||window.navigator.msPointerEnabled,ieTouch:window.navigator.msPointerEnabled&&window.navigator.msMaxTouchPoints>1||window.navigator.pointerEnabled&&window.navigator.maxTouchPoints>1},device:function(){var e=navigator.userAgent,a=e.match(/(Android);?[\s\/]+([\d.]+)?/),t=e.match(/(iPad).*OS\s([\d_]+)/),s=(e.match(/(iPod)(.*OS\s([\d_]+))?/),!t&&e.match(/(iPhone\sOS)\s([\d_]+)/));return{ios:t||s||t,android:a}}(),support:{touch:window.Modernizr&&Modernizr.touch===!0||function(){return!!("ontouchstart"in window||window.DocumentTouch&&document instanceof DocumentTouch)}(),transforms3d:window.Modernizr&&Modernizr.csstransforms3d===!0||function(){var e=document.createElement("div").style;return"webkitPerspective"in e||"MozPerspective"in e||"OPerspective"in e||"MsPerspective"in e||"perspective"in e}(),flexbox:function(){for(var e=document.createElement("div").style,a="alignItems webkitAlignItems webkitBoxAlign msFlexAlign mozBoxAlign webkitFlexDirection msFlexDirection mozBoxDirection mozBoxOrient webkitBoxDirection webkitBoxOrient".split(" "),t=0;t<a.length;t++)if(a[t]in e)return!0}(),observer:function(){return"MutationObserver"in window||"WebkitMutationObserver"in window}()},plugins:{}};for(var a=["jQuery","Zepto","Dom7"],t=0;t<a.length;t++)window[a[t]]&&e(window[a[t]]);var s;s="undefined"==typeof Dom7?window.Dom7||window.Zepto||window.jQuery:Dom7,s&&("transitionEnd"in s.fn||(s.fn.transitionEnd=function(e){function a(i){if(i.target===this)for(e.call(this,i),t=0;t<s.length;t++)r.off(s[t],a)}var t,s=["webkitTransitionEnd","transitionend","oTransitionEnd","MSTransitionEnd","msTransitionEnd"],r=this;if(e)for(t=0;t<s.length;t++)r.on(s[t],a);return this}),"transform"in s.fn||(s.fn.transform=function(e){for(var a=0;a<this.length;a++){var t=this[a].style;t.webkitTransform=t.MsTransform=t.msTransform=t.MozTransform=t.OTransform=t.transform=e}return this}),"transition"in s.fn||(s.fn.transition=function(e){"string"!=typeof e&&(e+="ms");for(var a=0;a<this.length;a++){var t=this[a].style;t.webkitTransitionDuration=t.MsTransitionDuration=t.msTransitionDuration=t.MozTransitionDuration=t.OTransitionDuration=t.transitionDuration=e}return this}))}(),"undefined"!=typeof module?module.exports=Swiper:"function"==typeof define&&define.amd&&define([],function(){"use strict";return Swiper});
//# sourceMappingURL=maps/swiper.jquery.min.js.map