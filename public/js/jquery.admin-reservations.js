$(document).ready(function () {
    $('select[name="status"], select[name="show"]').change(function () {
        var form = $(this).parents('form:first');

        var method = form.find('input[name="_method"]').val() || 'POST';
        // grab the url
        var url = form.prop('action');

        $.ajax({
            type: method,
            url: url,
            data: form.serialize(),
            dataType: 'json'
        })
            .fail(function () {
                alert('Request failed please try again later');
            })
            .done(function (data) {
                // do nufin
            });
    });
});