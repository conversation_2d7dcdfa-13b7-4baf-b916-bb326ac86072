
jQuery(($) => {
    /**
     * Anchor links
     */
    // $("a[href^=\\#]").on("click", (e) => {
    // 	e.preventDefault();

    // 	var $elem = $($.attr(this, "href"));
    // 	$("html, body").animate(
    // 		{
    // 			scrollTop: $elem.length ? $elem.offset().top : 0
    // 		},
    // 		SCROLL_ANIMATION_SPEED
    // 	);
    // });
    // When the window has finished loading create our google map below

    if ($('.js-sticky').length) {
        var fixmeTop = $('.js-sticky').offset().top;       // get initial position of the element
    }

    $(window).scroll(function () {                  // assign scroll event listener

        if ($(window).width() < 1025 && $('.js-sticky').length) {
            var currentScroll = $(window).scrollTop(); // get current position

            if (currentScroll >= fixmeTop) {           // apply position: fixed if you
                // $('.js-sticky').css({                      // scroll to that element or below it
                // 	position: 'fixed',
                // 	width: '100%'
                // });
            } else {                                   // apply position: static
                $('.js-sticky').css({                      // if you scroll above it
                    position: 'static',
                    width: 'unset'
                });
            }
        }

    });

    $('.jsOpenLanguageMenu').on('click', function () {
        $(this).find('.language-menu').toggle(100);
    });

    var rotation = 0;

    $('.filter-item').on('click', function (e) {

        if ($(this).hasClass('open')) {
            $(this).removeClass('open');
            $(this).find('img').rotate(0);
            $(this).children('.filter-dropdown').slideToggle('fast');
        } else {
            $('.filter-item').each(function () {
                if ($(this).hasClass('open')) {
                    $(this).removeClass('open');
                    $(this).find('img').rotate(0);
                    $(this).children('.filter-dropdown').slideToggle('fast');
                }
            });

            $(this).addClass('open');
            $(this).find('img').rotate(180);
            $(this).children('.filter-dropdown').slideToggle('fast');
        }
    }).on('click', '.filter-dropdown', function (e) {
        // clicked on descendant div
        e.stopPropagation();
    });

    $('.filter-toggle').on('click', function () {
        $('.filter-mobile-menu').slideToggle('fast');
    }).on('click', '.filter-mobile-menu', function (e) {
        // clicked on descendant div
        e.stopPropagation();
    });

    $(document).on("click", function (event) {

        if ($(window).width() > 1145) {
            var $trigger = $(".filter-item");
            if ($trigger !== event.target && !$trigger.has(event.target).length) {
                $('.filter-item').each(function () {
                    if ($(this).hasClass('open')) {
                        $(this).removeClass('open');
                        $(this).find('img').rotate(0);
                        $(this).children('.filter-dropdown').slideToggle('fast');
                    }
                });
            }
        } else {
            var $trigger = $(".filter-toggle");
            if ($trigger !== event.target && !$trigger.has(event.target).length) {
                $('.filter-mobile-menu').slideUp('fast');
            }
        }

    });

    var map;
    $(document).ready(function () {
        AOS.init({
            disable: function () {
                var maxWidth = 768;
                return window.innerWidth < maxWidth;
            }
        });

        if ($('#gmap_canvas').length) {
            google.maps.event.addDomListener(window, "load", gmapinit);
            gmapinit();
        }

        $("#irakleio").on('click', function () {
            newLocation(35.33966244512311, 25.174705836424685);
        });

        $("#chania").on('click', function () {
            newLocation(35.53988270871307, 24.140431532539296);
        });

        $("#rethymno").on('click', function () {
            newLocation(35.3650727551667, 24.490822930402206);
        });

        $("#agios-nikolaos").on('click', function () {
            newLocation(35.20295281581812, 25.716543947484755);
        });

    });

    function gmapinit() {
        // Basic options for a simple Google Map
        // For more options see: https://developers.google.com/maps/documentation/javascript/reference#MapOptions
        var mapOptions = {
            // How zoomed in you want the map to start at (always required)
            zoom: 11,

            // The latitude and longitude to center the map (always required)
            center: new google.maps.LatLng(35.33966244512311, 25.174705836424685), // New York

            // How you would like to style the map.
            // This is where you would paste any style found on Snazzy Maps.
            styles: [
                {
                    featureType: "all",
                    elementType: "labels",
                    stylers: [
                        {
                            saturation: "100",
                        },
                        {
                            visibility: "on",
                        },
                        {
                            color: "#1b1a1a",
                        },
                        {
                            lightness: "45",
                        },
                        {
                            gamma: "5.51",
                        },
                        {
                            weight: "1.85",
                        },
                    ],
                },
                {
                    featureType: "all",
                    elementType: "labels.text",
                    stylers: [
                        {
                            color: "#181717",
                        },
                        {
                            gamma: "3.61",
                        },
                    ],
                },
                {
                    featureType: "all",
                    elementType: "labels.text.fill",
                    stylers: [
                        {
                            saturation: 36,
                        },
                        {
                            lightness: 40,
                        },
                        {
                            visibility: "on",
                        },
                        {
                            color: "#120a0a",
                        },
                        {
                            weight: "4.53",
                        },
                    ],
                },
                {
                    featureType: "all",
                    elementType: "labels.text.stroke",
                    stylers: [
                        {
                            visibility: "on",
                        },
                        {
                            color: "#ffffff",
                        },
                        {
                            lightness: 16,
                        },
                    ],
                },
                {
                    featureType: "all",
                    elementType: "labels.icon",
                    stylers: [
                        {
                            visibility: "off",
                        },
                    ],
                },
                {
                    featureType: "administrative",
                    elementType: "geometry.fill",
                    stylers: [
                        {
                            color: "#fefefe",
                        },
                        {
                            lightness: 20,
                        },
                    ],
                },
                {
                    featureType: "administrative",
                    elementType: "geometry.stroke",
                    stylers: [
                        {
                            color: "#fefefe",
                        },
                        {
                            lightness: 17,
                        },
                        {
                            weight: 1.2,
                        },
                    ],
                },
                {
                    featureType: "landscape",
                    elementType: "geometry",
                    stylers: [
                        {
                            color: "#f5f5f5",
                        },
                        {
                            lightness: 20,
                        },
                    ],
                },
                {
                    featureType: "poi",
                    elementType: "geometry",
                    stylers: [
                        {
                            color: "#f5f5f5",
                        },
                        {
                            lightness: 21,
                        },
                    ],
                },
                {
                    featureType: "poi.park",
                    elementType: "geometry",
                    stylers: [
                        {
                            color: "#dedede",
                        },
                        {
                            lightness: 21,
                        },
                    ],
                },
                {
                    featureType: "road",
                    elementType: "all",
                    stylers: [
                        {
                            color: "#582a2a",
                        },
                    ],
                },
                {
                    featureType: "road",
                    elementType: "geometry",
                    stylers: [
                        {
                            color: "#fcb22c",
                        },
                        {
                            visibility: "simplified",
                        },
                    ],
                },
                {
                    featureType: "road",
                    elementType: "geometry.fill",
                    stylers: [
                        {
                            color: "#fcb22c",
                        },
                    ],
                },
                {
                    featureType: "road",
                    elementType: "geometry.stroke",
                    stylers: [
                        {
                            visibility: "simplified",
                        },
                        {
                            color: "#fcb22c",
                        },
                    ],
                },
                {
                    featureType: "road.highway",
                    elementType: "geometry.fill",
                    stylers: [
                        {
                            color: "#fcb22c",
                        },
                        {
                            lightness: 17,
                        },
                    ],
                },
                {
                    featureType: "road.highway",
                    elementType: "geometry.stroke",
                    stylers: [
                        {
                            color: "#ffffff",
                        },
                        {
                            lightness: 29,
                        },
                        {
                            weight: 0.2,
                        },
                    ],
                },
                {
                    featureType: "road.arterial",
                    elementType: "geometry",
                    stylers: [
                        {
                            color: "#ffffff",
                        },
                        {
                            lightness: 18,
                        },
                    ],
                },
                {
                    featureType: "road.arterial",
                    elementType: "geometry.fill",
                    stylers: [
                        {
                            color: "#f3e1c1",
                        },
                    ],
                },
                {
                    featureType: "road.local",
                    elementType: "geometry",
                    stylers: [
                        {
                            color: "#ffffff",
                        },
                        {
                            lightness: 16,
                        },
                    ],
                },
                {
                    featureType: "transit",
                    elementType: "geometry",
                    stylers: [
                        {
                            color: "#f2f2f2",
                        },
                        {
                            lightness: 19,
                        },
                    ],
                },
                {
                    featureType: "water",
                    elementType: "geometry",
                    stylers: [
                        {
                            color: "#d2d7d8",
                        },
                        {
                            lightness: 17,
                        },
                    ],
                },
            ],
        };

        // Get the HTML DOM element that will contain your map
        // We are using a div with id="gmap_canvas" seen below in the <body>
        var mapElement = document.getElementById("gmap_canvas");

        // Create the Google Map using our element and options defined above
        map = new google.maps.Map(mapElement, mapOptions);

        // Let's also add a marker while we're at it
        var marker = new google.maps.Marker({
            position: new google.maps.LatLng(35.33966244512311, 25.174705836424685),
            map: map,
            title: "Snazzy!",
        });
    }

    function newLocation(newLat, newLng) {
        map.setCenter({
            lat: newLat,
            lng: newLng
        });

        var marker = new google.maps.Marker({
            position: {
                lat: newLat,
                lng: newLng
            },
            map: map,
            title: "Snazzy!",
        });
    }



    jQuery.fn.rotate = function (degrees) {
        $(this).css({ 'transform': 'rotate(' + degrees + 'deg)' });
        return $(this);
    };

    $('.read-more').on('click', function () {
        $(this).prev('.read-more-content').slideToggle();
        let innerHTML = $(this).html();
        rotation += 180;
        if ($(this).hasClass('open')) {
            $(this).removeClass('open');
            innerHTML = innerHTML.replace('less', 'more');
            $(this).html(innerHTML);
            $(this).find('img').rotate(rotation);
        } else {
            $(this).addClass('open');
            innerHTML = innerHTML.replace('more', 'less');
            $(this).html(innerHTML);
            $(this).find('img').rotate(rotation);
        }
    });

    $('.optional-extra-item').each(function (e) {
        if ($(this).find('input').prop('checked') == true) {
            $(this).addClass('selected');
        }
    });

    // open mobile menu
    $(".trigger-menu").on("click", function (e) {
        e.preventDefault();

        $(".mobile-menu").toggleClass("is_active");
        $(this).toggleClass("is_active");
        $("body").toggleClass("overflow-hidden");
    });

    /* Testimonials Slider	*/
    $(".testimonials-slider").slick({
        arrows: false,
        dots: true,
        slidesToShow: 1,
        autoplay: true,
        autoplaySpeed: 5000,
    });

    /* Single Car Slider */
    $(".single-car-slider").slick({
        arrows: true,
        dots: false,
        slidesToShow: 1,
        // autoplay: true,
        // autoplaySpeed: 2000,
    });

    /* Car Slider	*/
    $(".jsCarSlider").slick({
        arrows: true,
        dots: true,
        slidesToShow: 3,
        slidesToScroll: 1,
        infinite: false,
        responsive: [
            {
                breakpoint: 1200,
                settings: {
                    slidesToShow: 2,
                    slidesToScroll: 1,
                    infinite: false,
                    arrows: true,
                    dots: true
                }
            },
            {
                breakpoint: 600,
                settings: {
                    slidesToShow: 1,
                    slidesToScroll: 1,
                    centerMode: true,
                    centerPadding: "7%",
                    dots: true,
                    arrows: true,
                    infinite: false
                }
            }
        ]
    });

    /* Optional Extras - Accessories */
    $('.jsCheckOption').each(function () {
        $(this).on('click', function () {
            // grab id of element/accessory to change
            var element_id = $(this).data('element');

            if ($(this).prop('checked') == true) {
                $(this).parent().parent().addClass('selected');
                // include the relevant accessory / add one count of this accessory to its hidden input
                $('#' + element_id).val(1);
            } else {
                $(this).parent().parent().removeClass('selected');
                // remove the relevant accessory / remove one count of this accessory to its hidden input
                $('#' + element_id).val(0);
            }
        })
    })

    // original code from psdgator that is now not needed (since we are using a button element)
    // $('a.rent-btn').on('click', function () {
    // 	$('#booking_form').submit();
    // });

    /* FAQ open/close question */
    $('.question').on('click', function () {
        $(this).parent().toggleClass('open');
        $(this).parent().find('.answer').slideToggle();
    })

    /* Service points open/close tab */
    // $('.locations-list-item h3').on('click', function () {
    //     $(this).parent().toggleClass('open');
    // })

    // $('.locations-list-item li').on('click', function () {
    //     $('.locations-list-item li').removeClass('active');
    //     $(this).addClass('active');
    // })

    // $('.datepicker_from').on('change', function () {
    //     $('.booking-date-from').html($('#pickup_date').val());
    // });

    // $('.datepicker_to').on('change', function () {
    //     $('.booking-date-to').html($('#dropoff_date').val());
    // });

    var current_locale = $('#current_locales').attr('title');
    var default_time = $('#default-time').data('value');

    /**
     *  Sets the default dates for pickup & dropoff datepickers
     *  (on clear all click)
     */
    // function setDatePickerDefaultDates() {
    //     var dropoff_date = new Date();
    //     dropoff_date.setDate(dropoff_date.getDate() + 2);
    //     $(".datepicker_from").datepicker("setDate", dropoff_date).trigger('change');

    //     var min_pickup_date = new Date();
    //     min_pickup_date.setDate(min_pickup_date.getDate() + 5);
    //     $(".datepicker_to").datepicker("option", "minDate", min_pickup_date);

    //     var pickup_date = new Date();
    //     pickup_date.setDate(pickup_date.getDate() + 9);
    //     $(".datepicker_to").datepicker("setDate", pickup_date).trigger('change');
    // }

    /**
     * Sets the min dropoff date according to a pickup time
     * @param pickupDate
     * @param initialRun (true only if it is triggered on document ready, false on every other case)
     */
    function setMinDropOffDate(pickupDate, initialRun) {
        var minDropOffDate = new Date(pickupDate);
        minDropOffDate.setDate(minDropOffDate.getDate() + 3);
        $(".datepicker_to").datepicker("option", "minDate", minDropOffDate).trigger('change');
        if (initialRun) {
            // trigger a change event to set pickup date session cookie
            $(".datepicker_from").trigger('change');
        }
    }

    /**
     * Triggers a change event on select2 elements in order to set their session cookies
     * Does not affect current selected value, just triggers the change event
     */
    function initSelect2SessionCookies() {
        $('.select2.sessionable').each(function () {
            if (document.cookie.indexOf($(this).prop('name')) < 0) {
                // set the cookie by triggering a change event without changing the values
                $(this).trigger('change');
            }
        });
    }

    function updatePrices(data) {
        // if the response says to, then we should refresh the page
        if (data['refresh'] === true) {
            location.reload();
        }

        $('#initial_price').html(data['total_price_initial']);

        if (data['extra_miles_charge'] > 0) {
            $('#extra_miles').html(data['extra_miles_charge']);
            $('#holder_extra_miles').fadeIn();
        }
        else {
            $('#holder_extra_miles').fadeOut();
        }

        if (data['remote_location_charge'] > 0) {
            $('#remote_location').html(data['remote_location_charge']);
            $('#holder_remote_location').fadeIn();
        }
        else {
            $('#holder_remote_location').fadeOut();
        }

        if (data['after_hours_charge'] > 0) {
            $('#after_hours').html(data['after_hours_charge']);
            $('#holder_after_hours').fadeIn();
        }
        else {
            $('#holder_after_hours').fadeOut();
        }

        if (data['fuel_plan_charge'] > 0) {
            $('#fuel_plan').html(data['fuel_plan_charge']);
            $('#holder_fuel_plan').fadeIn();
        }
        else {
            $('#holder_fuel_plan').fadeOut();
        }
        $('.final-price').html(data['final_price']);

        // update accessories
        for (var key in data['accessories']) {

            var value = data['accessories'][key];

            if (value['count'] > 0) {
                // $('.number_accessory_' + key).html('x' + value['count']);
                $('#holder_accessory_' + key).fadeIn();
                $('#subtotal_accessory_' + key).html(value['price'] * data['total_days']);
            }
            else {
                // $('.number_accessory_' + key).html('');
                $('#holder_accessory_' + key).fadeOut();
                $('#subtotal_accessory_' + key).html(value['price']);

            }
        } // update accessories

        // update days count
        $('.total-rent-days').html(data['total_days']);

    } // function updatePrices(data)

    $(document).ready(function () {

        // datepicker localization
        if (typeof (current_locale) !== "undefined") {
            $.datepicker.setDefaults(
                $.extend(
                    $.datepicker.regional[current_locale]
                )
            );
        }

        // scrolling elements
        $('.scroller').on('click', function (e) {
            e.preventDefault();
            var destination_id = $(this).data('destination');
            var element_to_scroll_to = document.getElementById(destination_id);
            element_to_scroll_to.scrollIntoView({ block: "nearest", behavior: "smooth" });
        });

        // newsletter form
        $('form#newsletter_form').on('submit', function (e) {
            // grab the form
            var form = $(this);
            // Laravel creates hidden inputs for the method attributes of forms (except post methods)
            // so we need to pass the POST by ourselves
            var method = 'POST';
            // grab the url
            var url = form.prop('action');
            var token = $('input[name="_token"]').val();

            $.ajax({
                type: method,
                url: url,
                headers: {
                    "x-csrf-token": token
                },
                data: form.serialize(),
                dataType: 'json'
            })
                .done(function (data) {
                    if (data.status == 'success') {
                        swal({ title: form.data("title"), text: form.data("text"), type: "success" });
                        // reset the input content
                        $('input[name="newsletter_email"]').val('');
                    }
                    else {
                        swal({ title: form.data("title-error"), text: form.data("text-error"), type: "warning" });
                    }
                });

            e.preventDefault();
        });

        $('.submit-footer-form').on('click', function () {
            $('form#newsletter_form').submit();
        });

        // select2 init
        // if ($('.select2').length) {
        //     $('.select2').select2();
        //     // for localised landing pages
        //     $('.force-select2').each(function () {

        //         var id = '#' + $(this).data('element_id');
        //         var value = $(this).data('value');
        //         if (value > 0) {
        //             $(id).val(value).trigger('change');
        //             Cookies.set($(this).data('element_id'), value);
        //         }
        //     });
        //     // select2 dropdowns that on clear form we want to have the 1st option selected
        //     var clearable_selects = $('.main .select2.clearable');
        // }

        /**********************************************************************/
        /**************************** Search Bucket ***************************/
        /**********************************************************************/


        


        $("input[id^='accessory_']").val(0);

    

        // bind the 'data-remote' attribute to ajaxified forms
        $('form[data-remote]').on('submit', function (e) {
            swal({ title: $('#url').data("proccessing-title"), text: $('#url').data("proccessing-message"), imageUrl: "../../images/loader.gif", hideConfirmButton: true });
            // grab the form
            var form = $(this);
            // Laravel creates hidden inputs for the method attributes of forms (except post methods)
            // so we need to pass the POST by ourselves
            var method = form.find('input[name="_method"]').val() || 'POST';
            var token = $('input[name="_token"]').val();
            // grab the url
            var url = form.prop('action');

            var reset_dropoff_location = false;
            if ($("#dropoff_location", form).length > 0 && $("#dropoff_location", form).val() === '') {
                reset_dropoff_location = true;
                $("#dropoff_location", form).val($("#pickup_location", form).val());
            }

            $.ajax({
                type: method,
                url: url,
                headers: {
                    "x-csrf-token": token
                },
                data: form.serialize(),
                dataType: 'json'
            })
                .fail(function () {
                    swal({ title: $('#url').data("error-title"), text: $('#url').data("error-message"), type: "error" });
                    if (reset_dropoff_location) {
                        $("#dropoff_location", form).val("");
                    }
                })
                .done(function (data) {
                    if (reset_dropoff_location) {
                        $("#dropoff_location", form).val("");
                    }
                    if (data.status == 'success') {
                        window.location = $('#url').data("geturl") + '/' + data.reservationId;
                    }
                    else if (data.status == 'error' && data.msg == 'Minimum reservation duration is 3 days') {
                        swal({ title: $('#url').data("minimum-title"), text: $('#url').data("minimum-message"), type: "warning" });
                    }
                    else if (data.status == 'error' && data.msg == 'Please verify the pickup - dropoff dates') {
                        swal({ title: $('#url').data("error-title"), text: $('#url').data("invalid-dates-message"), type: "warning" });
                    }
                    else {
                        swal({ title: $('#url').data("warning-title"), text: $('#url').data("warning-message"), type: "warning" });

                        $("[id^='error_']").html(" ");
                        $('.errorable').removeClass('witherror');
                        $('.error').removeClass('error--visible');

                        $.each(data.msg, function (index, value) {
                            $('#' + index).addClass('witherror');
                            $('#error_' + index).html(value);
                            $('#error_' + index).addClass('error--visible');
                        });
                    }
                });

            e.preventDefault();
        });

        // bind the 'sessionable' css class to elements that change the session vars stored
        $('.sessionable').change(function () {
            // Set the cookie
            Cookies.set($(this).prop('name'), $(this).val());
            // console.log(Cookies.get($(this).prop('name')));

            if ($(this).hasClass('priceable')) {
                // grab the booking form
                var form = $('#booking_form');
                var token = $('input[name="_token"]').val();

                if (form.length != 0) {

                    // grab the url
                    var url = $('#priceable_url').val();

                    $.ajax({
                        context: this,
                        type: 'POST',
                        headers: {
                            "x-csrf-token": token
                        },
                        url: url,
                        data: form.serialize(),
                        dataType: 'json'
                    })
                        .done(function (data) {
                            updatePrices(data);
                        });
                }
            }
        });


        $('#datepicker').on("change", function () {

            var form = $('#booking_form');
            var token = $('input[name="_token"]').val();

            if (form.length != 0) {

                // grab the url
                var url = $('#priceable_url').val();

                $.ajax({
                    context: this,
                    type: 'POST',
                    headers: {
                        "x-csrf-token": token
                    },
                    url: url,
                    data: form.serialize(),
                    dataType: 'json'
                })
                    .done(function (data) {
                        updatePrices(data);
                    });
            }
        });

        $('#different-dropoff-location').on('change' ,function(){
            if(! $(this).is(":checked")){
                var form = $('#booking_form');
            var token = $('input[name="_token"]').val();

            if (form.length != 0) {

                // grab the url
                var url = $('#priceable_url').val();

                $.ajax({
                    context: this,
                    type: 'POST',
                    headers: {
                        "x-csrf-token": token
                    },
                    url: url,
                    data: form.serialize(),
                    dataType: 'json'
                })
                    .done(function (data) {
                        updatePrices(data);
                    });
            }
            }
        });


        $('#fuel_plan_select_0, #fuel_plan_select_1').on('change', function (e) {
            $('#fuel_plan_value').val($(this).val());
            var form = $('#booking_form');
            var token = $('input[name="_token"]').val();

            // grab the url
            var url = $('#priceable_url').val();

            $.ajax({
                context: this,
                type: 'POST',
                headers: {
                    "x-csrf-token": token
                },
                url: url,
                data: form.serialize(),
                dataType: 'json'
            })
                .done(function (data) {
                    updatePrices(data);
                });
        });

        // always set dropoff min date according to pickup date on load
        setMinDropOffDate($(".datepicker_from").datepicker("getDate"), true);
        // set select2 values to sesion cookies
        initSelect2SessionCookies();

    });

});

//Get the button
let mybutton = document.getElementById("btn-back-to-top");

// When the user scrolls down 20px from the top of the document, show the button
window.onscroll = function () {
    scrollFunction();
};

function scrollFunction() {
    if (
        document.body.scrollTop > 80 ||
        document.documentElement.scrollTop > 80
    ) {
        mybutton.style.display = "block";
    } else {
        mybutton.style.display = "none";
    }
}
// When the user clicks on the button, scroll to the top of the document
mybutton.addEventListener("click", backToTop);

function backToTop() {
    window.scrollTo({ top: 0, behavior: 'smooth' });
    // document.body.scrollTop = 0;
    // document.documentElement.scrollTop = 0;
}