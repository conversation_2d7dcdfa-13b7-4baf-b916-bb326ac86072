/* Romansh initialisation for the jQuery UI date picker plugin. */
/* Written by <PERSON> (<EMAIL>). */
( function( factory ) {
	if ( typeof define === "function" && define.amd ) {

		// AMD. Register as an anonymous module.
		define( [ "../widgets/datepicker" ], factory );
	} else {

		// Browser globals
		factory( jQuery.datepicker );
	}
}( function( datepicker ) {

datepicker.regional.rm = {
	closeText: "Serrar",
	prevText: "&#x3C;Suandant",
	nextText: "Precedent&#x3E;",
	currentText: "Actual",
	monthNames: [
		"<PERSON><PERSON><PERSON>",
		"Favrer",
		"<PERSON>",
		"Avrigl",
		"Matg",
		"Zer<PERSON>ladur",
		"Fanadur",
		"Avust",
		"Settember",
		"October",
		"November",
		"December"
	],
	monthNamesShort: [
		"<PERSON>ha",
		"Fev",
		"Mar",
		"Avr",
		"<PERSON>g",
		"<PERSON><PERSON>",
		"<PERSON>",
		"<PERSON><PERSON>",
		"<PERSON><PERSON>",
		"Oct",
		"Nov",
		"Dec"
	],
	dayNames: [ "<PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON><PERSON>","<PERSON><PERSON>","<PERSON><PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON>","Sonda" ],
	dayNamesShort: [ "Dum","Gli","Mar","Mes","Gie","Ven","Som" ],
	dayNamesMin: [ "Du","Gl","Ma","Me","Gi","Ve","So" ],
	weekHeader: "emna",
	dateFormat: "dd/mm/yy",
	firstDay: 1,
	isRTL: false,
	showMonthAfterYear: false,
	yearSuffix: "" };
datepicker.setDefaults( datepicker.regional.rm );

return datepicker.regional.rm;

} ) );
