/* Portuguese initialisation for the jQuery UI date picker plugin. */
( function( factory ) {
	if ( typeof define === "function" && define.amd ) {

		// AMD. Register as an anonymous module.
		define( [ "../widgets/datepicker" ], factory );
	} else {

		// Browser globals
		factory( jQuery.datepicker );
	}
}( function( datepicker ) {

datepicker.regional.pt = {
	closeText: "Fechar",
	prevText: "Anterior",
	nextText: "Seguinte",
	currentText: "Hoje",
	monthNames: [ "Janeiro","Fevereiro","Março","Abril","Maio","Jun<PERSON>",
	"Jul<PERSON>","Agosto","Setembro","Outubro","Novembro","Dezembro" ],
	monthNamesShort: [ "Jan","Fev","Mar","Abr","Mai","Jun",
	"Jul","Ago","Set","Out","Nov","Dez" ],
	dayNames: [
		"<PERSON>",
		"<PERSON><PERSON>feira",
		"<PERSON><PERSON><PERSON><PERSON>feira",
		"<PERSON><PERSON><PERSON>-feira",
		"<PERSON><PERSON><PERSON>-f<PERSON>",
		"<PERSON><PERSON>-feira",
		"<PERSON><PERSON><PERSON><PERSON>"
	],
	dayNamesShort: [ "Dom","Seg","Ter","<PERSON>ua","Qui","Sex","Sáb" ],
	dayNamesMin: [ "Dom","Seg","Ter","Qua","Qui","Sex","Sáb" ],
	weekHeader: "Sem",
	dateFormat: "dd/mm/yy",
	firstDay: 1,
	isRTL: false,
	showMonthAfterYear: false,
	yearSuffix: "" };
datepicker.setDefaults( datepicker.regional.pt );

return datepicker.regional.pt;

} ) );
