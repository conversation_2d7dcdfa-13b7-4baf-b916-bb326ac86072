a
{
	text-decoration:none;
	padding: 2px 4px 4px 6px;
	display : block;
	border-width: 1px;
	border-style: solid;
	margin : 0px;
}

a.cke_scayt_toogle:hover,
a.cke_scayt_toogle:focus,
a.cke_scayt_toogle:active
{
	border-color: #316ac5;
	background-color: #dff1ff;
	color : #000;
	cursor: pointer;
	margin : 0px;
}
a.cke_scayt_toogle {
	color : #316ac5;
	border-color: #fff;
}
.scayt_enabled a.cke_scayt_item {
	color : #316ac5;
	border-color: #fff;
	margin : 0px;
}
.scayt_disabled a.cke_scayt_item {
	color : gray;
	border-color : #fff;
}
.scayt_enabled a.cke_scayt_item:hover,
.scayt_enabled a.cke_scayt_item:focus,
.scayt_enabled a.cke_scayt_item:active
{
	border-color: #316ac5;
	background-color: #dff1ff;
	color : #000;
	cursor: pointer;
}
.scayt_disabled a.cke_scayt_item:hover,
.scayt_disabled a.cke_scayt_item:focus,
.scayt_disabled a.cke_scayt_item:active
{
	border-color: gray;
	background-color: #dff1ff;
	color : gray;
	cursor: no-drop;
}
.cke_scayt_set_on, .cke_scayt_set_off
{
	display: none;
}
.scayt_enabled .cke_scayt_set_on
{
	display: none;
}
.scayt_disabled .cke_scayt_set_on
{
	display: inline;
}
.scayt_disabled .cke_scayt_set_off
{
	display: none;
}
.scayt_enabled  .cke_scayt_set_off
{
	display: inline;
}
