<?php

namespace Database\Factories;

use App\LocalisedMotif;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\LocalisedMotif>
 */
class LocalisedMotifFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = LocalisedMotif::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $locations = [
            'Heraklion' => 'The capital and largest city of Crete, known for its rich history and vibrant culture.',
            'Heraklion Airport' => 'The main international airport serving Heraklion and northern Crete.',
            'Chania' => 'A beautiful coastal city in western Crete, famous for its Venetian harbor and old town.',
            'Chania Airport' => 'International airport serving the Chania region in western Crete.',
            'Rethymno' => 'A picturesque town in northern Crete, known for its well-preserved Renaissance and Ottoman architecture.',
            'Agios Nikolaos' => 'A charming coastal town in eastern Crete, built around the scenic Lake Voulismeni.',
            'Ierapetra' => 'The southernmost city in Europe, known for its beautiful beaches and warm climate.',
            'Sitia' => 'A peaceful town in eastern Crete, famous for its archaeological sites and traditional atmosphere.',
            'Kissamos' => 'A coastal town in western Crete, gateway to beautiful beaches and ancient sites.',
            'Malia' => 'A popular resort town known for its nightlife and ancient Minoan palace ruins.'
        ];

        $location = $this->faker->randomElement(array_keys($locations));

        return [
            'title' => $location,
            'description' => $locations[$location],
        ];
    }

    /**
     * Create a factory state for Heraklion.
     */
    public function heraklion(): static
    {
        return $this->state(fn (array $attributes) => [
            'title' => 'Heraklion',
            'description' => 'The capital and largest city of Crete, known for its rich history and vibrant culture.',
        ]);
    }

    /**
     * Create a factory state for Chania.
     */
    public function chania(): static
    {
        return $this->state(fn (array $attributes) => [
            'title' => 'Chania',
            'description' => 'A beautiful coastal city in western Crete, famous for its Venetian harbor and old town.',
        ]);
    }

    /**
     * Create a factory state for airports.
     */
    public function airport(): static
    {
        $airports = [
            'Heraklion Airport' => 'The main international airport serving Heraklion and northern Crete.',
            'Chania Airport' => 'International airport serving the Chania region in western Crete.',
        ];

        $airport = $this->faker->randomElement(array_keys($airports));

        return $this->state(fn (array $attributes) => [
            'title' => $airport,
            'description' => $airports[$airport],
        ]);
    }

    /**
     * Create a factory state for coastal towns.
     */
    public function coastal(): static
    {
        $coastalTowns = [
            'Chania' => 'A beautiful coastal city in western Crete, famous for its Venetian harbor and old town.',
            'Agios Nikolaos' => 'A charming coastal town in eastern Crete, built around the scenic Lake Voulismeni.',
            'Ierapetra' => 'The southernmost city in Europe, known for its beautiful beaches and warm climate.',
            'Sitia' => 'A peaceful town in eastern Crete, famous for its archaeological sites and traditional atmosphere.',
        ];

        $town = $this->faker->randomElement(array_keys($coastalTowns));

        return $this->state(fn (array $attributes) => [
            'title' => $town,
            'description' => $coastalTowns[$town],
        ]);
    }
}
