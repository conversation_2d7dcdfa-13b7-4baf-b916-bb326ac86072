<?php

namespace Database\Seeders;

use App\LocalisedMotif;
use Carbon\Carbon;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class LocalisedMotifsTableSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        DB::table('localised_motifs')->delete();

        $motifs = [
            [
                'title' => 'Heraklion',
                'description' => 'The capital and largest city of Crete, known for its rich history and vibrant culture.'
            ],
            [
                'title' => 'Heraklion Airport',
                'description' => 'The main international airport serving Heraklion and northern Crete.'
            ],
            [
                'title' => 'Chania',
                'description' => 'A beautiful coastal city in western Crete, famous for its Venetian harbor and old town.'
            ],
            [
                'title' => 'Chania Airport',
                'description' => 'International airport serving the Chania region in western Crete.'
            ],
            [
                'title' => 'Rethymno',
                'description' => 'A picturesque town in northern Crete, known for its well-preserved Renaissance and Ottoman architecture.'
            ],
            [
                'title' => 'Agios Nikolaos',
                'description' => 'A charming coastal town in eastern Crete, built around the scenic Lake Voulismeni.'
            ]
        ];

        foreach ($motifs as $motif) {
            LocalisedMotif::create([
                'title' => $motif['title'],
                'description' => $motif['description'],
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ]);
        }

        $this->command->info('Localised motifs table seeded!');
    }
}
