<?php

namespace Database\Seeders;

use App\Accessory;
use App\Coupon;
use App\Group;
use App\Listing;
use App\Location;
use App\Period;
use App\User;
use App\Post;
use App\Language;
use App\LanguageTranslation;
use App\Country;
use App\CountryTranslation;
use Carbon\Carbon;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use App\Customer;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        Eloquent::unguard();

        $this->call('GroupTableSeeder');
        $this->call('UserTableSeeder');
        $this->call('ListingTableSeeder');
        $this->call('AccessoryTableSeeder');
        $this->call('LocationTableSeeder');
        $this->call('CouponTableSeeder');
        $this->call('GroupToGroupTableSeeder');
        $this->call('PeriodTableSeeder');
        $this->call('GroupPeriodTableSeeder');
        $this->call('GroupBasePriceTableSeeder');
        $this->call('PostsTableSeeder');
        $this->call('LanguageTableSeeder');
        $this->call('CountryTableSeeder');
        $this->call('CustomerCountryMigration');
        $this->call('LocalisedMotifsTableSeeder');
    }

}

class UserTableSeeder extends Seeder
{

    public function run()
    {
        DB::table('users')->delete();

        User::create(
            array(
                'username' => 'bmenekl',
                'email' => '<EMAIL>',
                'password' => Hash::make('123456')
            )
        );
        User::create(
            array(
                'username' => 'pepina',
                'email' => '<EMAIL>',
                'password' => Hash::make('123456'),
                'role' => 'super-admin'
            )
        );

        $this->command->info('User table seeded!');
    }

}

class ListingTableSeeder extends Seeder
{

    public function run()
    {
        DB::table('listings')->delete();

        Listing::create(
            array(
                'manufacturer' => 'Mercedes',
                'model' => 'CLK 200',
                'description' => 'Souzie',
                'group_id' => '8',
                'engine' => '1800',
                'consumption' => '10',
                'doors' => '2',
                'airbags' => '8',
                'discount_price' => 15,
                'low_price' => 18,
                'medium_price' => 22,
                'high_price' => 30,
                'our_cars_price' => 8,
                'clima' => true,
                'abs' => true,
                'four_wheeled' => false,
                'sunroof' => false,
                'radio_cd' => true,
            )
        );
        Listing::create(
            array(
                'manufacturer' => 'Mercedes',
                'model' => 'CLK 200',
                'description' => 'TSouzie',
                'group_id' => '8',
                'engine' => '2000',
                'consumption' => '12',
                'doors' => '2',
                'airbags' => '8',
                'discount_price' => 15,
                'low_price' => 18,
                'medium_price' => 22,
                'high_price' => 30,
                'our_cars_price' => 7,
                'clima' => true,
                'abs' => true,
                'four_wheeled' => true,
                'sunroof' => false,
                'radio_cd' => true,
            )
        );
        Listing::create(
            array(
                'manufacturer' => 'Land Rover',
                'model' => 'Freelander',
                'description' => 'Frixos',
                'group_id' => '3',
                'engine' => '1796',
                'consumption' => '11',
                'doors' => '5',
                'airbags' => '4',
                'discount_price' => 14,
                'low_price' => 18,
                'medium_price' => 20,
                'high_price' => 35,
                'our_cars_price' => 9,
                'clima' => true,
                'abs' => true,
                'four_wheeled' => true,
                'sunroof' => false,
                'radio_cd' => false,
            )
        );
        Listing::create(
            array(
                'manufacturer' => 'Volkswagen',
                'model' => 'Polo',
                'description' => 'Markos',
                'group_id' => '1',
                'engine' => '1400',
                'consumption' => '9',
                'doors' => '3',
                'airbags' => '8',
                'discount_price' => 10,
                'low_price' => 12,
                'medium_price' => 14,
                'high_price' => 20,
                'our_cars_price' => 5,
                'clima' => true,
                'abs' => true,
                'four_wheeled' => false,
                'sunroof' => false,
                'radio_cd' => true,
            )
        );

        $this->command->info('Listing table seeded!');
    }

}

class AccessoryTableSeeder extends Seeder
{

    public function run()
    {
        DB::table('accessories')->delete();

        Accessory::create(
            array(
                'name' => 'GPS',
                'description' => 'Garmin 7666',
                'price' => 100
            )
        );
        Accessory::create(
            array(
                'name' => 'Baby car seat',
                'description' => 'Tobi',
                'price' => 150
            )
        );

        $this->command->info('Accessory table seeded!');
    }

}

class LocationTableSeeder extends Seeder
{

    public function run()
    {
        DB::table('locations')->delete();

        Location::create(
            array(
                'name' => 'Heraklion airport',
                'area' => 'heraklion',
            )
        );
        Location::create(
            array(
                'name' => 'Heraklion port',
                'area' => 'heraklion',
            )
        );
        Location::create(
            array(
                'name' => 'Heraklion downtown',
                'area' => 'heraklion',
            )
        );
        Location::create(
            array(
                'name' => 'Heraklion Agia Pelagia',
                'area' => 'heraklion',
            )
        );
        Location::create(
            array(
                'name' => 'Heraklion Ammoudara',
                'area' => 'heraklion',
            )
        );
        Location::create(
            array(
                'name' => 'Heraklion Analipsi',
                'area' => 'heraklion',
            )
        );
        Location::create(
            array(
                'name' => 'Heraklion Annissaras',
                'area' => 'heraklion',
            )
        );
        Location::create(
            array(
                'name' => 'Heraklion Chersonisos',
                'area' => 'heraklion',
            )
        );
        Location::create(
            array(
                'name' => 'Heraklion Fodele',
                'area' => 'heraklion',
            )
        );
        Location::create(
            array(
                'name' => 'Heraklion Gournes',
                'area' => 'heraklion',
            )
        );
        Location::create(
            array(
                'name' => 'Heraklion Gouves',
                'area' => 'heraklion',
            )
        );
        Location::create(
            array(
                'name' => 'Heraklion Kokkini Hani',
                'area' => 'heraklion',
            )
        );
        Location::create(
            array(
                'name' => 'Heraklion Koutouloufari',
                'area' => 'heraklion',
            )
        );
        Location::create(
            array(
                'name' => 'Heraklion Lygaria',
                'area' => 'heraklion',
            )
        );
        Location::create(
            array(
                'name' => 'Heraklion Malia',
                'area' => 'heraklion',
            )
        );
        Location::create(
            array(
                'name' => 'Heraklion Piskopiano',
                'area' => 'heraklion',
            )
        );
        Location::create(
            array(
                'name' => 'Heraklion Stalis',
                'area' => 'heraklion',
            )
        );
        Location::create(
            array(
                'name' => 'Chania airport',
                'area' => 'chania',
            )
        );
        Location::create(
            array(
                'name' => 'Chania port',
                'area' => 'chania',
            )
        );
        Location::create(
            array(
                'name' => 'Chania downtown',
                'area' => 'chania',
            )
        );
        Location::create(
            array(
                'name' => 'Chania Agia Marina',
                'area' => 'chania',
            )
        );
        Location::create(
            array(
                'name' => 'Chania Kolimpari',
                'area' => 'chania',
            )
        );
        Location::create(
            array(
                'name' => 'Chania Platanias',
                'area' => 'chania',
            )
        );
        Location::create(
            array(
                'name' => 'Rethymno port',
                'area' => 'rethymno',
            )
        );
        Location::create(
            array(
                'name' => 'Rethymno downtown',
                'area' => 'rethymno',
            )
        );
        Location::create(
            array(
                'name' => 'Rethymno Bali',
                'area' => 'rethymno',
            )
        );
        Location::create(
            array(
                'name' => 'Rethymno Georgioupolis',
                'area' => 'rethymno',
            )
        );
        Location::create(
            array(
                'name' => 'Rethymno Kavros',
                'area' => 'rethymno',
            )
        );
        Location::create(
            array(
                'name' => 'Rethymno Panormo',
                'area' => 'rethymno',
            )
        );
        Location::create(
            array(
                'name' => 'Rethymno Platanias',
                'area' => 'rethymno',
            )
        );
        Location::create(
            array(
                'name' => 'Rethymno Scaleta',
                'area' => 'rethymno',
            )
        );
        Location::create(
            array(
                'name' => 'Lassithi Agios Nikolaos port',
                'area' => 'lassithi',
            )
        );
        Location::create(
            array(
                'name' => 'Lassithi Agios Nikolaos downtown',
                'area' => 'lassithi',
            )
        );
        Location::create(
            array(
                'name' => 'Lassithi Elounda',
                'area' => 'lassithi',
            )
        );
        Location::create(
            array(
                'name' => 'Lassithi Sissi',
                'area' => 'lassithi',
            )
        );

        $this->command->info('Location table seeded!');
    }
}

class CouponTableSeeder extends Seeder
{

    public function run()
    {
        // \App\Models\User::factory(10)->create();

        // \App\Models\User::factory()->create([
        //     'name' => 'Test User',
        //     'email' => '<EMAIL>',
        // ]);
    }
}
