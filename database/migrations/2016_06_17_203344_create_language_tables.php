<?php

use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateLanguageTables extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        
        Schema::table('languages', function($table) {
            $table->index('locale');
        });
        
        Schema::create('listings_translations', function (Blueprint $table) {
            $table->increments('id');
			$table->string('locale', 2)->index();
			$table->integer('listing_id')->unsigned()->nullable();
	        $table->text('description');
	        $table->unique(['listing_id','locale']);
			$table->foreign('locale')->references('locale')->on('languages')->onDelete('cascade');
			$table->foreign('listing_id')->references('id')->on('listings')->onDelete('cascade');
        });
        
        Schema::create('accessories_translations', function (Blueprint $table) {
            $table->increments('id');
			$table->string('locale', 2)->index();
			$table->integer('accessory_id')->unsigned()->nullable();
	        $table->string('name', 100);
	        $table->text('description');
	        $table->unique(['accessory_id','locale']);
			$table->foreign('locale')->references('locale')->on('languages')->onDelete('cascade');
			$table->foreign('accessory_id')->references('id')->on('accessories')->onDelete('cascade');
        });
        
        Schema::create('groups_translations', function (Blueprint $table) {
            $table->increments('id');
			$table->string('locale', 2)->index();
			$table->integer('group_id')->unsigned()->nullable();
	        $table->text('description');
	        $table->text('seo_text');
	        $table->unique(['group_id','locale']);
			$table->foreign('locale')->references('locale')->on('languages')->onDelete('cascade');
			$table->foreign('group_id')->references('id')->on('groups')->onDelete('cascade');
        });
        
        Schema::create('locations_translations', function (Blueprint $table) {
            $table->increments('id');
			$table->string('locale', 2)->index();
			$table->integer('location_id')->unsigned()->nullable();
			$table->string('name', 200);
	        $table->unique(['location_id','locale']);
			$table->foreign('locale')->references('locale')->on('languages')->onDelete('cascade');
			$table->foreign('location_id')->references('id')->on('locations')->onDelete('cascade');
        });
        
        Schema::create('languages_translations', function (Blueprint $table) {
            $table->increments('id');
			$table->string('locale', 2)->index();
			$table->integer('language_id')->unsigned()->nullable();
			$table->string('text', 50);
	        $table->unique(['language_id','locale']);
			$table->foreign('locale')->references('locale')->on('languages')->onDelete('cascade');
			$table->foreign('language_id')->references('id')->on('languages')->onDelete('cascade');
        });
        
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
		Schema::drop('languages_translations');
		Schema::drop('listings_translations');
		Schema::drop('accessories_translations');
		Schema::drop('groups_translations');
		Schema::drop('locations_translations');
    	Schema::table('languages', function($table) {
    		$table->dropIndex('languages_locale_index');
    	});
    }
}
