<?php

use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateListingsTable extends Migration {

	/**
	 * Run the migrations.
	 *
	 * @return void
	 */
	public function up()
	{
		Schema::create('listings', function($table)
	    {
	        $table->increments('id');
	        $table->string('manufacturer', 100);
	        $table->string('model', 100);
	        $table->text('description');
	        $table->integer('engine');
			$table->enum('fuel', ['petrol', 'diesel'])->default('petrol');
	        $table->decimal('consumption', 4, 2);
	        $table->integer('doors');
	        $table->integer('capacity');
	        $table->integer('driver_age');
	        $table->integer('discount_price');
	        $table->integer('low_price');
	        $table->integer('medium_price');
	        $table->integer('high_price');
			$table->enum('transmission', ['manual', 'automatic'])->default('manual');
	        $table->integer('airbags');
			$table->boolean('four_wheeled');
			$table->boolean('abs');
			$table->boolean('radio_cd');
			$table->boolean('clima');
			$table->boolean('sunroof');
			$table->string('image', 255)->nullable(true);
			$table->string('slug', 255)->unique()->index();
	        $table->timestamps();
	    });
		
	}

	/**
	 * Reverse the migrations.
	 *
	 * @return void
	 */
	public function down()
	{
		Schema::drop('listings');
	}

}
