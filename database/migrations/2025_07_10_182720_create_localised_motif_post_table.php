<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('localised_motif_post', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('localised_motif_id');
            $table->unsignedInteger('post_id');
            $table->timestamps();

            $table->foreign('localised_motif_id')
                ->references('id')
                ->on('localised_motifs')
                ->onDelete('cascade');
            $table->foreign('post_id')
                ->references('id')
                ->on('posts')
                ->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('localised_motif_post');
    }
};
