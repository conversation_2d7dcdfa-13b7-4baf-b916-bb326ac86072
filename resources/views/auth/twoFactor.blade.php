@extends('layouts.frontend.base')

@section('body')

    @if(session()->has('message'))
        <p class="alert alert-info">
            {{ session()->get('message') }}
        </p>
    @endif
    <form class="main" method="POST" action="{{ route('verify.store') }}">
        {{ csrf_field() }}

        <section class=" stripes padding-large">
            <div class="container">
                <div class="desktop-6 offset-3 columns">
                    <h1>Two Factor Verification</h1>
                    <p class="text-muted">
                        You have received an email which contains a two factor verification code.
                        If you haven't received it, <a class="text cyan" href="{{ route('verify.resend') }}">click here to resend the email</a>.
                    </p>

                    <div class="input-group mb-3 margin-top">
                        <input name="two_factor_code" type="text"
                               class="form-control{{ $errors->has('two_factor_code') ? ' is-invalid' : '' }}"
                               required autofocus placeholder="Two Factor Code">
                        @if($errors->has('two_factor_code'))
                            <div class="invalid-feedback">
                                {{ $errors->first('two_factor_code') }}
                            </div>
                        @endif
                    </div>

                    <div class="row">
                        <div class="col-6 margin-top">
                            <input type="submit" class="button button-theme margin-top-small condensed" value="Verify">
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </form>
@stop
