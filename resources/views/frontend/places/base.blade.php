@extends('layouts.frontend.base')

@section('head_extra')
    {{-- <link rel="stylesheet" type="text/css" href="{!! asset('css/ratings.css') !!}?v={!! config('app.styles_version'); !!}"> --}}
    <link rel="canonical" href="{{ $canonical_url }}"/>
@stop

@section('body')
    <section data-aos="fade-up" data-aos-duration="1500" data-aos-once="true"
             class="section section-hero background-image section-hero-w-form location-hero"
             style="background: url({!! asset($background_image) !!}">
        <div class="container container-1291">
            <div class="hero-txt">
                <h1>@yield('main_heading')</h1>
                <p>@yield('sub_heading')</p>
            </div>
        </div>
    </section>

    <section class="section section-search-form margin-top" data-aos="fade-up" data-aos-duration="1500"
             data-aos-once="true">
        {!!  Form::open(array('url' => route('booking.handle'), 'class'=> ['main', 'search-form'], 'id' => 'booking_form')) !!}
        @include('frontend.listings.partials._search_form_homepage', array('priceable' => false))
        {!!  Form::close() !!}
    </section>

    <section class="section" data-aos="fade-up" data-aos-duration="1500" data-aos-once="true">
        <div class="container">
            <div class="breadcrumbs" data-aos="fade-up" data-aos-duration="1500" data-aos-once="true">
                <a href="{!! route('home') !!}">
                    <img src="{!! asset('images/home.svg') !!}"
                         alt="{!! trans('breadcrumbs.home') !!}"/> {!! trans('breadcrumbs.home') !!}
                </a>
                <span>‣</span>
                @yield('breadcrumbs_completion')

                {!! $breadcrumbs_schema !!}
            </div>
        </div>
    </section>

    @include('frontend.partials._featuredListings', ['show_button' => true])

    @yield('body_place_text')

    @yield('useful_info')

    @include('frontend.partials._meet_greet')

    @yield('location_faq')

    @yield('location_tips')

    @include('frontend.partials._reviews')

    <div class="force-select2" style="display:none;" data-element_id="pickup_location"
         data-value="{{ $location_default }}"></div>
    <div class="force-select2" style="display:none;" data-element_id="dropoff_location"
         data-value="{{ $location_default }}"></div>

    {!! $local_business_schema_localised_pages !!}
@stop


