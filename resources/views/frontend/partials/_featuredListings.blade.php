@if (count($featuredListings) > 0)
    <section class="section section-car-slider @if ($show_button === false) section-car-slider--no-button @endif"
        data-aos="fade-up" data-aos-duration="1500" data-aos-once="true">
        <div class="container container-1291">
            <div class="car-slider--top">
                <h2>
                    @section('featured_heading'){!! Lang::get('home.featured_listings') !!}@show
                        @if ($show_button === true)
                            <a class="check-fleet d-block d-md-none" href="{!! LaravelLocalization::getLocalizedURL(App::getLocale(), URL::route('listings.index')) !!}">{!! Lang::get('listings.featured_listings_slider.check_fleet') !!}
                                <svg xmlns="http://www.w3.org/2000/svg" width="5.603" height="9.299"
                                    viewBox="0 0 5.603 9.299">
                                    <path id="Path_398" data-name="Path 398"
                                        d="M11146.562,3882.394l-3.189,4.649h2.227l3.376-4.528v-.243l-3.376-4.528h-2.227Z"
                                        transform="translate(-11143.372 -3877.745)" />
                                </svg></a>
                        @endif
                    </h2>
                </div>
                <div class="car-slider-container jsCarSlider">
                    @foreach ($featuredListings as $featuredListing)
                        <div class="car-slider-item">
                            <div class="badges">
                                @if ($featuredListing->group->on_offer)
                                    <div class="badge-item offer">
                                        <img src="{!! asset('images/icons/hot-offer.svg') !!}" alt="on offer badge" /> <span>HOT OFFER</span>
                                    </div>
                                @endif
                                @if ($featuredListing->popular)
                                    <div class="badge-item popular">
                                        <img src="{!! asset('images/icons/best-seller.svg') !!}" alt="popular badge" /> <span> BEST SELLER</span>
                                    </div>
                                @endif
<?php /*                                @if (1 == 1)
                                    <div class="badge-item eco">
                                        <img src="{!! asset('images/icons/eco-friendly.svg') !!}" alt="eco friendly" /> <span> ECO FRIENDLY</span>
                                    </div>
                                @endif
 */ ?>
                            </div>

                            <?php $main_photo = $featuredListing->getFirstMedia('photos'); ?>
                            <div class="car-slider-item__img">
                            @if(!empty($main_photo))
                                {{ $main_photo('thumb') }}
                            @endif
                            </div>
                            <div class="car-slider-info">
                                <div class="car-slider-info--left">
                                    <span class="car-group">Class: Group {!! $featuredListing->group->name !!}</span>
                                    <h3>{!! $featuredListing->SEOshortTitle !!}</h3>

                                    <div class="car-props">
                                        {{-- <div class="car-desc--left">
                                    <span>{!! trans('common.seats') !!}:</span>
                                    <span>CC:</span>
                                    <span>{{ trans('listings.transmission') }}:</span>
                                    @if ($featuredListing->clima)<span>{{ trans('listings.clima') }}:</span>@endif
                                </div> --}}

                                        <div class="car-prop-item">
                                            <img src="{!! asset('images/icons/passenger.svg') !!}" alt="passengers icon" />
                                            <span>{!! $featuredListing->seats !!}</span>
                                        </div>
                                        <div class="car-prop-item">
                                            <img src="{!! asset('images/icons/engine_cc.svg') !!}" alt="fuel icon" />
                                            <span>{!! $featuredListing->engine !!}</span>

                                        </div>
                                        <div class="car-prop-item">
                                            <img src="{!! asset('images/icons/gear-shift.svg') !!}" alt="transmission icon" />
                                            <span>{!! $featuredListing->transmission !!}</span>

                                        </div>
                                        @if ($featuredListing->clima)
                                            <div class="car-prop-item">
                                                <img src="{!! asset('images/icons/snowflake.svg') !!}" alt="fuel icon" />
                                                <span>{{ trans('common.yes') }}</span>
                                            </div>
                                        @endif



                                    </div>
                                </div>
                                <div class="car-slider-info--right">
                                    @if ($featuredListing->group->base_price)
                                        <span class="car-from">{!! Lang::get('listings.from') !!}</span>
                                        <div class="car-slider-info--right__wrapper">

                                            <span class="car-price">{!! $featuredListing->group->base_price !!}<sup>&euro;</sup></span>
                                            <span class="car-period">{!! Lang::get('listings.per_day') !!}</span>
                                        </div>
                                    @endif
                                    <a class="btn btn-yellow" href="{!! route('listings.show', [$featuredListing->slug, 'ref_' => $url_ref]) !!}">{!! trans('listings.featured_listings_slider.rent_it') !!}</a>
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>
            </div>
        </section>
    @endif
