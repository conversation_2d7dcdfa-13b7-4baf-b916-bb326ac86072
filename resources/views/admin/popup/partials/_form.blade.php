
'',
<section class="padding-small">
	<div class="container">
        <div class="row">
            <div class="desktop-4 columns">
                <p>{!! Form::label('title', 'Title:') !!}</p>
                <p>{!! Form::text('title') !!}</p>
                <p>{!! $errors->first('title') !!}</p>
            </div>
        </div>
        <div class="desktop-2 columns padding-top-tiny"  >
            {!! Form::label('start_date', 'Start date: ') !!}
            {!! Form::text('start_date', isset($popup) && !empty($popup) ? $popup->start_date : '', ['class' => 'datepicker_from']) !!}
        </div>
        <div class="desktop-2 columns padding-top-tiny"  >
            {!! Form::label('end_date', 'End date') !!}
            {!! Form::text('end_date', isset($popup) && !empty($popup) ? $popup->end_date : '', ['class' => 'datepicker_from']) !!}
        </div>
        <div class="row">
            <div class="desktop-3 columns">
                <p>{!! Form::label('active', 'Active:') !!}</p>
                <p>{!! Form::select('active', ['1' => 'Yes', '0' => 'No']) !!}</p>
                <p>{!! $errors->first('active') !!}</p>
            </div>
            <div class="desktop-2 columns right"    >
                @if (isset($popup) && !empty($popup->image))
                    <img src="{!! asset($popup->image) !!}" alt="{!! $popup->id !!}" class="search_image" style="width:130px;">
                @endif
            </div>
            <div class="desktop-3 columns"    >
                <p>{!! Form::label('image', 'Image:') !!}</p>
                <p>{!! Form::file('image') !!}</p>
            </div>
        </div>
	</div>
</section>

<section class=" padding-small  "    >
    <div class="container">
        <div class="desktop-9 columns"    >
            <p>{!! Form::label('content', 'Content:') !!}</p>
            <p>{!! Form::textarea('content') !!}</p>
            <span class="error">{!! $errors->first('content') !!}</span>
        </div>
    </div>
</section>

<section class="padding">
	<div class="container">
		<div class="desktop-4 offset-4 columns">
    		<p>{!! Form::submit('Save', ['class' => 'button']) !!}</p>
		</div>
 
	</div>
</section>
{!! Form::hidden('maxFileSize', $upload_max_filesize, $attributes = ['id' => 'maxFileSize']); !!}
{!! Form::hidden('maxFileSizeShort', $upload_max_filesize_short, $attributes = ['id' => 'maxFileSizeShort']); !!}











 