@extends('layouts.admin.base')

@section('head_extra')
	<link rel="stylesheet" type="text/css" href="{!! asset('css/pagination.css') !!}">
@stop


@section('body')


<section class="padding-small stripes">
	<div class="container ">
		<div class="desktop-12    columns   "  >
			<h1 class="text bold condensed">Reservations - {!! ucfirst($reservations_site_dropdown_options[$siteSelected])  !!}</h1>
		</div>
    </div>
</section>

{!! Form::open(array('route' => 'reservations', 'method' => 'get',  'class' => 'main')) !!}
<section class="padding-small admin_search">
	<div class="container ">
		<div class="desktop-6 columns"  >
			{!! Form::label('google_it', 'Search for customers ( name, email or phone )') !!}
			{!! Form::text('google_it', $searchAttributes_google_it) !!}
		</div>
		<div class="desktop-2 columns"  >
			{!! Form::label('site', 'SITE',['style' => 'margin-bottom:3px;']) !!}
			{!! Form::select('site', $reservations_site_dropdown_options, $siteSelected) !!}
		</div>
		<div class="desktop-2 columns padding-top"  >
			{!! Form::button('Clear', ['class' => 'button button-small button-delete clear-form']) !!}
		</div>
		<div class="desktop-2 columns padding-top"  >
			{!! Form::submit('Search', ['name' => 'g_form', 'class' => 'button button-small']) !!}
		</div>
    </div>
</section>	
{!! Form::close() !!}

{!! Form::open(array('route' => 'reservations', 'method' => 'get',  'class' => 'main')) !!}
<section class="padding-small stripes admin_search">
	<div class="container ">
		<div class="desktop-2 columns padding-top-tiny"  >
			{!! Form::label('id', 'Reservation ID') !!}
			{!! Form::text('id', $searchAttributes_id) !!}
		</div>
		<div class="desktop-2 columns padding-top-tiny"  >
			{!! Form::label('price_low', 'Price from') !!}
			{!! Form::text('price_low', $searchAttributes_price_low) !!}
		</div>
		<div class="desktop-2 columns padding-top-tiny"  >
			{!! Form::label('price_high', 'Price to') !!}
			{!! Form::text('price_high', $searchAttributes_price_high) !!}
		</div>
    </div>
	<div class="container ">
		<div class="desktop-2 columns padding-top-tiny"  >
			{!! Form::label('listing_model', 'Listing model') !!}
			{!! Form::select('listing_model',  ['' => 'All'] + $reservations_listing_model, $searchAttributes_listing_model) !!}
		</div>
		<div class="desktop-2 columns padding-top-tiny"  >
			{!! Form::label('listing_group', 'Listing group') !!}
			{!! Form::select('listing_group', ['' => 'All'] + $reservations_listing_group, $searchAttributes_listing_group) !!}
		</div>
		<div class="desktop-2 columns padding-top-tiny"  >
			{!! Form::label('country', 'Country') !!}
			{!! Form::select('country', ['' => 'All'] + $reservations_countries, $searchAttributes_country) !!}
		</div>
		<div class="desktop-2 columns padding-top-tiny"  >
			{!! Form::label('pickup_location', 'Pickup') !!}
			{!! Form::select('pickup_location', ['' => 'Select location'] + $locations_dropdown_pickup, $searchAttributes_pickup_location) !!}
		</div>
		<div class="desktop-2 columns padding-top-tiny"  >
			{!! Form::label('dropoff_location', 'Dropoff') !!}
			{!! Form::select('dropoff_location', ['' => 'Select location'] + $locations_dropdown_pickup, $searchAttributes_dropoff_location) !!}
		</div>
    </div>
	<div class="container ">
		<div class="desktop-2 columns padding-top-tiny"  >
			{!! Form::label('pickup_date_low', 'Pickup date from') !!}
			{!! Form::text('pickup_date_low', $searchAttributes_pickup_date_low, ['class' => 'datepicker_from']) !!}
		</div>
		<div class="desktop-2 columns padding-top-tiny"  >
			{!! Form::label('pickup_date_high', 'Pickup date to') !!}
			{!! Form::text('pickup_date_high', $searchAttributes_pickup_date_high, ['class' => 'datepicker_from']) !!}
		</div>
		<div class="desktop-2 columns padding-top-tiny"  >
			{!! Form::label('dropoff_date_low', 'Dropoff date from') !!}
			{!! Form::text('dropoff_date_low', $searchAttributes_dropoff_date_low, ['class' => 'datepicker_from']) !!}
		</div>
		<div class="desktop-2 columns padding-top-tiny"  >
			{!! Form::label('dropoff_date_high', 'Dropoff date to') !!}
			{!! Form::text('dropoff_date_high', $searchAttributes_dropoff_date_high, ['class' => 'datepicker_from']) !!}
		</div>
		<div class="desktop-2 columns padding-top-tiny"  >
			{!! Form::label('created_at_low', 'Created from') !!}
			{!! Form::text('created_at_low', $searchAttributes_created_at_low, ['class' => 'datepicker_from']) !!}
		</div>
		<div class="desktop-2 columns padding-top-tiny"  >
			{!! Form::label('created_at_high', 'Created to') !!}
			{!! Form::text('created_at_high', $searchAttributes_created_at_high, ['class' => 'datepicker_from']) !!}
		</div>
		<div class="desktop-2 columns padding-top-tiny"  >
			{!! Form::label('show', 'Show') !!}
			{!! Form::select('show', ['' => 'All', 'show' => 'Show', 'no show' => 'No show'], $searchAttributes_show) !!}
		</div>
		<div class="desktop-2 columns padding-top-tiny"  >
			{!! Form::label('status', 'Status') !!}
			{!! Form::select('status', ['' => 'All', 'pending' => 'Pending', 'scheduled' => 'Scheduled'], $searchAttributes_status) !!}
		</div>
		<div class="desktop-2 columns padding-top-tiny"  >
			{!! Form::label('after_hours_charge', 'After hours charge') !!}
			{!! Form::select('after_hours_charge', ['' => 'All', '1' => 'Yes', '0' => 'No'], $searchAttributes_after_hours_charge) !!}
		</div>
		<div class="desktop-2 columns padding-top-tiny"  >
			{!! Form::label('fuel_plan_charge', 'Fuel plan charge') !!}
			{!! Form::select('fuel_plan_charge', ['' => 'All', '1' => 'Yes', '0' => 'No'], $searchAttributes_fuel_plan_charge) !!}
		</div>
		<div class="desktop-2 columns padding-top-tiny"  >
			{!! Form::label('extra_day_charge', 'Extra day charge') !!}
			{!! Form::select('extra_day_charge', ['' => 'All', '1' => 'Yes', '0' => 'No'], $searchAttributes_extra_day_charge) !!}
		</div>
		<div class="desktop-2 columns padding-top-tiny"  >
			{!! Form::label('extra_miles_charge', 'Extra miles charge') !!}
			{!! Form::select('extra_miles_charge', ['' => 'All', '1' => 'Yes', '0' => 'No'], $searchAttributes_extra_miles_charge) !!}
		</div>
		<div class="desktop-2 columns padding-top-tiny"  >
			{!! Form::label('discount_coupon', 'Discount coupon used') !!}
			{!! Form::select('discount_coupon', ['' => 'All', '1' => 'Yes', '0' => 'No'], $searchAttributes_discount_coupon) !!}
		</div>
		<div class="desktop-2 columns padding-top-tiny"  >
			{!! Form::label('site', 'SITE') !!}
			{!! Form::select('site', $reservations_site_dropdown_options, $siteSelected) !!}
		</div>
    </div>
	<div class="container ">
		<div class="desktop-2 offset-8 columns padding-top-small"  >
			{!! Form::button('Clear', ['class' => 'button button-small button-delete clear-form']) !!}
		</div>
		<div class="desktop-2 columns padding-top-small"  >
			{!! Form::submit('Search', ['name' => 's_form', 'class' => 'button button-small']) !!}
		</div>
    </div>
</section>	
{!! Form::close() !!}

<section class="padding-small">
		<div class="container ">
			<div class="desktop-12 columns padding-small"  >
				<strong class="text grey pull-left padding-top-small">
					{{$reservations->firstItem()}}-{{$reservations->lastItem()}} of {{$reservations->total()}} total result<?php echo ($reservations->total() === 1 ? '' : 's');?>
				</strong>
				<span class="pull-right pagination-top">
					{!! $reservations->appends(request()->except('page'))->render() !!}
				</span>
				<div class="clear"></div>
			</div>
	    </div>
        @foreach ($reservations as $reservation)
            @include('admin.reservations.partials.reservation_list_item')
		@endforeach
	<div class="text center">
		{!! $reservations->appends(request()->except('page'))->render() !!}
	</div>
</section>
@stop