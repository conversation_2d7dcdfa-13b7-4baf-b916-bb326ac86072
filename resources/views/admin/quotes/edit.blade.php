@extends('layouts.admin.base')

@section('head_extra')
<style>
@media print {
.hide-print {display:none;}
}
</style>
@stop  

@section('body')

<section class="padding stripes">
	<div class="container">
        <div class="desktop-8 columns">
            <h1 class="text bold condensed">QUOTE ID: {!! $quote->id !!} - {!! $quote->title !!}</h1>
			<h2 class="text condensed bold margin-top"><span class="badge badge_large badge_site_{!! $quote->site !!}">{!! $quote_site !!}</span></h2>
        </div>
	 
	   <div class="desktop-4 text right columns">
			<h3 class="text bold condensed hide-print"><a href="javascript:window.print()"><i class="fa fa-print"></i></a><br>{!! link_to_route('admin.quotes.index', 'back to quotes') !!}</h3>
		</div>
	</div>	
</section>
<section class="border_site_{!! $quote->site !!}">
	<div class="container">
		<div class="desktop-6 padding columns">
		    <h2 class="text condensed bold margin-bottom-small">QUOTE DETAILS</h2>
            <h3 class="text condensed"><b>Car Group:</b>  Group {!! $quote->listing_group !!}</h3>
            <h3 class="text condensed"><b>Car:</b> {!! $quote->title !!}</h3>
        	<h3 class="text condensed"><b>Dates:</b> {!! $quote->pickup_date->format('d M Y') !!} {!! substr($quote->pickup_time, 0, -3) !!} - {!! $quote->dropoff_date->format('d M Y') !!} {!! substr($quote->dropoff_time, 0, -3) !!}</h3>
        	<h3 class="text condensed"><b>From:</b> {!! $quote->getPickupLocation() !!} <b>To:</b> {!! $quote->getDropoffLocation() !!} </h3>
        	<h2 class="text condensed bold margin-top margin-bottom-small">CUSTOMER DETAILS</h2>
        	<h3 class="text condensed"><b>Name:</b> {!! $customer->name  !!}</h3>
        	<h3 class="text condensed"><b>email:</b> {!! $customer->email !!}</h3>
		</div>
		<div class="desktop-6 padding columns">
		    <h2 class="text condensed bold margin-bottom-small">PRICING DETAILS</h2>  
        	<h3 class="text condensed"><b>Total days:</b> {!! $quote->total_days !!}</h3>
        	<h2 class="text condensed bold margin-top margin-bottom-small">COMMENTS</h2>
            {!! Form::model($quote, array('method' => 'PUT', 'class' => 'main',  'route' => array('admin.quotes.update', $quote->id))) !!}
                <p><textarea name="comment">{!! $quote->comment !!}</textarea></p>
                {!! Form::hidden('listing_id', $quote->listing_id) !!}
                {!! Form::hidden('customer_name', $quote->getCustomer()->name) !!}
                {!! Form::hidden('customer_email', $quote->getCustomer()->email) !!}
                <section class="padding">
                    <div class="container">
                        <div class="desktop-4 columns">
                            <p>{!! Form::submit('Save', array('class' => 'button')) !!}</p>
                        </div>
                    </div>
                </section>
            {!! Form::close() !!}
		</div>
	</div>
</section>
@stop