@extends('layouts.admin.base')

@section('body')
<section class=" padding stripes">
	<div class="container">
		<div class="desktop-12 columns   "  >
			<h1 class="text bold condensed">Quotes</h1>
		</div>
		<div class="clear"></div>
		<div class="divider"></div>

		{!! Form::open(array('url' => URL::route('admin.quotes.index'), 'class'=>'main', 'method'=>'GET')) !!}
		<div class="desktop-2 offset-8 columns"  >
			<p>{!! Form::select('siteSelected', $sites, $siteSelected) !!}</p>
		</div>
		<div class="desktop-2 columns"  >
			<button type="submit" class="button button-small text  responsive">{!! Lang::get('forms.find_now') !!}</button>
		</div>
		{!! Form::close() !!}
		<div class="clear"></div>
    </div>
</section>
<!-- FORM
	================================================== -->
<section class="      ">
	<div class="container ">
		<div class="desktop-4 offset-8   columns   "  >
		</div>
    </div>
</section>
<section class=" padding   ">
		  @foreach ($quotes as $quote)
            <div class="container border_site_{!! $quote->site !!}">
            	<div class="bck white margin-bottom-small">
            		<div class="desktop-5 columns padding-top-small">
                        <span class="text grey">{!! $quote->created_at->format('d M Y') !!}</span>
            			<h2 class="text condensed bold">
                            {!! $quote->id !!}: {!! $quote->Title !!}
                        </h2>
            			<h3 class="text book">{!! $quote->getCustomer() ? $quote->getCustomer()->name : '' !!}</h3>
            			<h4 class="text margin-bottom"><span class="bold">Comment:</span> {!! $quote->comment !!} </h4>
            		</div>
            		<div class="desktop-7 columns">
            			<div class="padding-top-large box_model">
            				<h4 class="text"><b>{!! $quote->pickup_date->format('d M Y') !!}</b> {!! substr($quote->pickup_time, 0, -3)  !!} - <b>{!! $quote->dropoff_date->format('d M Y') !!}</b> {!! substr($quote->dropoff_time, 0, -3) !!} </h4>
            				<h4 class="text">{!! $quote->getPickupLocation() !!} - To: {!! $quote->getDropoffLocation() !!} </h4>
            			</div>
            		</div>
            		<div class="clear"></div>
            		<div class="desktop-5 columns padding-bottom-small">{!! link_to_route('admin.quotes.edit', 'EDIT QUOTE', $quote->id, array('class'=>'button button-small margin-top-small')) !!} </div>
            		<div class="desktop-7 columns text right">{!! Form::model($quote, ['method' => 'DELETE', 'route' => ['admin.quotes.destroy', $quote->id]]) !!}
            			{!! Form::submit('Delete', array('class'=>'button button-tiny button-delete alert-popup')) !!}
            			{!! Form::close() !!} </div>
            		<div class="clear"></div>
            	</div>
            </div>
		@endforeach
</section>
@stop
