footer {
    background-color: $color-red;
    color: #fff;
    padding: 2rem 0;
    h2, h3, h4, a{
        color: #fff;
    }

    .footer-links {
        display: flex;
        justify-content: space-around;
        border-bottom: 1px solid;
        padding-bottom: 2rem;
        .footer-col {
            h3 {
                margin-bottom: 1rem;
                color: $background-yellow;
            }
            .footer-menu {                
                list-style: none;
                display: flex;
                flex-direction: column;
                li {
                    a {
                        transition: color .2s;
                        font-size: 82%;
                        &:hover {
                            color: $yellow;
                        }
                    }
                }

               
            }
        }
    }
    .footer-contact {
        display: flex;
        align-items: start;
        justify-content: space-around;
        gap: 1rem;
        margin: 1.5rem 0 1rem;
        font-size: 82%;
        address {
            margin-top: 2rem;
        }
        .badges {
            display: flex;
            flex-direction: column;
            gap: 1rem;
            margin-top: 1.5rem;
            .eot {
                display: flex;
                gap: 1rem;
                align-items: center;
            }
        }
    }
    .footer-copyright {
        display: flex;
        justify-content: space-between;
        font-size: 75%;
        .copyright {
            margin-left: 3rem;
        }
        .terms {
            margin-right: 3rem;
        }
    }
}

/***************************/
/****** Media Queries ******/
/**************************/

/* width between 768px and 1020px */

@media only screen and (min-width: 769px) and (max-width: 1020px) {

    footer {
        .footer-contact {
            padding: 0 2rem;
        }
    }
    
}
/* Max width 768px */

@media only screen and (max-width: 768px) {
    footer {
        padding: 2rem 0 3rem;
        .footer-links {
            flex-wrap: wrap;
            padding: 0 1rem 2rem;
            justify-content: space-between;
            .footer-col {
                flex: 0 0 48%;
                margin-bottom: 2rem;
                padding: 0 0.5rem;
            }
        }
        .footer-contact {
            flex-wrap: wrap;
            padding: 1rem;
            .address {
                margin-top: 0;
            }
            .badges {
                .google {
                    display: block;
                    margin: auto;
                }
            }
        }
        .footer-copyright {
            justify-content: space-evenly;
            padding: 0 1rem;
            .copyright {
                margin-left: 0;
            }
            .terms {
                margin-right: 0;
            }
        }
    }
}

/* Width 560px - 1020px */

@media only screen and (min-width: 560px) and (max-width: 1020px) {
    
}

/* Width > 1020px */

@media only screen and (min-width: 1020px) and (max-width: 1320px) {
    
}