.select-location {
    position: relative;
    position: relative;
    display: flex;
    align-items: center;
    gap: 1rem;
    #location-select {
        border-radius: 24px;
        background-color: rgba(255, 255, 255, 0.9);
        padding: 0.2rem 1rem;
        border-style: groove;
    }
}

.loadmore {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 1rem;
    button {
        &:focus-visible {
            outline: none;
        }
    }
}