<?php

namespace Tests\Unit;

use App\LocalisedMotif;
use App\Post;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Database\Eloquent\Collection;
use Tests\TestCase;
use Database\Factories\LocalisedMotifFactory;

class LocalisedMotifTest extends TestCase
{
    use RefreshDatabase;

    /**
     * Test that a LocalisedMotif can be created with valid data.
     */
    public function test_localised_motif_can_be_created_with_valid_data(): void
    {
        $localisedMotif = LocalisedMotif::create([
            'title' => 'Test Location',
            'description' => 'A test location description'
        ]);

        $this->assertInstanceOf(LocalisedMotif::class, $localisedMotif);
        $this->assertEquals('Test Location', $localisedMotif->title);
        $this->assertEquals('A test location description', $localisedMotif->description);
        $this->assertNotNull($localisedMotif->id);
        $this->assertNotNull($localisedMotif->created_at);
        $this->assertNotNull($localisedMotif->updated_at);
    }

    /**
     * Test that LocalisedMotif has the correct fillable attributes.
     */
    public function test_localised_motif_has_correct_fillable_attributes(): void
    {
        $localisedMotif = new LocalisedMotif();
        $fillable = $localisedMotif->getFillable();

        $this->assertContains('title', $fillable);
        $this->assertContains('description', $fillable);
        $this->assertCount(2, $fillable);
    }

    /**
     * Test that LocalisedMotif uses the correct table name.
     */
    public function test_localised_motif_uses_correct_table_name(): void
    {
        $localisedMotif = new LocalisedMotif();
        $this->assertEquals('localised_motifs', $localisedMotif->getTable());
    }

    /**
     * Test that LocalisedMotif has timestamps enabled.
     */
    public function test_localised_motif_has_timestamps_enabled(): void
    {
        $localisedMotif = new LocalisedMotif();
        $this->assertTrue($localisedMotif->timestamps);
    }

    /**
     * Test that LocalisedMotif can be updated.
     */
    public function test_localised_motif_can_be_updated(): void
    {
        $localisedMotif = LocalisedMotif::create([
            'title' => 'Original Title',
            'description' => 'Original description'
        ]);

        $localisedMotif->update([
            'title' => 'Updated Title',
            'description' => 'Updated description'
        ]);

        $this->assertEquals('Updated Title', $localisedMotif->title);
        $this->assertEquals('Updated description', $localisedMotif->description);
    }

    /**
     * Test that LocalisedMotif can be deleted.
     */
    public function test_localised_motif_can_be_deleted(): void
    {
        $localisedMotif = LocalisedMotif::create([
            'title' => 'Test Location',
            'description' => 'A test location description'
        ]);

        $id = $localisedMotif->id;
        $localisedMotif->delete();

        $this->assertNull(LocalisedMotif::find($id));
    }

    /**
     * Test that LocalisedMotif has a posts relationship.
     */
    public function test_localised_motif_has_posts_relationship(): void
    {
        $localisedMotif = LocalisedMotif::create([
            'title' => 'Test Location',
            'description' => 'A test location description'
        ]);

        $this->assertInstanceOf(
            \Illuminate\Database\Eloquent\Relations\BelongsToMany::class,
            $localisedMotif->posts()
        );
    }

    /**
     * Test that LocalisedMotif can be associated with posts.
     */
    public function test_localised_motif_can_be_associated_with_posts(): void
    {
        $localisedMotif = LocalisedMotif::create([
            'title' => 'Test Location',
            'description' => 'A test location description'
        ]);

        // Create test posts
        $post1 = Post::create([
            'published' => true,
            'featured' => false
        ]);

        $post2 = Post::create([
            'published' => false,
            'featured' => true
        ]);

        // Associate posts with localised motif
        $localisedMotif->posts()->attach([$post1->id, $post2->id]);

        // Test the relationship
        $this->assertCount(2, $localisedMotif->posts);
        $this->assertTrue($localisedMotif->posts->contains($post1));
        $this->assertTrue($localisedMotif->posts->contains($post2));
    }

    /**
     * Test that LocalisedMotif publishedPosts relationship returns only published posts.
     */
    public function test_localised_motif_published_posts_returns_only_published_posts(): void
    {
        $localisedMotif = LocalisedMotif::create([
            'title' => 'Test Location',
            'description' => 'A test location description'
        ]);

        // Create test posts
        $publishedPost = Post::create([
            'published' => true,
            'featured' => false
        ]);

        $unpublishedPost = Post::create([
            'published' => false,
            'featured' => true
        ]);

        // Associate both posts with localised motif
        $localisedMotif->posts()->attach([$publishedPost->id, $unpublishedPost->id]);

        // Test publishedPosts relationship
        $publishedPosts = $localisedMotif->publishedPosts;

        $this->assertCount(1, $publishedPosts);
        $this->assertTrue($publishedPosts->contains($publishedPost));
        $this->assertFalse($publishedPosts->contains($unpublishedPost));
    }

    /**
     * Test that Post has a localisedMotifs relationship.
     */
    public function test_post_has_localised_motifs_relationship(): void
    {
        $post = Post::create([
            'published' => true,
            'featured' => false
        ]);

        $this->assertInstanceOf(
            \Illuminate\Database\Eloquent\Relations\BelongsToMany::class,
            $post->localisedMotifs()
        );
    }

    /**
     * Test that Post can be associated with localised motifs.
     */
    public function test_post_can_be_associated_with_localised_motifs(): void
    {
        $post = Post::create([
            'published' => true,
            'featured' => false
        ]);

        // Create test localised motifs
        $motif1 = LocalisedMotif::create([
            'title' => 'Heraklion',
            'description' => 'Capital of Crete'
        ]);

        $motif2 = LocalisedMotif::create([
            'title' => 'Chania',
            'description' => 'Beautiful coastal city'
        ]);

        // Associate localised motifs with post
        $post->localisedMotifs()->attach([$motif1->id, $motif2->id]);

        // Test the relationship
        $this->assertCount(2, $post->localisedMotifs);
        $this->assertTrue($post->localisedMotifs->contains($motif1));
        $this->assertTrue($post->localisedMotifs->contains($motif2));
    }

    /**
     * Test the many-to-many relationship works bidirectionally.
     */
    public function test_many_to_many_relationship_works_bidirectionally(): void
    {
        $localisedMotif = LocalisedMotif::create([
            'title' => 'Test Location',
            'description' => 'A test location description'
        ]);

        $post = Post::create([
            'published' => true,
            'featured' => false
        ]);

        // Attach from localised motif side
        $localisedMotif->posts()->attach($post->id);

        // Test both sides of the relationship
        $this->assertTrue($localisedMotif->posts->contains($post));
        $this->assertTrue($post->localisedMotifs->contains($localisedMotif));
    }

    /**
     * Test that the pivot table stores timestamps.
     */
    public function test_pivot_table_stores_timestamps(): void
    {
        $localisedMotif = LocalisedMotif::create([
            'title' => 'Test Location',
            'description' => 'A test location description'
        ]);

        $post = Post::create([
            'published' => true,
            'featured' => false
        ]);

        // Attach with timestamps
        $localisedMotif->posts()->attach($post->id);

        // Get the pivot data
        $pivotData = $localisedMotif->posts()->first()->pivot;

        $this->assertNotNull($pivotData->created_at);
        $this->assertNotNull($pivotData->updated_at);
    }

    /**
     * Test that relationships can be detached.
     */
    public function test_relationships_can_be_detached(): void
    {
        $localisedMotif = LocalisedMotif::create([
            'title' => 'Test Location',
            'description' => 'A test location description'
        ]);

        $post = Post::create([
            'published' => true,
            'featured' => false
        ]);

        // Attach and verify
        $localisedMotif->posts()->attach($post->id);
        $this->assertCount(1, $localisedMotif->posts);

        // Detach and verify
        $localisedMotif->posts()->detach($post->id);
        $this->assertCount(0, $localisedMotif->fresh()->posts);
    }

    /**
     * Test that multiple posts can be attached and detached.
     */
    public function test_multiple_posts_can_be_attached_and_detached(): void
    {
        $localisedMotif = LocalisedMotif::create([
            'title' => 'Test Location',
            'description' => 'A test location description'
        ]);

        $posts = collect();
        for ($i = 1; $i <= 3; $i++) {
            $posts->push(Post::create([
                'published' => true,
                'featured' => false
            ]));
        }

        // Attach multiple posts
        $localisedMotif->posts()->attach($posts->pluck('id')->toArray());
        $this->assertCount(3, $localisedMotif->posts);

        // Detach one post
        $localisedMotif->posts()->detach($posts->first()->id);
        $this->assertCount(2, $localisedMotif->fresh()->posts);

        // Detach all remaining posts
        $localisedMotif->posts()->detach();
        $this->assertCount(0, $localisedMotif->fresh()->posts);
    }

    /**
     * Test that the relationship uses the correct pivot table.
     */
    public function test_relationship_uses_correct_pivot_table(): void
    {
        $localisedMotif = new LocalisedMotif();
        $postsRelation = $localisedMotif->posts();

        $this->assertEquals('localised_motif_post', $postsRelation->getTable());
    }

    /**
     * Test that LocalisedMotif can be found by title.
     */
    public function test_localised_motif_can_be_found_by_title(): void
    {
        LocalisedMotif::create([
            'title' => 'Heraklion',
            'description' => 'Capital of Crete'
        ]);

        LocalisedMotif::create([
            'title' => 'Chania',
            'description' => 'Beautiful coastal city'
        ]);

        $heraklion = LocalisedMotif::where('title', 'Heraklion')->first();
        $chania = LocalisedMotif::where('title', 'Chania')->first();

        $this->assertNotNull($heraklion);
        $this->assertNotNull($chania);
        $this->assertEquals('Capital of Crete', $heraklion->description);
        $this->assertEquals('Beautiful coastal city', $chania->description);
    }

    /**
     * Test that LocalisedMotif collection methods work correctly.
     */
    public function test_localised_motif_collection_methods_work_correctly(): void
    {
        $locations = ['Heraklion', 'Chania', 'Rethymno', 'Agios Nikolaos'];

        foreach ($locations as $location) {
            LocalisedMotif::create([
                'title' => $location,
                'description' => "Description for {$location}"
            ]);
        }

        $allMotifs = LocalisedMotif::all();
        $this->assertCount(4, $allMotifs);

        $airportMotifs = LocalisedMotif::where('title', 'like', '%Airport%')->get();
        $this->assertCount(0, $airportMotifs);

        $heraklionMotifs = LocalisedMotif::where('title', 'like', 'Heraklion%')->get();
        $this->assertCount(1, $heraklionMotifs);
    }

    /**
     * Test LocalisedMotif factory creates valid instances.
     */
    public function test_localised_motif_factory_creates_valid_instances(): void
    {
        $localisedMotif = LocalisedMotif::factory()->create();

        $this->assertInstanceOf(LocalisedMotif::class, $localisedMotif);
        $this->assertNotEmpty($localisedMotif->title);
        $this->assertNotEmpty($localisedMotif->description);
        $this->assertNotNull($localisedMotif->id);
    }

    /**
     * Test LocalisedMotif factory states work correctly.
     */
    public function test_localised_motif_factory_states_work_correctly(): void
    {
        $heraklion = LocalisedMotif::factory()->heraklion()->create();
        $chania = LocalisedMotif::factory()->chania()->create();
        $airport = LocalisedMotif::factory()->airport()->create();
        $coastal = LocalisedMotif::factory()->coastal()->create();

        $this->assertEquals('Heraklion', $heraklion->title);
        $this->assertEquals('Chania', $chania->title);
        $this->assertStringContainsString('Airport', $airport->title);
        $this->assertNotEmpty($coastal->title);
    }

    /**
     * Test complex relationship scenarios with multiple posts and motifs.
     */
    public function test_complex_relationship_scenarios(): void
    {
        // Create multiple localised motifs
        $heraklion = LocalisedMotif::factory()->heraklion()->create();
        $chania = LocalisedMotif::factory()->chania()->create();
        $airport = LocalisedMotif::factory()->airport()->create();

        // Create multiple posts
        $publishedPost1 = Post::create(['published' => true, 'featured' => false]);
        $publishedPost2 = Post::create(['published' => true, 'featured' => true]);
        $unpublishedPost = Post::create(['published' => false, 'featured' => false]);

        // Create complex relationships
        $heraklion->posts()->attach([$publishedPost1->id, $unpublishedPost->id]);
        $chania->posts()->attach([$publishedPost1->id, $publishedPost2->id]);
        $airport->posts()->attach([$publishedPost2->id, $unpublishedPost->id]);

        // Test that posts can belong to multiple motifs
        $this->assertCount(2, $publishedPost1->localisedMotifs);
        $this->assertCount(2, $publishedPost2->localisedMotifs);
        $this->assertCount(2, $unpublishedPost->localisedMotifs);

        // Test published posts filtering
        $this->assertCount(1, $heraklion->publishedPosts);
        $this->assertCount(2, $chania->publishedPosts);
        $this->assertCount(1, $airport->publishedPosts);

        // Test that the correct posts are returned
        $this->assertTrue($heraklion->publishedPosts->contains($publishedPost1));
        $this->assertFalse($heraklion->publishedPosts->contains($unpublishedPost));
    }

    /**
     * Test that cascade delete works correctly.
     */
    public function test_cascade_delete_works_correctly(): void
    {
        $localisedMotif = LocalisedMotif::factory()->create();
        $post = Post::create(['published' => true, 'featured' => false]);

        // Create relationship
        $localisedMotif->posts()->attach($post->id);

        // Verify relationship exists
        $this->assertCount(1, $localisedMotif->posts);
        $this->assertCount(1, $post->localisedMotifs);

        // Delete localised motif
        $localisedMotif->delete();

        // Verify post still exists but relationship is gone
        $this->assertNotNull(Post::find($post->id));
        $this->assertCount(0, $post->fresh()->localisedMotifs);
    }

    /**
     * Test performance with large number of relationships.
     */
    public function test_performance_with_large_number_of_relationships(): void
    {
        $localisedMotif = LocalisedMotif::factory()->create();

        // Create multiple posts
        $posts = collect();
        for ($i = 0; $i < 10; $i++) {
            $posts->push(Post::create([
                'published' => $i % 2 === 0, // Half published, half not
                'featured' => $i % 3 === 0   // Every third is featured
            ]));
        }

        // Attach all posts
        $localisedMotif->posts()->attach($posts->pluck('id')->toArray());

        // Test counts
        $this->assertCount(10, $localisedMotif->posts);
        $this->assertCount(5, $localisedMotif->publishedPosts); // Half are published

        // Test that we can efficiently query the relationships
        $publishedCount = $localisedMotif->posts()->where('published', true)->count();
        $this->assertEquals(5, $publishedCount);
    }
}
