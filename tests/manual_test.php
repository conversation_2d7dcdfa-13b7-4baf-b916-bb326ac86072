<?php

// Simple manual test for the average price per day calculation
$revenue = 1800;
$totalDays = 14;
$avgPricePerDay = $revenue / $totalDays;

echo "Testing average price per day calculation:\n";
echo "Revenue: $revenue\n";
echo "Total Days: $totalDays\n";
echo "Average Price Per Day: $avgPricePerDay\n";

// Check if the calculation is correct
$expectedAvgPricePerDay = 128.57142857142858;
$isCorrect = abs($avgPricePerDay - $expectedAvgPricePerDay) < 0.0001;

echo "Expected: $expectedAvgPricePerDay\n";
echo "Test Result: " . ($isCorrect ? "PASSED" : "FAILED") . "\n";
