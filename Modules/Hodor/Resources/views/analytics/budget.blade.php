@extends('hodor::layouts.master')

@section('content_header')
    <div class="row mb-2">
        <div class="col">
            <h1>{{ $page_title }}</h1>
        </div>
        <div class="col-4 col-sm-3 col-md-2">
            @include('hodor::analytics._partials.form')
        </div>
    </div>
@stop

@section('content')
    @include('hodor::analytics._partials.counts')
    @include('hodor::analytics._partials.analytics_graphs', ['selectedDates' => 'Pickup'])
    @include('hodor::analytics._partials.analytics_graphs', ['selectedDates' => 'Created'])
    @include('hodor::analytics._partials.tables')
@endsection

@section('css')
    <link rel="stylesheet" href="/vendor/jqvmap/jqvmap.min.css">
@stop

@section('js')
    <script>
        let tableData={!! json_encode($analytics_data['tables']) !!};
        let graphData={!! json_encode($analytics_data['graphs']) !!};
    </script>
    <script src="/vendor/jqvmap/jquery.vmap.min.js"></script>
    <script src="/vendor/jqvmap/maps/jquery.vmap.world.js"></script>
    <script src="/js/admin_analytics.js?v={!! env('APP_SCRIPTS_VERSION', '1') !!}"></script>
@stop
