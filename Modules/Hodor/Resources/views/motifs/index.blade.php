@extends('hodor::layouts.master')

@section('content')
<section class="content-header">
    <div class="container-fluid">
        <div class="row">
            <div class="col-sm-6"></div>
            <div class="col-sm-6">
                <div class="float-right">
                    <a href="{{ route('hodor.motifs.create') }}" class="btn btn-primary"><i class="fas fa-plus"></i> Motif</a>
                </div>
            </div>
        </div>
    </div>
</section>
<section class="content">
    <div class="container-fluid">
        <div class="row">
            @include('hodor::common.alert')
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <div>{{ $motifs->count() }} of {{ $motifs->total() }} items</div>
                    </div>
                    <div class="card-body table-responsive p-0">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Title</th>
                                    <th>Posts (pub.)</th>
                                    <th>Listings (pub.)</th>
                                    <th></th>
                                </tr>
                            </thead>
                            <tbody>
                            @foreach ($motifs as $motif)
                                <tr>
                                    <td><strong><a href="{{ route('hodor.motifs.edit', $motif->id) }}">{{ $motif->id }}</a></strong></td>
                                    <td>{{ $motif->title }}</td>
                                    <td>@if($motif->posts()->exists()) {{ $motif->posts()->count() }} ({{ $motif->publishedPosts()->count() }}) @else - @endif</td>
                                    <td>@if($motif->listings()->exists()) {{ $motif->listings()->count() }} ({{ $motif->publishedListings()->count() }}) @else - @endif</td>
                                    <td class="text-right">
                                        <a class="btn btn-outline-info" title="Edit" href="{{ route('hodor.motifs.edit', $motif->id) }}">
                                            <i class="fas fa-pen"></i>
                                        </a>
                                        {!! Form::open(array('method' => 'DELETE', 'class' => 'btn', 'route' => array('hodor.motifs.destroy', $motif->id))) !!}
                                            {!! Form::token() !!}
                                            <button class="btn btn-outline-danger" title="Delete" onclick="return confirm('Are you sure you want to delete this item?');"><i class="fas fa-trash"></i></button>
                                        {!! Form::close() !!}
                                    </td>
                                </tr>
                            @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div>{{ $motifs->links() }}</div>
</section>
@endsection
