@extends('hodor::layouts.master')

@section('content')
<section class="content">
    <div class="container-fluid">
        <div class="row">
            <div class="col text-right mb-3">
                @if($photo->model instanceof \App\Post)
                    <a href="{{ route('hodor.posts.photos.index', $photo->model->id) }}">Photos of post: {{ $photo->model->id }}</a>
                @endif
            </div>
        </div>
        <div class="card card-default">
            <div class="card-body">
                {{ $photo('thumb') }}
            </div>
        </div>
        <div class="card card-default">
            @include('hodor::common.alert')
            {!! Form::model($photo, array('method' => 'PUT', 'files' => false, 'class' => 'main', 'route' => array('hodor.photos.update', $photo->id))) !!}
            {!! Form::token() !!}
                <div class="card-body">
                    @include('hodor::photos._form')
                </div>
                <div class="card-footer">
                    <button type="submit" class="col-md-4 btn btn-primary">Submit</button>
                </div>
            {!! Form::close() !!}
        </div>
    </div>
</section>
@endsection