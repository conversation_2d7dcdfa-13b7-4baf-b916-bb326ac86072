<style>
    .language-section {
        margin-bottom: 25px;
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }
    .language-header {
        background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
        color: white;
        padding: 15px 20px;
        border-radius: 8px 8px 0 0;
        border-left: 5px solid #ffc107;
        position: relative;
    }
    .language-header::before {
        content: '';
        position: absolute;
        top: 0;
        right: 0;
        width: 0;
        height: 0;
        border-style: solid;
        border-width: 0 20px 20px 0;
        border-color: transparent #ffc107 transparent transparent;
    }
    .language-header h3 {
        margin: 0;
        font-weight: 600;
        font-size: 1.2rem;
        text-shadow: 1px 1px 2px rgba(0,0,0,0.2);
    }
    .language-body {
        background: #f8f9fa;
        padding: 25px;
        border-radius: 0 0 8px 8px;
        border: 1px solid #dee2e6;
        border-top: none;
    }
    .language-section:nth-child(odd) .language-header {
        background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%);
        border-left-color: #fd7e14;
    }
    .language-section:nth-child(odd) .language-header::before {
        border-color: transparent #fd7e14 transparent transparent;
    }
    .language-section:nth-child(3n) .language-header {
        background: linear-gradient(135deg, #dc3545 0%, #bd2130 100%);
        border-left-color: #20c997;
    }
    .language-section:nth-child(3n) .language-header::before {
        border-color: transparent #20c997 transparent transparent;
    }
    .language-section:nth-child(4n) .language-header {
        background: linear-gradient(135deg, #6f42c1 0%, #59359a 100%);
        border-left-color: #e83e8c;
    }
    .language-section:nth-child(4n) .language-header::before {
        border-color: transparent #e83e8c transparent transparent;
    }
</style>

<div class="row">
    @foreach ($languages as $locale)
        <div class="col-md-6">
            <div class="card language-section">
                <div class="card-header language-header">
                    <h3 class="card-title">🌐 {{ strtoupper($locale) }} Texts</h3>
                </div>
                <div class="card-body language-body">
                    <div class="form-group">
                        <label for="description-{{ $locale }}">
                            Description ({{ strtoupper($locale) }})*
                        </label>
                        {!! Form::text($locale . '[description]', $group->translate($locale)->description ?? '', ['id' => 'description-' . $locale . '-' . $group->id, 'class' => 'form-control' . ($errors->has($locale . '.description') ? ' is-invalid' : null)]) !!}
                        @if($errors->has($locale . '.description'))
                            <div class="invalid-feedback">{{ $errors->first($locale . '.description') }}</div>
                        @endif
                        @include('hodor::common._translationSuggestCta', ['model' => $group, 'field' => 'description', 'locale' => $locale, 'wysiwyg' => 'no'])
                    </div>

                    <div class="form-group">
                        <label for="description_cr-{{ $locale }}">
                            Description for CretanRentals ({{ strtoupper($locale) }})
                        </label>
                        {!! Form::text($locale . '[description_cr]', $group->translate($locale)->description_cr ?? '', ['id' => 'description_cr-' . $locale . '-' . $group->id, 'class' => 'form-control']) !!}
                        @include('hodor::common._translationSuggestCta', ['model' => $group, 'field' => 'description_cr', 'locale' => $locale, 'wysiwyg' => 'no'])
                    </div>

                    <div class="form-group">
                        <label for="seo_text-{{ $locale }}">
                            SEO Text ({{ strtoupper($locale) }})
                        </label>
                        {!! Form::textarea($locale . '[seo_text]', $group->translate($locale)->seo_text ?? '', ['id' => 'seo_text-' . $locale . '-' . $group->id, 'class' => 'form-control seo_text', 'rows' => 6]) !!}
                        @include('hodor::common._translationSuggestCta', ['model' => $group, 'field' => 'seo_text', 'locale' => $locale, 'wysiwyg' => 'yes'])
                    </div>

                    <div class="form-group">
                        <label for="seo_text_cr-{{ $locale }}">
                            SEO Text for CretanRentals ({{ strtoupper($locale) }})
                        </label>
                        {!! Form::textarea($locale . '[seo_text_cr]', $group->translate($locale)->seo_text_cr ?? '', ['id' => 'seo_text_cr-' . $locale . '-' . $group->id, 'class' => 'form-control seo_text', 'rows' => 6]) !!}
                        @include('hodor::common._translationSuggestCta', ['model' => $group, 'field' => 'seo_text_cr', 'locale' => $locale, 'wysiwyg' => 'yes'])
                    </div>
                </div>
            </div>
        </div>
        @if($loop->iteration % 2 == 0)
            </div><div class="row">
        @endif
    @endforeach
</div>

@section('js')
<script src="{{ asset('packages/ckeditor/ckeditor.js') }}"></script>
<script>
$(document).ready(function() {
    // Initialize CKEditor for SEO text areas
    $('.seo_text').each(function() {
        CKEDITOR.replace(this.id || this.name);
    });
});
</script>
@stop
