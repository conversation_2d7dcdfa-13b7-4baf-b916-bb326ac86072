<?php

namespace Modules\Hodor\Http\Requests;

use App\Http\Requests\Request;

class GroupBasePriceUpdateRequest extends Request
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'newValue' => 'required|numeric|min:0',
            'site' => 'required|in:eurodollar,cretanrentals',
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array
     */
    public function messages()
    {
        return [
            'newValue.required' => 'The price value is required.',
            'newValue.numeric' => 'The price value must be a number.',
            'newValue.min' => 'The price value must be at least 0.',
            'site.required' => 'The site parameter is required.',
            'site.in' => 'The site must be either eurodollar or cretanrentals.',
        ];
    }
}
