<?php

namespace Modules\Hodor\Http\Controllers;

use App\Group;
use App\Supergroup;
use Illuminate\Contracts\Support\Renderable;
use Illuminate\Http\Request;
use Modules\Hodor\Http\Requests\SupergroupRequest;
use Modules\Hodor\Http\Requests\SupergroupUpdateRequest;

class SupergroupsController extends HodorController
{
    protected $languages;

    public function __construct()
    {
        // languages
        foreach (config('laravellocalization.supportedLocales') as $langKey => $langData){
            $this->languages[$langKey] = $langData['name'];
        }
    }

    /**
     * Display a listing of the resource.
     * @return Renderable
     */
    public function index()
    {
        $supergroups = Supergroup::orderBy('created_at', 'desc')->paginate(20);

        $this->view_data['supergroups'] = $supergroups;
        $this->view_data['page_title']  = 'Supergroups';

        return view('hodor::supergroups.index', $this->view_data);
    }

    /**
     * Show the form for creating a new resource.
     * @return Renderable
     */
    public function create()
    {
        $this->view_data['page_title']  = 'Create New Supergroup';

        return view('hodor::supergroups.create', $this->view_data);
    }

    /**
     * Store a newly created resource in storage.
     * @param Request $request
     * @return Renderable
     */
    public function store(SupergroupRequest $request)
    {
        // having passed validation, we also fill all the title attributes in all languages
        // ...with the name given
        $data = [
            'name' => $request['name'],
            'en' => ['title' => $request['name']],
            'fr' => ['title' => $request['name']],
            'de' => ['title' => $request['name']],
            'it' => ['title' => $request['name']],
            'el' => ['title' => $request['name']],
        ];

        $supergroup = Supergroup::create($data);

        return redirect()->route('hodor.supergroups.edit',  $supergroup->id)
            ->withSuccess('Entity with id: ' . $supergroup->id . ' was successfully saved!');
    }

    /**
     * Show the form for editing the specified resource.
     * @param int $id
     * @return Renderable
     */
    public function edit($id)
    {
        $supergroup = Supergroup::findOrFail($id);

        $this->view_data['supergroup']  = $supergroup;
        $this->view_data['page_title']  = 'Editing supergroup: ' . $supergroup->name;

        // languages
        $this->view_data['languages'] = $this->languages;

        return view('hodor::supergroups.edit', $this->view_data);
    }

    /**
     * Update the specified resource in storage.
     * @param SupergroupUpdateRequest $request
     * @param int $id
     * @return Renderable
     */
    public function update(SupergroupUpdateRequest $request, $id)
    {
        $supergroup = Supergroup::findOrFail($id);

        $supergroup->update($request->validated());

        return redirect()->route('hodor.supergroups.edit',  $supergroup->id)
            ->withSuccess('Entity with id: ' . $supergroup->id . ' was successfully updated!');
    }

    /**
     * Remove the specified resource from storage.
     * @param int $id
     * @return Renderable
     */
    public function destroy($id)
    {
        $deleteItem =  Supergroup::find($id);

        if ($deleteItem->delete()) {
            return redirect()->route('hodor.supergroups.index')
                ->withSuccess('Entity with id: ' . $id . ' was successfully deleted!');
        } else {
            return redirect()->route('hodor.supergroups.index')
                ->withError('msg', 'An error occured!');
        }
    }
}
