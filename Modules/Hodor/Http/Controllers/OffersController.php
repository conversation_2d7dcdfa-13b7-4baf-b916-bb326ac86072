<?php

namespace Modules\Hodor\Http\Controllers;

use App\Accessory;
use App\Country;
use App\Customer;
use App\Jobs\SendOfferNotification;
use App\Listing;
use App\Location;
use App\Offer;
use App\Services\Search\OfferSearch;
use Illuminate\Contracts\Support\Renderable;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Lang;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Modules\CretanRentals\Util\CretanDefaultValues;
use Modules\Hodor\Http\Requests\OfferSearchRequest;
use Modules\Hodor\Http\Requests\OfferStoreRequest;
use Modules\Hodor\Http\Requests\OfferUpdateRequest;

class OffersController extends HodorController
{
    /**
     * Display a listing of the resource.
     * @return Renderable
     */
    public function index(OfferSearchRequest $request)
    {
        $this->view_data['offers']      = OfferSearch::apply($request);
        $this->view_data['page_title']  = 'Commercial Offers';

        return view('hodor::offers.index', $this->view_data);
    }

    /**
     * Show the form for creating a new resource.
     * @return Renderable
     */
    public function create()
    {
        $this->view_data['page_title']  = 'Create New Commercial Offer';

        return view('hodor::offers.create', $this->view_data);
    }

    /**
     * Store a newly created resource in storage.
     * @param Request $request
     * @return Renderable
     */
    public function store(OfferStoreRequest $request)
    {
        $offer = Offer::create($request->validated());

        // and set the slug
        $offer->slug = (string) Str::orderedUuid();
        $offer->save();

        return redirect()->route('hodor.offers.edit',  $offer->id)
            ->withSuccess('Entity with id: ' . $offer->id . ' was successfully created!');
    }

    /**
     * Show the form for editing the specified resource.
     * @param int $id
     * @return Renderable
     */
    public function edit($id)
    {
        $offer = Offer::findOrFail($id);

        $this->view_data['offer']           = $offer;
        $this->view_data['page_title']      = 'Offer: ' . $id;
        // initialize listings_list without empty value
        $this->view_data['listings_list']   = Listing::orderBy('id', 'asc')->publishedEurodollar()->get()->pluck('longTitle', 'id')->toArray();
        $this->view_data['locations_list']  = Location::all()->pluck('name', 'id')->all();
        $this->view_data['times_list']      = CretanDefaultValues::pickupDropoffHours();
        $this->view_data['accessories']     = Accessory::all()->pluck('name', 'id');
        $this->view_data['countries_list']  = Country::all()->pluck('name', 'id')->toArray();
        asort($this->view_data['countries_list']);
        $this->view_data['countries_list']  = array(Lang::get('forms.select_country')) + $this->view_data['countries_list'];

        return view('hodor::offers.edit', $this->view_data);
    }

    /**
     * Update the specified resource in storage.
     * @param Request $request
     * @param int $id
     * @return Renderable
     */
    public function update(OfferUpdateRequest $request, $id)
    {
        $offer = Offer::findOrFail($id);

        $offer->update($request->validated());

        // sync accessories
        $offer->accessories()->sync($request->input('accessories'));

        // in case there already is a customer related to the offer, we just update them
        if($offer->customer()->exists())
        {
            $offer->customer->name          = $request->input('customer_name');
            $offer->customer->email         = $request->input('customer_email');
            $offer->customer->address       = $request->input('customer_address');
            $offer->customer->telephone     = $request->input('customer_telephone');
            // since we made country_id non madatory we needs checks prior to persisting
            if(Country::where('id', '=', $request->input('customer_country_id'))->exists())
            {
                $offer->customer->country_id = $request->input('customer_country_id');
            }

            $offer->customer->save();
        }
        else
        {
            $customer = new Customer([
                'name'          => $request->input('customer_name'),
                'email'         => $request->input('customer_email'),
                'address'       => $request->input('customer_address'),
                'telephone'     => $request->input('customer_telephone'),
                'country_id'    => Country::where('id', '=', $request->input('customer_country_id'))->exists() ?
                    $request->input('customer_country_id')
                    : null,
                'site'          => 1,
            ]);

            $customer->save();

            $offer->customer()->associate($customer);

            $offer->save();
        }

        return redirect()->route('hodor.offers.edit',  $offer->id)
            ->withSuccess('Entity with id: ' . $offer->id . ' was successfully updated!');
    }

    /**
     * Update the specified resource in storage with a new slug.
     * @param Request $request
     * @param int $id
     * @return Renderable
     */
    public function slugify(Request $request, $id)
    {
        $offer = Offer::findOrFail($id);

        $offer->slug = (string) Str::orderedUuid();
        $offer->save();

        Log::info('Offer with id: ' . $offer->id . ' was slugified through Hodor');

        return redirect()->back()
            ->withSuccess('Entity with id: ' . $offer->id . ' was successfully slugified!');
    }

    /**
     * Send the offer email to the customer  fer the specified resource
     * @param Request $request
     * @param int $id
     * @return Renderable
     */
    public function notify(Request $request, $id)
    {
        $offer = Offer::findOrFail($id);

        dispatch(new SendOfferNotification($offer));

        return redirect()->back()
            ->withSuccess('Entity with id: ' . $offer->id . ' was successfully sent to customer!');
    }

    /**
     * Remove the specified resource from storage.
     * @param int $id
     * @return Renderable
     */
    public function destroy($id)
    {
        $deleteItem =  Offer::find($id);

        if ($deleteItem->delete()) {
            return redirect()->route('hodor.motifs.index')
                ->withSuccess('Entity with id: ' . $id . ' was successfully deleted!');
        } else {
            return redirect()->route('hodor.offers.index')
                ->with('error', 'An error occured!');
        }

    }
}
