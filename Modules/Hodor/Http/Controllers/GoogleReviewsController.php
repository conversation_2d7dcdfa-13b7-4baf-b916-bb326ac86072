<?php

namespace Modules\Hodor\Http\Controllers;

use App\GoogleReview;
use App\Location;
use App\Services\Search\GoogleReviewSearch;
use Illuminate\Contracts\Support\Renderable;
use Illuminate\Http\Request;
use Modules\Hodor\Http\Requests\GoogleReviewSearchRequest;
use Modules\Hodor\Http\Requests\GoogleReviewTextUpdateRequest;
use Modules\Hodor\Http\Requests\GoogleReviewUpdateRequest;

class GoogleReviewsController extends HodorController
{
    /**
     * @var $languages
     */
    private $languages;

    public function __construct()
    {
        // languages
        foreach (config('laravellocalization.supportedLocales') as $langKey => $langData){
            $this->languages[$langKey] = $langData['name'];
        }
    }

    /**
     * Display a listing of the resource.
     * @return Renderable
     */
    public function index(GoogleReviewSearchRequest $request)
    {
        $this->view_data['reviews']     = GoogleReviewSearch::apply($request);
        $this->view_data['page_title']  = 'Google Reviews';
        $this->view_data['locations']   = Location::all();

        return view('hodor::google_reviews.index', $this->view_data);
    }

    /**
     * Show the form for editing the specified resource.
     * @param int $id
     * @return Renderable
     */
    public function edit($id)
    {
        $review = GoogleReview::findOrFail($id);

        $this->view_data['review']      = $review;
        $this->view_data['page_title']  = 'Google review: ' . $id;
        $this->view_data['locations']   = Location::pluck('name', 'id');

        return view('hodor::google_reviews.edit', $this->view_data);
    }

    /**
     * Update the specified resource in storage.
     * @param Request $request
     * @param int $id
     * @return Renderable
     */
    public function update(GoogleReviewUpdateRequest $request, $id)
    {
        $review = GoogleReview::findOrFail($id);

        $review->update($request->validated());

        // we want to populate the english content translation with the translated_text attribute
        if( !empty($review->translated_text) )
        {
            // hodor is run in en locale by default
            $review->content = $review->translated_text;
            $review->save();
        }

        return redirect()->route('hodor.google-reviews.edit',  $review->id)
            ->withSuccess('Entity with id: ' . $review->id . ' was successfully saved!');
    }

    /**
     * Show the form for editing the specified resource.
     * @param int $id
     * @return Renderable
     */
    public function textEdit(int $id)
    {
        $review = GoogleReview::findOrFail($id);

        $this->view_data['review']      = $review;
        $this->view_data['page_title']  = 'Google review ' . $review->id . ': Edit texts';

        // languages
        $this->view_data['languages'] = $this->languages;

        return view('hodor::google_reviews.edit_texts', $this->view_data);
    }

    /**
     * Update the specified resource in storage.
     * @param GoogleReviewTextUpdateRequest $request
     * @param int $id
     * @return Renderable
     */
    public function textUpdate(GoogleReviewTextUpdateRequest $request, $id)
    {
        $review = GoogleReview::findOrFail($id);

        $review->update($request->validated());

        // hack to bypass the issue of updating the updated_at filed of the post on translation change
        $review->touch();

        return redirect()->route('hodor.google-reviews.texts.edit',  $review->id)
            ->withSuccess('The texts of entity with id: ' . $review->id . ' successfully updated!');
    }

    /**
     * Remove the specified resource from storage.
     * @param int $id
     * @return Renderable
     */
    public function destroy($id)
    {
        //
    }
}
